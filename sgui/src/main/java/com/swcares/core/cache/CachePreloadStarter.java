package com.swcares.core.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 缓存预加载启动器
 * 在应用启动时自动执行所有缓存预加载器
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Slf4j
@Component
public class CachePreloadStarter implements ApplicationListener<ContextRefreshedEvent> {
    
    @Resource
    private List<CachePreloader> cachePreloaders;
    
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 确保只在根上下文刷新时执行一次
        if (event.getApplicationContext().getParent() == null) {
            log.info("开始执行缓存预加载...");
            
            // 按优先级排序
            cachePreloaders.sort(Comparator.comparingInt(CachePreloader::getOrder));
            
            // 异步执行预加载，避免延长应用启动时间
            CompletableFuture.runAsync(() -> {
                for (CachePreloader preloader : cachePreloaders) {
                    log.info("开始预加载缓存: {}", preloader.getName());
                    long startTime = System.currentTimeMillis();
                    
                    try {
                        preloader.preloadCache(null);
                        log.info("缓存预加载完成: {}, 耗时: {}ms", 
                            preloader.getName(), System.currentTimeMillis() - startTime);
                    } catch (Exception e) {
                        log.error("缓存预加载失败: {}", preloader.getName(), e);
                    }
                }
                
                log.info("所有缓存预加载任务完成");
            });
        }
    }
}
