package com.swcares.core.unified;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 全局统一异常处理器
 *
 * <AUTHOR>
 * @date 2025/4/18 11:30
 */
@Slf4j
@ControllerAdvice
@ResponseBody
public class SguiExceptionHandler {

    private static final Pattern PAT_FIELD_MODEL = Pattern.compile("parse(\\w*) error, field : (\\w*)");

    /**
     * 捕捉所有的异常，
     * 在业务逻辑中dao、service、controller都不处理异常
     *
     * @param exp 异常数据
     * @return 返回包装后的异常数据
     */
    @ExceptionHandler(Exception.class)
    public SguiResult handleException(Exception exp) {
        // 业务异常
        if (exp instanceof SguiResultException || exp instanceof UnifiedResultException) {
            exp.printStackTrace();
            // 日志记录
            log.error(exp.getMessage());
            return SguiResult.fail(exp.getMessage());
        } else if (exp instanceof HttpMessageNotReadableException) {
            HttpMessageNotReadableException httpMessageNotReadableException = (HttpMessageNotReadableException) exp;
            String msg = httpMessageNotReadableException.getMessage();
            List<String> args = ReUtil.getAllGroups(PAT_FIELD_MODEL, msg, false);
            // 这个地方还要优化，目前这个地方的数据类型只是基本数据类型，复合数据类型没有兼容
            String rt;
            if (CollUtil.isNotEmpty(args)) {
                String fieldName = args.get(1);
                rt = StrUtil.format("{}输入值的类型错误", fieldName);
            } else {
                rt = "参数存在错误，请核实";
            }
            return SguiResult.fail(rt);
        } else if (exp instanceof MethodArgumentNotValidException) {
            // 字段校验问题
            MethodArgumentNotValidException methodArgumentNotValidException = (MethodArgumentNotValidException) exp;
            BindingResult bindingResult = methodArgumentNotValidException.getBindingResult();
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            List<String> errors = new ArrayList<>();
            fieldErrors.forEach(fieldError -> {
                String rt = StrUtil.format("({}):{}", fieldError.getField(), fieldError.getDefaultMessage());
                errors.add(rt);
            });
            return SguiResult.fail(CollUtil.join(errors, StrUtil.COMMA));
        } else {
            // 控制台打印
            exp.printStackTrace();
            // 日志记录
            log.error(exp.getMessage());
            // 程序异常
            return SguiResult.fail("SGUI-0000-00", "系统异常，请联系中国航信");
        }
    }
}
