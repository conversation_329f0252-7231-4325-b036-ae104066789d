package com.swcares.service.bkc.impl;

import com.swcares.core.constants.AuthCacheConstants;
import com.swcares.service.bkc.IVerifyCodeService;
import com.wf.captcha.SpecCaptcha;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:11
 */
@Service
public class VerifyCodeServiceImpl implements IVerifyCodeService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response, double rdm) throws IOException {
        // 生成验证码，设置验证码图片的宽高
        SpecCaptcha captcha = new SpecCaptcha(130, 48);
        // 验证码长度
        captcha.setLen(4);
        // 获取验证码
        String code = captcha.text();
        // 存储验证码到redis
        boolean hasSguiSessionIdCookie = false;
        boolean hasEff4Cookie = false;
        boolean has4aecCookie = false;
        // 查找cookie
        Cookie[] cookies = request.getCookies();
        String sguiSessionId = "";
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("sguiSessionId".equals(cookie.getName())) {
                    hasSguiSessionIdCookie = true;
                    sguiSessionId = cookie.getValue();
                }
                if ("4aec126d2c1b2c170b0016878393233b".equals(cookie.getName())) {
                    has4aecCookie = true;
                }
                if ("eff4ac174bb68d85306d2568930cf81c".equals(cookie.getName())) {
                    hasEff4Cookie = true;
                }
            }
        }
        String captchaKey;
        // 如果有sguiSessionId的cookie，直接使用当前cookie值作为验证码的key
        if (hasSguiSessionIdCookie) {
            captchaKey = sguiSessionId;
        }
        // 没有则表示当前第一次打开会话，需要新建一个sguiSessionId的cookie，返回到响应头
        else {
            captchaKey = UUID.randomUUID().toString();
            // 构建全局Cookie
            ResponseCookie sguidCookie = ResponseCookie.from("sguiSessionId", captchaKey)
                    // 启用HttpOnly
                    .httpOnly(true)
                    // 全局生效路径
                    .path("/")
                    // 跨站策略
                    .sameSite("Lax")
                    .build();

            // 添加Cookie到响应头
            response.addHeader("Set-Cookie", sguidCookie.toString());
        }
        if (!hasEff4Cookie) {
            ResponseCookie eff4Cookie = ResponseCookie.from("eff4ac174bb68d85306d2568930cf81c", "10f0b99146c70480bf1bfb70ad949c41")
                    // 启用HttpOnly
                    .httpOnly(true)
                    // 全局生效路径
                    .path("/")
                    .build();
            // 添加Cookie到响应头
            response.addHeader("Set-Cookie", eff4Cookie.toString());
        }
        if (!has4aecCookie) {
            ResponseCookie eff4Cookie = ResponseCookie.from("4aec126d2c1b2c170b0016878393233b", "17fe7ba6fc97cb077c29903064650043")
                    // 启用HttpOnly
                    .httpOnly(true)
                    // 全局生效路径
                    .path("/")
                    .build();
            // 添加Cookie到响应头
            response.addHeader("Set-Cookie", eff4Cookie.toString());
        }
        stringRedisTemplate.opsForValue().set(
                AuthCacheConstants.CAPTCHA + captchaKey,
                code,
                5,
                TimeUnit.MINUTES
        );
        // 返回图片流和key
        response.setContentType("image/png");
        captcha.out(response.getOutputStream());
    }
}
