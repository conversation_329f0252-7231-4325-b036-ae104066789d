package com.swcares.service.bkc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.IssueTicketDto;
import com.swcares.obj.vo.IssueTicketVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IIssueService;
import com.swcares.service.bkc.IUpdatePnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 出票服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/29 15:00
 */
@Slf4j
@Service
public class IssueServiceImpl implements IIssueService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IssueTicketVo issueTicket(IssueTicketDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        if (dto.getIssue() == null || StrUtil.isEmpty(dto.getIssue().getPrinterNo())) {
            throw new SguiResultException("打票机编号不能为空");
        }

        // 获取当前用户信息
        UserInfo currentUser = iSguiCommonService.getCurrentUserInfo();
        if (ObjectUtil.isEmpty(currentUser)) {
            throw new SguiResultException("获取当前用户信息失败");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        MnjxSi mnjxSi = iMnjxSiService.getById(currentUser.getSiId());
        // 查询Office信息
        MnjxOffice office = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        if (ObjectUtil.isEmpty(office)) {
            throw new SguiResultException("获取Office信息失败");
        }

        if (dto.getCheckPinYinName()) {
            IssueTicketVo vo = new IssueTicketVo();
            vo.setValidateIetFail(false);
            vo.setCheckSameName(true);
            vo.setIssueRes(null);
            vo.setBopBookTicketRes(null);
            return vo;
        }

        // 1. 检查出票条件
        Map<String, Object> map = this.checkIssueConditions(dto, pnr, office, mnjxSi);
        if (map.containsKey("tkneError")) {
            return (IssueTicketVo) map.get("tkneError");
        }

        List<MnjxPnrNm> pnrNmList = (List<MnjxPnrNm>) map.get("pnrNmList");
        // 2. 执行出票
        return this.performIssue(dto, pnr, pnrNmList, office, mnjxSi);
    }

    /**
     * 检查出票条件，并返回需要出票的旅客列表
     */
    private Map<String, Object> checkIssueConditions(IssueTicketDto dto, MnjxPnr pnr, MnjxOffice office, MnjxSi mnjxSi) throws SguiResultException {
        Map<String, Object> map = new HashMap<>();
        // 1.1 检查PNR是否存在（已在主方法中检查）

        // 1.2 检查PNR状态是否是OP
        if (!"OP".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("PNR无法出票，检查PNR状态");
        }

        // 1.3 检查出票权限
        // 需求确认不检查出票权限
//        this.checkIssueAuthority(dto, pnr, office);

        // 1.4 检查航段组中最早的航班起飞时间是否已超过当前时间
        this.checkFlightTime(pnr);

        // 1.5 检查打票机
        this.checkPrinter(dto.getIssue().getPrinterNo(), pnr, office, mnjxSi, dto.getIssue().getIssueItems().size());

        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        // 筛选单独出票的旅客，如果为空表示全部出票
        List<IssueTicketDto.IssueItemDto> issuePassengerList = dto.getIssue().getIssueItems().stream()
                .filter(i -> StrUtil.isNotEmpty(i.getPassengerId()) && i.getSelected())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(issuePassengerList)) {
            List<Integer> passengerIdList = issuePassengerList.stream()
                    .filter(IssueTicketDto.IssueItemDto::getSelected)
                    .map(i -> Integer.parseInt(i.getPassengerId().replace("P", "")))
                    .distinct()
                    .collect(Collectors.toList());
            passengers = passengers.stream()
                    .filter(p -> passengerIdList.contains(p.getPsgIndex()))
                    .collect(Collectors.toList());
        }

        if (Boolean.TRUE.equals(dto.getIssue().getSelectPassengerExistOi())) {
            // 存在OI时出票检查
            this.checkExistOi(passengers, map);
        } else {
            // 没有OI项即没做改签运价时检查当前旅客是否已出过票
            this.checkIfIssued(passengers, issuePassengerList);
        }

        // 1.6 检查非婴儿旅客的证件信息是否存在SSR FOID或SSR DOCS
        this.checkSsrFoid(passengers, issuePassengerList);

        // 1.7 检查婴儿旅客的SSR INFT行动代码是否是HK
        this.checkInfantSsr(pnr, passengers, issuePassengerList);

        // 1.8 检查运价组PNRFP PNRFC PNRFN或NMFP NMFC NMFN是否完整
        this.checkPriceInfo(pnr, passengers, issuePassengerList, dto);

        // 不检查出票时限
        // 1.9 检查出票时限
//        this.checkIssueTimeLimit(pnr);

        map.put("pnrNmList", passengers);
        return map;
    }

    private void checkExistOi(List<MnjxPnrNm> passengers, Map<String, Object> map) {
        List<IssueTicketVo.BopBookTicketResVo> bookTicketResVoList = new ArrayList<>();
        for (MnjxPnrNm passenger : passengers) {
            // 检查该旅客下的SSR TKNE是否已全部删除
            List<MnjxNmSsr> tkneSsrList = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, passenger.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, "TKNE")
                    .list();
            if (CollUtil.isNotEmpty(tkneSsrList)) {
                IssueTicketVo.BopBookTicketResVo bookTicketResVo = new IssueTicketVo.BopBookTicketResVo();
                bookTicketResVo.setSuccessType(false);
                bookTicketResVo.setPassenger("ADL");
                bookTicketResVo.setPassengerNames(passenger.getName());
                bookTicketResVo.setHasTkneFail(false);
                bookTicketResVo.setTxnTraceKey("XXXSAT" + System.currentTimeMillis());
                bookTicketResVo.setIssueDate(DateUtil.now());
                bookTicketResVo.setDescription("USAS ISSU/EXCH ERROR, BECAUSE:SSR TKNE(主机出票/换开失败)");
                bookTicketResVoList.add(bookTicketResVo);
            }
        }
        if (CollUtil.isNotEmpty(bookTicketResVoList)) {
            IssueTicketVo result = new IssueTicketVo();

            result.setBopBookTicketRes(bookTicketResVoList);

            // 构建出票结果
            IssueTicketVo.IssueResVo issueRes = new IssueTicketVo.IssueResVo();
            issueRes.setResult("0");
            issueRes.setTxnTraceKey(null);

            result.setIssueRes(issueRes);
            result.setValidateIetFail(false);
            result.setCheckSameName(false);

            map.put("tkneError", result);
        }
    }

    /**
     * 非OI改期重新出票的情况下，检查旅客是否已出过票
     *
     * @param passengers
     * @param issuePassengerList
     * @throws SguiResultException
     */
    private void checkIfIssued(List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issuePassengerList) throws SguiResultException {
        for (MnjxPnrNm passenger : passengers) {
            MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, passenger.getPnrNmId())
                    .one();
            if (ObjectUtil.isNotEmpty(pnrNmTn)) {
                throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:PNR TICKETED(主机出票/换开失败)");
            }
            // 指定旅客出票
            if (CollUtil.isNotEmpty(issuePassengerList)) {
                // 是否还单独指定了婴儿
                List<IssueTicketDto.IssueItemDto> infIssuePassengerList = issuePassengerList.stream()
                        .filter(i -> i.getPassengerId().equals("P" + passenger.getPsgIndex()) && "INF".equals(i.getPassengerType()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(infIssuePassengerList)) {
                    MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                            .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                            .one();
                    pnrNmTn = iMnjxPnrNmTnService.lambdaQuery()
                            .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                            .one();
                    if (ObjectUtil.isNotEmpty(pnrNmTn)) {
                        throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:PNR TICKETED(主机出票/换开失败)");
                    }
                }
            } else {
                // 全部旅客出票，需要单独查当前旅客是否携带婴儿
                MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                        .one();
                if (ObjectUtil.isNotEmpty(nmXn)) {
                    pnrNmTn = iMnjxPnrNmTnService.lambdaQuery()
                            .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                            .one();
                    if (ObjectUtil.isNotEmpty(pnrNmTn)) {
                        throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:PNR TICKETED(主机出票/换开失败)");
                    }
                }
            }
        }
    }

    /**
     * 执行出票
     */
    private IssueTicketVo performIssue(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, MnjxOffice office, MnjxSi currentUser) throws SguiResultException {
        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());

        List<MnjxPnrNmTn> tnList = new ArrayList<>();
        List<MnjxPnrNmTicket> ticketList = new ArrayList<>();
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        // 2.1 获取所有出票的旅客
        List<IssueTicketDto.IssueItemDto> issueItems = dto.getIssue().getIssueItems();
        if (issueItems.stream().noneMatch(i -> StrUtil.isNotEmpty(i.getPassengerId())) && !"EXCH".equals(pnr.getPnrStatus())) {
            // 过滤掉已经出票的旅客
            List<String> pnrNmIds = passengers.stream()
                    .map(MnjxPnrNm::getPnrNmId)
                    .collect(Collectors.toList());

            List<MnjxPnrNmTn> existingTickets = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                    .list();

            if (CollUtil.isNotEmpty(existingTickets)) {
                Set<String> ticketedPassengerIds = existingTickets.stream()
                        .map(MnjxPnrNmTn::getPnrNmId)
                        .collect(Collectors.toSet());

                passengers = passengers.stream()
                        .filter(p -> !ticketedPassengerIds.contains(p.getPnrNmId()))
                        .collect(Collectors.toList());
            }

            if (CollUtil.isEmpty(passengers)) {
                throw new SguiResultException("所有旅客已出票");
            }
        }

        // 2.2 计算票号，生成tn、nmTicket、ssr tkne记录
        this.generateTicketNumbers(dto, pnr, passengers, office, tnList, ticketList, nmSsrList);

        // 如果是改签出票，需要先删除原来的TN，并更改原来的ticket票面状态为EXCHANGED
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .list();
        if (pnrSegList.stream().anyMatch(p -> "1".equals(p.getExchanged()))) {
            this.deleteTnAndUpdateTicketStatus(passengers, currentUser, newAtNo);
            pnrSegList.forEach(p -> p.setExchanged("0"));
            iMnjxPnrSegService.updateBatchById(pnrSegList);
            pnr.setUpdateTime(new Date());
        }

        iMnjxPnrNmTnService.saveBatch(tnList);
        iMnjxPnrNmTicketService.saveBatch(ticketList);
        iMnjxNmSsrService.saveBatch(nmSsrList);

        // 2.3 存储数据到pnr_nm_ticket_price表
        this.saveTicketPriceData(dto, pnr, passengers, tnList, ticketList);

        // 2.4 删除TK FC EI OI记录
        this.deleteTkFcEiRecords(dto, pnr, passengers, newAtNo);

        // 2.5 生成新的TK项，值为"T"
        this.generateTkRecord(pnr);

        // 如果请求的dorr为true，需要把航段组行动代码更新为RR
        if (Boolean.TRUE.equals(dto.getIssue().getDorr())) {
            this.changeSegActionCodeToRr(pnr);
        }

        // 2.6 生成新的RMK项，值为"TJ 当前OFFICE号"
        this.generateRmkRecord(pnr, office);

        // 2.7 生成psgCki和psgSeat数据
        this.generatePsgCkiAndSeat(pnr, passengers);

        // 2.8 生成客票操作记录ticketOperateRecord
        this.generateTicketOperateRecord(ticketList, currentUser);

        // 3. PNR项的重新排序和封口
        this.reorderAndSeal(pnr, newAtNo, currentUser);

        // 构建返回结果
        return this.buildResult(dto);
    }

    /**
     * 改签出票，删除原票号TN，更新原票状态为EXCHANGED
     *
     * @param passengers
     * @param si
     */
    private void deleteTnAndUpdateTicketStatus(List<MnjxPnrNm> passengers, MnjxSi si, String newAtNo) {
        List<MnjxPnrNmTn> deleteTnList = new ArrayList<>();
        List<MnjxPnrNmTicket> updateTicketList = new ArrayList<>();
        for (MnjxPnrNm pnrNm : passengers) {
            MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, pnrNm.getPnrNmId())
                    .one();
            List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, nmTn.getTnId())
                    .list();
            nmTicketList.forEach(n -> {
                if (StrUtil.isNotEmpty(n.getTicketStatus1())) {
                    n.setTicketStatus1(Constant.TICKET_STATUS_EXCHANGED);
                }
                if (StrUtil.isNotEmpty(n.getTicketStatus2())) {
                    n.setTicketStatus2(Constant.TICKET_STATUS_EXCHANGED);
                }
            });
            deleteTnList.add(nmTn);
            updateTicketList.addAll(nmTicketList);
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, pnrNm.getPnrNmId())
                    .one();
            if (ObjectUtil.isNotEmpty(nmXn)) {
                MnjxPnrNmTn xnTn = iMnjxPnrNmTnService.lambdaQuery()
                        .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                        .one();
                List<MnjxPnrNmTicket> xnTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .in(MnjxPnrNmTicket::getPnrNmTnId, xnTn.getTnId())
                        .list();
                xnTicketList.forEach(n -> {
                    if (StrUtil.isNotEmpty(n.getTicketStatus1())) {
                        n.setTicketStatus1(Constant.TICKET_STATUS_EXCHANGED);
                    }
                    if (StrUtil.isNotEmpty(n.getTicketStatus2())) {
                        n.setTicketStatus2(Constant.TICKET_STATUS_EXCHANGED);
                    }
                });
                deleteTnList.add(xnTn);
                updateTicketList.addAll(xnTicketList);
            }
        }
        if (CollUtil.isNotEmpty(deleteTnList)) {
            iMnjxPnrNmTnService.removeByIds(deleteTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList()));
            for (MnjxPnrNmTn tn : deleteTnList) {
                // 更新pnrRecord
                MnjxPnrRecord tnRecord = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, passengers.get(0).getPnrId())
                        .eq(MnjxPnrRecord::getPnrType, "TN")
                        .eq(MnjxPnrRecord::getInputValue, tn.getInputValue())
                        .one();
                tnRecord.setChangeMark("X");
                tnRecord.setChangeAtNo(newAtNo);
                iMnjxPnrRecordService.updateById(tnRecord);
            }
        }
        if (CollUtil.isNotEmpty(updateTicketList)) {
            iMnjxPnrNmTicketService.updateBatchById(updateTicketList);
            this.generateTicketOperateRecord(updateTicketList, si);
        }
    }

    private MnjxPnrFp buildPnrFp(String dtoFp, MnjxPnr pnr) {
        MnjxPnrFp pnrFp = new MnjxPnrFp();
        pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFp.setPnrId(pnr.getPnrId());
        pnrFp.setPnrIndex(0); // 后续重新排序
        pnrFp.setIsBaby(dtoFp.contains("IN/") ? 1 : 0);
        pnrFp.setPatType(dtoFp.contains("IN/") ? "IN" : "AD");
        pnrFp.setPayType(dtoFp.split(",")[0].replace("IN/", ""));
        pnrFp.setCurrencyType(dtoFp.split(",")[1]);
        pnrFp.setInputValue("FP/" + dtoFp);
        return pnrFp;
    }

    private MnjxNmFp buildNmFp(String dtoFp, List<MnjxPnrNm> passengers) {
        MnjxNmFp nmFp = new MnjxNmFp();
        nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
        Optional<MnjxPnrNm> optional = passengers.stream()
                .filter(p -> p.getPsgIndex() == Integer.parseInt(dtoFp.substring(dtoFp.lastIndexOf("/P") + 2)))
                .findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        nmFp.setPnrNmId(optional.get().getPnrNmId());
        nmFp.setPnrIndex(0); // 后续重新排序
        nmFp.setIsBaby(dtoFp.contains("IN/") ? 1 : 0);
        nmFp.setPatType(dtoFp.contains("IN/") ? "IN" : "AD");
        nmFp.setPayType(dtoFp.split(",")[0].replace("IN/", ""));
        nmFp.setCurrencyType(dtoFp.split(",")[1].replaceAll("/P\\d+", ""));
        nmFp.setInputValue("FP/" + dtoFp);
        return nmFp;
    }

    /**
     * 修改航段组行动代码为RR
     *
     * @param pnr
     */
    private void changeSegActionCodeToRr(MnjxPnr pnr) {
        List<MnjxPnrSeg> segList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrSeg::getPnrSegType, "SA")
                .list();
        segList = segList.stream()
                .filter(s -> !"RR".equals(s.getActionCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(segList)) {
            return;
        }
        segList.forEach(s -> {
            s.setActionCode("RR");
            String inputValue = s.getInputValue();
            s.setInputValue(inputValue.replace(" HK", " RR"));
        });
        iMnjxPnrSegService.updateBatchById(segList);
    }


    /**
     * 查询航班是已存在的旅客编号
     *
     * @param tnList 已筛选过需要出票的旅客列表
     */
    private void structurePsgNum(List<MnjxPnrNmTn> tnList, List<MnjxPnrSeg> mnjxPnrSegs) {
        log.debug("{}{}线程进入设置旅客序号", DateUtils.now(), Thread.currentThread().getName());

        //所有航班里各航段
        List<String> s1IdList = mnjxPnrSegs.stream().filter(v -> v.getPnrSegNo() % 2 != 0).map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
        List<String> s2IdList = mnjxPnrSegs.stream().filter(v -> v.getPnrSegNo() % 2 == 0).map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
        synchronized (this) {
            //所有航班按航段分开的
            List<MnjxPnrNmTicket> s1IdNmTicketList = CollUtil.isEmpty(s1IdList) ? new ArrayList<>() : iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getS1Id, s1IdList).list();
            if (CollUtil.isNotEmpty(s1IdNmTicketList)) {
                s1IdNmTicketList = s1IdNmTicketList.stream()
                        .filter(t -> StrUtil.isNotEmpty(t.getHbnb1()))
                        .collect(Collectors.toList());
            }

            List<MnjxPnrNmTicket> s2IdNmTicketList = CollUtil.isEmpty(s2IdList) ? new ArrayList<>() : iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getS2Id, s2IdList).list();
            if (CollUtil.isNotEmpty(s2IdNmTicketList)) {
                s2IdNmTicketList = s2IdNmTicketList.stream()
                        .filter(t -> StrUtil.isNotEmpty(t.getHbnb2()))
                        .collect(Collectors.toList());
            }

            //按航班分组
            Map<String, List<MnjxPnrSeg>> fltMap = mnjxPnrSegs.stream()
                    .collect(Collectors.groupingBy(v -> {
                        if (StrUtils.isNotEmpty(v.getCarrierFlight())) {
                            return v.getFlightDate() + v.getCarrierFlight();
                        } else {
                            return v.getFlightDate() + v.getFlightNo();
                        }
                    }));

            //根据航班遍历
            List<MnjxPnrNmTicket> finalS1IdNmTicketList = s1IdNmTicketList;
            List<MnjxPnrNmTicket> finalS2IdNmTicketList = s2IdNmTicketList;
            fltMap.forEach((k, v) -> {
                int max = 0;
                //该航班所有segId
                List<String> segIds = v.stream()
                        .map(MnjxPnrSeg::getPnrSegId)
                        .collect(Collectors.toList());
                //该航班航段1的
                List<MnjxPnrNmTicket> s1List = finalS1IdNmTicketList.stream()
                        .filter(t -> segIds.contains(t.getS1Id()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(s1List)) {
                    OptionalInt s1Max = s1List.stream()
                            .mapToInt(s -> Integer.parseInt(s.getHbnb1()))
                            .max();
                    max = s1Max.isPresent() ? s1Max.getAsInt() : 0;
                }
                //该航班航段2的
                List<MnjxPnrNmTicket> s2List = finalS2IdNmTicketList.stream()
                        .filter(t -> segIds.contains(t.getS2Id()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(s2List)) {
                    OptionalInt s2Max = s2List.stream()
                            .mapToInt(s -> Integer.parseInt(s.getHbnb2()))
                            .max();
                    int tempMax = s2Max.isPresent() ? s2Max.getAsInt() : 0;
                    max = Math.max(tempMax, max);
                }

                max += 1;
                for (MnjxPnrNmTn tn : tnList) {
                    List<MnjxPnrNmTicket> mnjxPnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery()
                            .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                            .list();
                    for (MnjxPnrNmTicket mnjxPnrNmTicket : mnjxPnrNmTickets) {
                        //航段1,2 id
                        String s1Id = mnjxPnrNmTicket.getS1Id();
                        String s2Id = mnjxPnrNmTicket.getS2Id();
                        String hbnb = StrUtil.fill(StrUtil.toString(max), '0', 4, true);
                        if (segIds.contains(s1Id) && StrUtil.isEmpty(mnjxPnrNmTicket.getHbnb1())) {
                            mnjxPnrNmTicket.setHbnb1(hbnb);
                            max++;
                        } else if (segIds.contains(s2Id) && StrUtil.isEmpty(mnjxPnrNmTicket.getHbnb2())) {
                            mnjxPnrNmTicket.setHbnb2(hbnb);
                            max++;
                        }
                    }
                }
            });
        }
    }

    /**
     * 检查出票权限
     */
    private void checkIssueAuthority(IssueTicketDto dto, MnjxPnr pnr, MnjxOffice office) throws SguiResultException {
        // 查询航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isEmpty(segments)) {
            throw new SguiResultException("PNR中没有航段信息");
        }

        String validateAirline = dto.getIssue().getValidateAirline();

        if (StrUtil.isNotEmpty(validateAirline)) {
            // 检查指定航司的出票权限
            MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                    .eq(MnjxAirline::getAirlineCode, validateAirline)
                    .one();
            if (ObjectUtil.isEmpty(airline)) {
                throw new SguiResultException("航司代码不存在");
            }

            List<MnjxAgentAirline> agentAirlines = iMnjxAgentAirlineService.lambdaQuery()
                    .eq(MnjxAgentAirline::getAgentId, office.getOrgId())
                    .eq(MnjxAgentAirline::getAirlineId, airline.getAirlineId())
                    .list();

            if (CollUtil.isEmpty(agentAirlines)) {
                throw new SguiResultException("没有该航司的出票权限");
            }
        } else {
            // 检查所有航段的航司出票权限
            List<String> airlineCodes = new ArrayList<>();
            for (MnjxPnrSeg segment : segments) {
                if (StrUtil.isNotEmpty(segment.getFlightNo()) && segment.getFlightNo().length() >= 2) {
                    String airlineCode = segment.getFlightNo().substring(0, 2);
                    airlineCodes.add(airlineCode);
                }
            }

            if (CollUtil.isNotEmpty(airlineCodes)) {
                List<String> airlineIds = iMnjxAirlineService.lambdaQuery()
                        .in(MnjxAirline::getAirlineCode, airlineCodes)
                        .list()
                        .stream()
                        .map(MnjxAirline::getAirlineId)
                        .collect(Collectors.toList());

                List<String> agentAirlineIds = iMnjxAgentAirlineService.lambdaQuery()
                        .eq(MnjxAgentAirline::getAgentId, office.getOrgId())
                        .list()
                        .stream()
                        .map(MnjxAgentAirline::getAirlineId)
                        .collect(Collectors.toList());

                List<String> unauthorizedAirlines = airlineIds.stream()
                        .filter(airlineId -> !agentAirlineIds.contains(airlineId))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(unauthorizedAirlines)) {
                    throw new SguiResultException("没有部分航司的出票权限");
                }
            }
        }
    }

    /**
     * 检查航班时间
     */
    private void checkFlightTime(MnjxPnr pnr) throws SguiResultException {
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isNotEmpty(segments)) {
            MnjxPnrSeg firstSegment = segments.get(0);
            if (!"SA".equals(firstSegment.getPnrSegType())) {
                // 检查首段航班是否已起飞
                String flightDate = firstSegment.getFlightDate();
                String estimateOff = firstSegment.getEstimateOff();

                if (StrUtil.isNotEmpty(flightDate) && StrUtil.isNotEmpty(estimateOff)) {

                    String flightDateTime = flightDate + " " + estimateOff.substring(0, 2) + ":" + estimateOff.substring(2);
                    Date flightTime = DateUtil.offsetHour(DateUtil.parse(flightDateTime, "yyyy-MM-dd HH:mm"), -2);
                    if (flightTime.before(new Date())) {
                        throw new SguiResultException("航班已关闭出票");
                    }
                }
            }
        }
    }

    /**
     * 检查打票机
     */
    private void checkPrinter(String printerNo, MnjxPnr pnr, MnjxOffice office, MnjxSi currentUser, int passengerCount) throws SguiResultException {
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, printerNo)
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .one();

        if (ObjectUtil.isEmpty(printer)) {
            throw new SguiResultException("打票机不存在");
        }

        // 检查打票机类型是否是4
        if (!"4".equals(printer.getPrinterType())) {
            throw new SguiResultException("打票机类型错误");
        }

        // 检查打票机是否由当前用户建控
        if (StrUtil.isEmpty(printer.getSiId()) || !currentUser.getSiId().equals(printer.getSiId())) {
            throw new SguiResultException("打票机未建控或不是当前用户建控");
        }

        // 检查打票机状态是否是UP
        if (!"UP".equals(printer.getPrinterStatus())) {
            throw new SguiResultException("打票机状态不是UP");
        }

        // 检查打票机属性是否是TAT/ET
        if (!"TAT/ET".equals(printer.getPrintAttribute())) {
            throw new SguiResultException("打票机属性不是TAT/ET");
        }

        // 检查打票机输入状态是否是ACTIVE
        if (!"ACTIVE".equals(printer.getInputStatus())) {
            throw new SguiResultException("打票机输入状态不是ACTIVE");
        }

        // 检查票号库存
        if (ObjectUtil.isEmpty(printer.getTicketStart()) || ObjectUtil.isEmpty(printer.getTicketEnd())) {
            throw new SguiResultException("打票机未上票");
        }

        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .list();

        int segmentCount = (int) segments.stream().filter(s -> !"SA".equals(s.getPnrSegType())).count();
        int ticketsPerPassenger = (segmentCount + 1) / 2; // 每个旅客需要的票数
        int totalTicketsNeeded = passengerCount * ticketsPerPassenger;

        BigInteger availableTickets;
        if (ObjectUtil.isNotEmpty(printer.getLastTicket())) {
            availableTickets = printer.getTicketEnd().subtract(printer.getLastTicket());
        } else {
            availableTickets = printer.getTicketEnd().subtract(printer.getTicketStart()).add(BigInteger.ONE);
        }

        if (availableTickets.compareTo(BigInteger.valueOf(totalTicketsNeeded)) < 0) {
            throw new SguiResultException("票号库存不足");
        }
    }

    /**
     * 检查SSR FOID
     */
    private void checkSsrFoid(List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issueItems) throws SguiResultException {
        // 检查非婴儿旅客的证件信息
        for (MnjxPnrNm passenger : passengers) {
            if (CollUtil.isNotEmpty(issueItems)
                    && issueItems.stream().anyMatch(i -> passenger.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", "")) && "INF".equals(i.getPassengerType()))) {
                continue;
            }
            List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, passenger.getPnrNmId())
                    .in(MnjxNmSsr::getSsrType, "FOID", "DOCS")
                    .ne(MnjxNmSsr::getActionCode, "XX")
                    .list();

            if (CollUtil.isEmpty(ssrList)) {
                throw new SguiResultException(StrUtil.format("旅客{}缺少有效的证件信息(SSR FOID或DOCS)", passenger.getPsgIndex()));
            }

        }
    }

    /**
     * 检查婴儿SSR INFT
     */
    private void checkInfantSsr(MnjxPnr pnr, List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issueItems) throws SguiResultException {
        if (CollUtil.isNotEmpty(issueItems) && issueItems.stream().noneMatch(i -> "INF".equals(i.getPassengerType()))) {
            return;
        }

        List<String> pnrNmIds = passengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(issueItems)) {
            pnrNmIds = passengers.stream()
                    .filter(
                            p -> issueItems.stream()
                                    .anyMatch(
                                            i -> p.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", "")) && "INF".equals(i.getPassengerType())
                                    )
                    )
                    .map(MnjxPnrNm::getPnrNmId)
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(pnrNmIds)) {
            return;
        }

        // 查询婴儿信息
        List<MnjxNmXn> infants = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                .list();

        if (CollUtil.isNotEmpty(infants)) {
            // 查询航段数量（非SA类型）
            long segmentCount = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                    .ne(MnjxPnrSeg::getPnrSegType, "SA")
                    .count();

            for (MnjxNmXn infant : infants) {
                // 检查每个婴儿的SSR INFT记录
                List<MnjxNmSsr> inftSsrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, infant.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .list();

                // 检查SSR INFT数量是否与航段数量匹配
                if (inftSsrList.size() != segmentCount) {
                    throw new SguiResultException("婴儿SSR INFT数量与航段数量不匹配");
                }

                // 检查所有SSR INFT的行动代码是否为HK
                boolean allHK = inftSsrList.stream()
                        .allMatch(ssr -> "HK".equals(ssr.getActionCode()));

                if (!allHK) {
                    throw new SguiResultException("婴儿SSR INFT行动代码不是HK");
                }
            }
        }
    }

    /**
     * 检查运价信息
     */
    private void checkPriceInfo(MnjxPnr pnr, List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issueItems, IssueTicketDto dto) throws SguiResultException {
        List<MnjxPnrSeg> segList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .list();

        // 重新保存一份所有旅客数据，原passeners可能会被筛选
        List<MnjxPnrNm> allPnrNmList = new ArrayList<>();
        passengers.forEach(p -> {
            MnjxPnrNm pnrNm = new MnjxPnrNm();
            BeanUtil.copyProperties(p, pnrNm);
            allPnrNmList.add(pnrNm);
        });

        // 部分出票时，只检查出票旅客的运价
        if (CollUtil.isNotEmpty(issueItems)) {
            // 筛选对应序号的旅客
            List<Integer> passengerIdList = issueItems.stream()
                    .map(i -> Integer.parseInt(i.getPassengerId().replace("P", "")))
                    .collect(Collectors.toList());
            passengers = passengers.stream()
                    .filter(p -> passengerIdList.contains(p.getPsgIndex()))
                    .collect(Collectors.toList());
        }

        // 检查PNR级别的运价信息
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .list();

        // 旅客级别的运价检查
        List<String> pnrNmIds = passengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        // 先获取所有旅客的运价信息
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .in(MnjxNmFc::getPnrNmId, pnrNmIds)
                .list();
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIds)
                .list();
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .in(MnjxNmFp::getPnrNmId, pnrNmIds)
                .list();
        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .in(MnjxNmEi::getPnrNmId, pnrNmIds)
                .list();

        boolean notCompleteEi = CollUtil.isEmpty(pnrEiList) && CollUtil.isEmpty(nmEiList);
        if (notCompleteEi) {
            throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
        }
        boolean notCompleteFc = CollUtil.isEmpty(pnrFcList) && CollUtil.isEmpty(nmFcList);
        if (notCompleteFc) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FC);
        }
        boolean notCompleteFn = CollUtil.isEmpty(pnrFnList) && CollUtil.isEmpty(nmFnList);
        if (notCompleteFn) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FN);
        }
        boolean notCompleteFp = CollUtil.isEmpty(pnrFpList) && CollUtil.isEmpty(nmFpList);
        boolean needToSaveFp = false;
        if (notCompleteFp) {
            // 如果PNR里没有FP，在出票的时候同时输入的，需要在这里构建FP，构建完后在下面继续判断
            if (CollUtil.isNotEmpty(dto.getFpList())) {
                // dtoFp:CASH,CNY  IN/CASH,CNY  CASH,CNY/P1  IN/CASH,CNY/P2
                for (String dtoFp : dto.getFpList()) {
                    if (!"CASH,CNY".equals(dtoFp.replace("IN/", "").replaceAll("/P\\d+", ""))) {
                        throw new SguiResultException("CHECK FORM OF PAYMENT");
                    }
                    if (dtoFp.contains("/P")) {
                        // 旅客级别的FP
                        nmFpList.add(this.buildNmFp(dtoFp, allPnrNmList));
                    } else {
                        // PNR级别的FP
                        pnrFpList.add(this.buildPnrFp(dtoFp, pnr));
                    }
                }
                // 去除空元素
                pnrFpList = pnrFpList.stream().filter(Objects::nonNull).collect(Collectors.toList());
                nmFpList = nmFpList.stream().filter(Objects::nonNull).collect(Collectors.toList());
                needToSaveFp = CollUtil.isNotEmpty(pnrFpList) || CollUtil.isNotEmpty(nmFpList);
            } else {
                throw new SguiResultException("NO FORM OF PAYMENT");
            }
        }

        // FC和航段的判断
        if (CollUtil.isNotEmpty(pnrFcList) && CollUtil.isNotEmpty(nmFcList)) {
            switch (segList.size()) {
                case 1:
                    if (!"SA".equals(segList.get(0).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg1Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg1Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 2:
                    if (!"SA".equals(segList.get(1).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg2Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg2Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 3:
                    if (!"SA".equals(segList.get(2).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg3Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg3Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 4:
                    if (!"SA".equals(segList.get(3).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg4Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg4Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 5:
                    if (!"SA".equals(segList.get(4).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg5Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg5Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 如果只存在PNR级别运价
            if (CollUtil.isNotEmpty(pnrFcList)) {
                switch (segList.size()) {
                    case 1:
                        if (!"SA".equals(segList.get(0).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg1Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 2:
                        if (!"SA".equals(segList.get(1).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg2Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 3:
                        if (!"SA".equals(segList.get(2).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg3Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 4:
                        if (!"SA".equals(segList.get(3).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg4Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 5:
                        if (!"SA".equals(segList.get(4).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg5Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    default:
                        break;
                }
            }
            // 只存在旅客级别运价
            else if (CollUtil.isNotEmpty(nmFcList)) {
                switch (segList.size()) {
                    case 1:
                        if (!"SA".equals(segList.get(0).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg1Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 2:
                        if (!"SA".equals(segList.get(1).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg2Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 3:
                        if (!"SA".equals(segList.get(2).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg3Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 4:
                        if (!"SA".equals(segList.get(3).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg4Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 5:
                        if (!"SA".equals(segList.get(4).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg5Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    default:
                        break;
                }

            }
        }

        // 检查每个旅客是否都有完整的运价信息
        for (MnjxPnrNm passenger : passengers) {
            Integer psgIndex = passenger.getPsgIndex();
            // 指定部分旅客时，筛选对应P序号的旅客
            if (CollUtil.isNotEmpty(issueItems)) {
                // 指定的非婴儿旅客
                List<IssueTicketDto.IssueItemDto> adtOrChdIssueItemList = issueItems.stream()
                        .filter(i -> !"INF".equals(i.getPassengerType()) && psgIndex == Integer.parseInt(i.getPassengerId().replace("P", "")))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(adtOrChdIssueItemList)) {
                    this.validateFn(nmFnList, pnrFnList, passenger, true);
                    this.validateFp(nmFpList, pnrFpList, passenger, true);
                    this.validateFc(nmFcList, pnrFcList, passenger, true);
                    this.validateEi(nmEiList, pnrEiList, passenger, true);
                }

                // 指定的婴儿旅客
                List<IssueTicketDto.IssueItemDto> infIssueItemList = issueItems.stream()
                        .filter(i -> "INF".equals(i.getPassengerType()) && psgIndex == Integer.parseInt(i.getPassengerId().replace("P", "")))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(infIssueItemList)) {
                    this.validateFn(nmFnList, pnrFnList, passenger, false);
                    this.validateFp(nmFpList, pnrFpList, passenger, false);
                    this.validateFc(nmFcList, pnrFcList, passenger, false);
                    this.validateEi(nmEiList, pnrEiList, passenger, false);
                }
            } else {
                this.validateFn(nmFnList, pnrFnList, passenger, true);
                this.validateFp(nmFpList, pnrFpList, passenger, true);
                this.validateFc(nmFcList, pnrFcList, passenger, true);
                this.validateEi(nmEiList, pnrEiList, passenger, true);
                // 有婴儿额外检查旅客携带婴儿的运价
                MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                        .one();
                if (ObjectUtil.isNotEmpty(nmXn)) {
                    this.validateFn(nmFnList, pnrFnList, passenger, false);
                    this.validateFp(nmFpList, pnrFpList, passenger, false);
                    this.validateFc(nmFcList, pnrFcList, passenger, false);
                    this.validateEi(nmEiList, pnrEiList, passenger, false);
                }
            }
        }

        // 检查完后把FP入库
        if (needToSaveFp) {
            if (CollUtil.isNotEmpty(pnrFpList)) {
                iMnjxPnrFpService.saveBatch(pnrFpList);
            }
            if (CollUtil.isNotEmpty(nmFpList)) {
                iMnjxNmFpService.saveBatch(nmFpList);
            }
        }
    }

    private void validateEi(List<MnjxNmEi> nmEiList, List<MnjxPnrEi> pnrEiList, MnjxPnrNm passenger, boolean isAdt) throws SguiResultException {
        if (isAdt) {
            // 交叉检查
            // 如果旅客EI不为空，从旅客EI开始检查
            if (CollUtil.isNotEmpty(nmEiList)) {
                // 没有非婴儿的nmEI时
                if (nmEiList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && !e.getInputValue().contains("/IN"))) {
                    // 再查pnrEi有没有非婴儿的ei
                    if (CollUtil.isNotEmpty(pnrEiList)) {
                        // pnrEi也没有非婴儿ei，报错
                        if (pnrEiList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && !e.getInputValue().contains("/IN"))) {
                            throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
                        }
                    }
                    // 没有pnrEi，报错
                    else {
                        throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
                    }
                }
            }
            // 检查pnrEi（前面检查过pnrEi和nmEi至少存在其中一种，这里else直接进入pnrEi的判断）
            else {
                // pnrEi找不到非婴儿Ei，报错
                if (pnrEiList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && !e.getInputValue().contains("/IN"))) {
                    throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
                }
            }
        } else {
            // 交叉检查
            // 如果旅客EI不为空，从旅客EI开始检查
            if (CollUtil.isNotEmpty(nmEiList)) {
                // 没有婴儿的nmEi时
                if (nmEiList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getInputValue().contains("/IN"))) {
                    // 再查pnrEi有没有婴儿的Ei
                    if (CollUtil.isNotEmpty(pnrEiList)) {
                        // pnrEi也没有婴儿ei，报错
                        if (pnrEiList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getInputValue().contains("/IN"))) {
                            throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
                        }
                    }
                    // 没有pnrEi，报错
                    else {
                        throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
                    }
                }
            }
            // 检查pnrEi（前面检查过pnrEi和nmEi至少存在其中一种，这里else直接进入pnrEi的判断）
            else {
                // pnrEi找不到婴儿Ei，报错
                if (pnrEiList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getInputValue().contains("/IN"))) {
                    throw new SguiResultException("USAS ISSU/EXCH ERROR, BECAUSE:EI ELEMENT MISSING(主机出票/换开失败)");
                }
            }
        }
    }

    private void validateFp(List<MnjxNmFp> nmFpList, List<MnjxPnrFp> pnrFpList, MnjxPnrNm passenger, boolean isAdt) throws SguiResultException {
        if (isAdt) {
            // 交叉检查
            // 如果旅客FP不为空，从旅客fp开始检查
            if (CollUtil.isNotEmpty(nmFpList)) {
                // 没有婴儿的nmFp
                if (nmFpList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getIsBaby() == 0)) {
                    // 再查pnrFp有没有非婴儿的fp
                    if (CollUtil.isNotEmpty(pnrFpList)) {
                        // pnrFp也没有非婴儿fp，报错
                        if (pnrFpList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 0)) {
                            throw new SguiResultException("NO FORM OF PAYMENT");
                        }
                    }
                    // 没有pnrFp，报错
                    else {
                        throw new SguiResultException("NO FORM OF PAYMENT");
                    }
                }
            }
            // 检查pnrFp（前面检查过pnrFp和nmFp至少存在其中一种，这里else直接进入pnrFp的判断）
            else {
                // pnrFp找不到非婴儿fp，报错
                if (pnrFpList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 0)) {
                    throw new SguiResultException("NO FORM OF PAYMENT");
                }
            }
        } else {
            // 交叉检查
            // 如果旅客FP不为空，从旅客fp开始检查
            if (CollUtil.isNotEmpty(nmFpList)) {
                // 没有婴儿的nmFp
                if (nmFpList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getIsBaby() == 1)) {
                    // 再查pnrFp有没有婴儿的fp
                    if (CollUtil.isNotEmpty(pnrFpList)) {
                        // pnrFp也没有婴儿，报错
                        if (pnrFpList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 1)) {
                            throw new SguiResultException("NO FORM OF PAYMENT");
                        }
                    }
                    // 没有pnrFp，报错
                    else {
                        throw new SguiResultException("NO FORM OF PAYMENT");
                    }
                }
            }
            // 检查pnrFp（前面检查过pnrFp和nmFp至少存在其中一种，这里else直接进入pnrFp的判断）
            else {
                // pnrFp找不到婴儿fp，报错
                if (pnrFpList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 1)) {
                    throw new SguiResultException("NO FORM OF PAYMENT");
                }
            }
        }
    }

    private void validateFc(List<MnjxNmFc> nmFcList, List<MnjxPnrFc> pnrFcList, MnjxPnrNm passenger, boolean isAdt) throws SguiResultException {
        if (isAdt) {
            // 交叉检查
            // 如果旅客FC不为空，从旅客FC开始检查
            if (CollUtil.isNotEmpty(nmFcList)) {
                // 没有婴儿的nmFc
                if (nmFcList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getIsBaby() == 0)) {
                    // 再查pnrFc有没有婴儿的fc
                    if (CollUtil.isNotEmpty(pnrFcList)) {
                        // pnrFc也没有婴儿，报错
                        if (pnrFcList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 0)) {
                            throw new SguiResultException("INCOMPLETE FC");
                        }
                        // 如果旅客是成人，但找不到成人pnrFc，报错（pnr中同时有成人和儿童，但运价pnrFc只有儿童）
                        if (StrUtil.equalsAny(passenger.getPsgType(), "0", "3", "4") && pnrFcList.stream().allMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && (e.getInputValue().contains("**(CH)") || e.getInputValue().contains("**(IN)")))) {
                            throw new SguiResultException("INCOMPLETE FC");
                        }
                    }
                    // 没有pnrFc，报错
                    else {
                        throw new SguiResultException("INCOMPLETE FC");
                    }
                }
            }
            // 检查pnrFc（前面检查过pnrFc和nmFc至少存在其中一种，这里else直接进入pnrFc的判断）
            else {
                // pnrFc找不到非婴儿fc，报错
                if (pnrFcList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 0)) {
                    throw new SguiResultException("INCOMPLETE FC");
                }
                // 如果旅客是成人，但找不到成人pnrFc，报错（pnr中同时有成人和儿童，但运价pnrFc只有儿童）
                if (StrUtil.equalsAny(passenger.getPsgType(), "0", "3", "4") && pnrFcList.stream().allMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && (e.getInputValue().contains("**(CH)") || e.getInputValue().contains("**(IN)")))) {
                    throw new SguiResultException("INCOMPLETE FC");
                }
            }
        } else {
            // 交叉检查
            // 如果旅客FC不为空，从旅客FC开始检查
            if (CollUtil.isNotEmpty(nmFcList)) {
                // 没有婴儿的nmFc
                if (nmFcList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getIsBaby() == 1)) {
                    // 再查pnrFc有没有婴儿的fc
                    if (CollUtil.isNotEmpty(pnrFcList)) {
                        // pnrFc也没有婴儿，报错
                        if (pnrFcList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 1)) {
                            throw new SguiResultException("INCOMPLETE FC");
                        }
                    }
                    // 没有pnrFc，报错
                    else {
                        throw new SguiResultException("INCOMPLETE FC");
                    }
                }
            }
            // 检查pnrFc（前面检查过pnrFc和nmFc至少存在其中一种，这里else直接进入pnrFc的判断）
            else {
                // pnrFc找不到婴儿fc，报错
                if (pnrFcList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 1)) {
                    throw new SguiResultException("INCOMPLETE FC");
                }
            }
        }
    }

    private void validateFn(List<MnjxNmFn> nmFnList, List<MnjxPnrFn> pnrFnList, MnjxPnrNm passenger, boolean isAdt) throws SguiResultException {
        if (isAdt) {
            // 交叉检查
            // 如果旅客FN不为空，从旅客FN开始检查
            if (CollUtil.isNotEmpty(nmFnList)) {
                // 没有婴儿的nmFn
                if (nmFnList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getIsBaby() == 0)) {
                    // 再查pnrFn有没有非婴儿的fn
                    if (CollUtil.isNotEmpty(pnrFnList)) {
                        // pnrFn也没有非婴儿的，报错
                        if (pnrFnList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 0)) {
                            throw new SguiResultException("INCOMPLETE FN/P" + passenger.getPsgIndex());
                        }
                    }
                    // 没有pnrFn，报错
                    else {
                        throw new SguiResultException("INCOMPLETE FN/P" + passenger.getPsgIndex());
                    }
                }
            }
            // 检查pnrFn（前面检查过pnrFn和nmFn至少存在其中一种，这里else直接进入pnrFn的判断）
            else {
                // pnrFn找不到非婴儿fn，报错
                if (pnrFnList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 0)) {
                    throw new SguiResultException("INCOMPLETE FN/P" + passenger.getPsgIndex());
                }
            }
        } else {
            // 交叉检查
            // 如果旅客FN不为空，从旅客FN开始检查
            if (CollUtil.isNotEmpty(nmFnList)) {
                // 没有婴儿的nmFn
                if (nmFnList.stream().noneMatch(e -> e.getPnrNmId().equals(passenger.getPnrNmId()) && e.getIsBaby() == 1)) {
                    // 再查pnrFp有没有婴儿的fn
                    if (CollUtil.isNotEmpty(pnrFnList)) {
                        // pnrFn也没有婴儿，报错
                        if (pnrFnList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 1)) {
                            throw new SguiResultException("INCOMPLETE FN/IN/P" + passenger.getPsgIndex());
                        }
                    }
                    // 没有pnrFn，报错
                    else {
                        throw new SguiResultException("INCOMPLETE FN/IN/P" + passenger.getPsgIndex());
                    }
                }
            }
            // 检查pnrFn（前面检查过pnrFn和nmFn至少存在其中一种，这里else直接进入pnrFn的判断）
            else {
                // pnrFn找不到婴儿fn，报错
                if (pnrFnList.stream().noneMatch(e -> e.getPnrId().equals(passenger.getPnrId()) && e.getIsBaby() == 1)) {
                    throw new SguiResultException("INCOMPLETE FN/IN/P" + passenger.getPsgIndex());
                }
            }
        }
    }

    /**
     * 检查出票时限
     */
    private void checkIssueTimeLimit(MnjxPnr pnr) throws SguiResultException {
        // 查询首段航班信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isNotEmpty(segments)) {
            MnjxPnrSeg firstSegment = segments.get(0);
            if (!"SA".equals(firstSegment.getPnrSegType())) {
                String flightDate = firstSegment.getFlightDate();
                String estimateOff = firstSegment.getEstimateOff();

                if (StrUtil.isNotEmpty(flightDate) && StrUtil.isNotEmpty(estimateOff)) {
                    String flightDateTime = flightDate + " " + estimateOff.substring(0, 2) + ":" + estimateOff.substring(2);
                    Date flightTime = DateUtil.parse(flightDateTime, "yyyy-MM-dd HH:mm");

                    // 检查是否超过出票时限（这里简化处理，实际可能需要更复杂的逻辑）
                    if (flightTime.before(new Date())) {
                        throw new SguiResultException("已超过出票时限");
                    }
                }
            }
        }
    }

    /**
     * 获取出票旅客
     */
    private List<MnjxPnrNm> getIssuePassengers(MnjxPnr pnr) throws SguiResultException {
        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("PNR中没有旅客信息");
        }

        // 过滤掉已经出票的旅客
        List<String> pnrNmIds = passengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        List<MnjxPnrNmTn> existingTickets = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                .list();

        if (CollUtil.isNotEmpty(existingTickets)) {
            Set<String> ticketedPassengerIds = existingTickets.stream()
                    .map(MnjxPnrNmTn::getPnrNmId)
                    .collect(Collectors.toSet());

            passengers = passengers.stream()
                    .filter(p -> !ticketedPassengerIds.contains(p.getPnrNmId()))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("所有旅客已出票");
        }

        return passengers;
    }

    /**
     * 生成票号
     */
    private void generateTicketNumbers(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, MnjxOffice office,
                                       List<MnjxPnrNmTn> tnList, List<MnjxPnrNmTicket> ticketList, List<MnjxNmSsr> nmSsrList) throws SguiResultException {
        // 获取打票机信息
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, dto.getIssue().getPrinterNo())
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .one();

        if (ObjectUtil.isEmpty(printer)) {
            throw new SguiResultException("打票机不存在");
        }

        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        // 获取航司信息
        String airlineCode = StrUtil.isNotEmpty(dto.getIssue().getValidateAirline())
                ? dto.getIssue().getValidateAirline()
                : segments.get(0).getFlightNo().substring(0, 2);

        MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, airlineCode)
                .one();

        // 计算票号并生成相关记录
        BigInteger currentTicketNo = ObjectUtil.isNotEmpty(printer.getLastTicket())
                ? printer.getLastTicket().add(BigInteger.ONE)
                : printer.getTicketStart();

        for (MnjxPnrNm passenger : passengers) {
            // 指定旅客出票
            if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
                List<IssueTicketDto.IssueItemDto> filterIssueItems = dto.getIssue().getIssueItems().stream()
                        .filter(i -> passenger.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", "")))
                        .collect(Collectors.toList());
                // 按序号筛选后列表数量大于1说明是成人+携带的婴儿一起出票
                if (filterIssueItems.size() > 1) {
                    // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                    currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, null, tnList, ticketList, nmSsrList);
                    // 上一个结束票号+1为当前最新票号
                    currentTicketNo = currentTicketNo.add(BigInteger.ONE);

                    // 有婴儿，婴儿继续出票
                    MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                            .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                            .one();
                    // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                    currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, nmXn, tnList, ticketList, nmSsrList);
                    // 上一个结束票号+1为当前最新票号
                    currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                } else {
                    String passengerType = filterIssueItems.get(0).getPassengerType();
                    // 指定出成人或儿童票，指定儿童票时前端没有传passengerType，只能用非INF判断
                    if (!"INF".equals(passengerType)) {
                        // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                        currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, null, tnList, ticketList, nmSsrList);
                        // 上一个结束票号+1为当前最新票号
                        currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                    }
                    // 指定出婴儿票
                    else {
                        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                                .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                                .one();
                        if (ObjectUtil.isNotEmpty(nmXn)) {
                            // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                            currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, nmXn, tnList, ticketList, nmSsrList);
                            // 上一个结束票号+1为当前最新票号
                            currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                        }
                    }
                }
            }
            // 全部出票
            else {
                // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, null, tnList, ticketList, nmSsrList);
                // 上一个结束票号+1为当前最新票号
                currentTicketNo = currentTicketNo.add(BigInteger.ONE);

                // 如果有婴儿，婴儿继续出票
                MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                        .one();
                if (ObjectUtil.isNotEmpty(nmXn)) {
                    // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                    currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, nmXn, tnList, ticketList, nmSsrList);
                    // 上一个结束票号+1为当前最新票号
                    currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                }
            }
        }

        // 更新打票机的最后票号
        printer.setLastTicket(currentTicketNo.subtract(BigInteger.ONE));
        iMnjxPrinterService.updateById(printer);
    }

    /**
     * 为单个旅客生成票据
     */
    private BigInteger generatePassengerTickets(IssueTicketDto dto, MnjxPnrNm passenger, List<MnjxPnrSeg> segments,
                                                MnjxPrinter printer, MnjxAirline airline, BigInteger startTicketNo, MnjxNmXn nmXn,
                                                List<MnjxPnrNmTn> tnList, List<MnjxPnrNmTicket> ticketList, List<MnjxNmSsr> nmSsrList) {
        String settlementCode = airline.getAirlineSettlementCode();
        // 生成TN记录
        MnjxPnrNmTn tn = new MnjxPnrNmTn();
        tn.setTnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        if (ObjectUtil.isNotEmpty(nmXn)) {
            tn.setNmXnId(nmXn.getNmXnId());
        } else {
            tn.setPnrNmId(passenger.getPnrNmId());
        }
        tn.setPrinterId(printer.getPrinterId());
        tn.setIssuedTime(DateUtils.now());
        tn.setIssuedSiId(printer.getSiId());
        if (StrUtil.isNotEmpty(dto.getIssue().getValidateAirline())) {
            tn.setIssuedAirline(dto.getIssue().getValidateAirline());
        } else {
            tn.setIssuedAirline(airline.getAirlineCode());
        }

        // 计算票号范围
        int ticketsPerPassenger = (segments.size() - 1) / 2;
        BigInteger endTicketNo = startTicketNo.add(BigInteger.valueOf(ticketsPerPassenger));

        String inputValue;
        if (segments.size() < 3) {
            inputValue = String.format("TN/%s-%s/P%d", settlementCode, endTicketNo, passenger.getPsgIndex());
            if (ObjectUtil.isNotEmpty(nmXn)) {
                inputValue = String.format("TN/IN/%s-%s/P%d", settlementCode, endTicketNo, passenger.getPsgIndex());
            }
        } else {
            String endSub = endTicketNo.toString().substring(endTicketNo.toString().length() - 2);
            inputValue = String.format("TN/%s-%s-%s/P%d", settlementCode, startTicketNo, endSub, passenger.getPsgIndex());
            if (ObjectUtil.isNotEmpty(nmXn)) {
                inputValue = String.format("TN/IN/%s-%s-%s/P%d", settlementCode, startTicketNo, endSub, passenger.getPsgIndex());
            }
        }
        tn.setInputValue(inputValue);

        tnList.add(tn);

        // 生成票据记录和SSR TKNE记录
        this.generateTicketRecords(passenger, segments, tn, settlementCode, startTicketNo, nmXn, ticketList, nmSsrList);

        return endTicketNo;
    }

    /**
     * 生成票据记录
     */
    private void generateTicketRecords(MnjxPnrNm passenger, List<MnjxPnrSeg> segments,
                                       MnjxPnrNmTn tn, String settlementCode, BigInteger startTicketNo, MnjxNmXn nmXn,
                                       List<MnjxPnrNmTicket> ticketList, List<MnjxNmSsr> nmSsrList) {
        BigInteger currentTicketNo = startTicketNo;

        for (int i = 0; i < segments.size(); i += 2) {

            MnjxPnrNmTicket ticket = new MnjxPnrNmTicket();
            ticket.setNmTicketId(IdUtil.getSnowflake(1, 1).nextIdStr());
            ticket.setPnrNmTnId(tn.getTnId());
            ticket.setTicketNo(settlementCode + currentTicketNo);
            ticket.setReceiptPrint("0");
            ticket.setIsEt("1");

            // 设置第一个航段
            if (i < segments.size()) {
                MnjxPnrSeg segment1 = segments.get(i);
                if (!"SA".equals(segment1.getPnrSegType())) {
                    ticket.setS1Id(segment1.getPnrSegId());
                    ticket.setTicketStatus1("OPEN FOR USE");
                    // 生成SSR TKNE记录
                    this.generateSsrTkne(passenger, ticket, segment1, nmXn, nmSsrList);
                }
            }

            // 设置第二个航段
            if (i + 1 < segments.size()) {
                MnjxPnrSeg segment2 = segments.get(i + 1);
                if (!"SA".equals(segment2.getPnrSegType())) {
                    ticket.setS2Id(segment2.getPnrSegId());
                    ticket.setTicketStatus2("OPEN FOR USE");
                    // 生成SSR TKNE记录
                    this.generateSsrTkne(passenger, ticket, segment2, nmXn, nmSsrList);
                }
            }

            ticketList.add(ticket);

            currentTicketNo = currentTicketNo.add(BigInteger.ONE);
        }
    }

    /**
     * 生成SSR TKNE记录
     */
    @Override
    public void generateSsrTkne(MnjxPnrNm passenger, MnjxPnrNmTicket ticket, MnjxPnrSeg segment, MnjxNmXn nmXn, List<MnjxNmSsr> nmSsrList) {
        if ("SA".equals(segment.getPnrSegType())) {
            return;
        }

        MnjxNmSsr nmSsr = new MnjxNmSsr();
        nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmSsr.setPnrNmId(passenger.getPnrNmId());
        nmSsr.setPnrSegNo(segment.getPnrSegNo());
        nmSsr.setSsrType(Constant.TKNE);
        nmSsr.setActionCode(segment.getActionCode());
        nmSsr.setOrgDst(StrUtil.format("{}{}", segment.getOrg(), segment.getDst()));
        nmSsr.setAirlineCode(StrUtil.subPre(segment.getFlightNo(), 2));
        nmSsr.setFltDate(segment.getFlightDate());
        String inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/{}/P{}",
                nmSsr.getSsrType(), nmSsr.getAirlineCode(), nmSsr.getActionCode(), nmSsr.getOrgDst(),
                StrUtil.subSuf(segment.getFlightNo(), 2), segment.getSellCabin(), DateUtils.ymd2PreCom(nmSsr.getFltDate(), 5), ticket.getTicketNo(),
                nmSsr.getPnrSegNo(), passenger.getPsgIndex());
        if (ObjectUtil.isNotEmpty(nmXn)) {
            inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} INF{}/{}/P{}",
                    nmSsr.getSsrType(), nmSsr.getAirlineCode(), nmSsr.getActionCode(), nmSsr.getOrgDst(),
                    StrUtil.subSuf(segment.getFlightNo(), 2), segment.getSellCabin(), DateUtils.ymd2PreCom(nmSsr.getFltDate(), 5), ticket.getTicketNo(),
                    nmSsr.getPnrSegNo(), passenger.getPsgIndex());
        }
        nmSsr.setSsrInfo(inputValue);
        nmSsr.setInputValue(inputValue);

        nmSsrList.add(nmSsr);
    }

    /**
     * 保存票价数据
     */
    private void saveTicketPriceData(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, List<MnjxPnrNmTn> tnList, List<MnjxPnrNmTicket> ticketList) {
        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrNm passenger : passengers) {
            // 获取该旅客的票号
            List<MnjxPnrNmTn> operateTnList = tnList.stream()
                    .filter(t -> passenger.getPnrNmId().equals(t.getPnrNmId()))
                    .collect(Collectors.toList());
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                    .one();
            if (ObjectUtil.isNotEmpty(nmXn)) {
                // 指定旅客时
                if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
                    // 同时指定了成人和婴儿
                    if (dto.getIssue().getIssueItems().stream().filter(i -> passenger.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", ""))).count() > 1) {
                        operateTnList.addAll(tnList.stream()
                                .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                                .collect(Collectors.toList()));
                    }
                    // 只指定了婴儿
                    else if ("INF".equals(dto.getIssue().getIssueItems().get(0).getPassengerType())) {
                        operateTnList = tnList.stream()
                                .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                                .collect(Collectors.toList());
                    }
                }
                // 没指定，全部出票，需要添加携带的婴儿tn
                else {
                    operateTnList.addAll(tnList.stream()
                            .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                            .collect(Collectors.toList()));
                }
            }

            for (MnjxPnrNmTn tn : operateTnList) {
                List<MnjxPnrNmTicket> tickets = ticketList.stream()
                        .filter(t -> tn.getTnId().equals(t.getPnrNmTnId()))
                        .collect(Collectors.toList());
                boolean isInft = StrUtil.isNotEmpty(tn.getNmXnId());

                for (MnjxPnrNmTicket ticket : tickets) {
                    MnjxTicketPrice ticketPrice = new MnjxTicketPrice();
                    ticketPrice.setTicketNo(ticket.getTicketNo());

                    // 获取运价信息
                    this.setTicketPriceInfo(ticketPrice, passenger, pnr, segments, isInft);
                    String issueSegNo;
                    if (StrUtil.isNotEmpty(ticket.getS1Id())) {
                        Integer pnrSegNo = segments.stream()
                                .filter(s -> ticket.getS1Id().equals(s.getPnrSegId()))
                                .collect(Collectors.toList())
                                .get(0)
                                .getPnrSegNo();
                        issueSegNo = pnrSegNo.toString();
                        if (segments.stream().anyMatch(s -> s.getPnrSegNo() > pnrSegNo)) {
                            issueSegNo = issueSegNo + "-" + (pnrSegNo + 1);
                        }
                    } else {
                        Integer pnrSegNo = segments.stream()
                                .filter(s -> ticket.getS2Id().equals(s.getPnrSegId()))
                                .collect(Collectors.toList())
                                .get(0)
                                .getPnrSegNo();
                        issueSegNo = (pnrSegNo - 1) + "-" + pnrSegNo;
                    }
                    ticketPrice.setIssueInfo(StrUtil.format("{} {} {} {} {}", tn.getIssuedSiId(), tn.getIssuedAirline(), tn.getIssuedTime(), tn.getPrinterId(), issueSegNo));

                    iMnjxTicketPriceService.save(ticketPrice);
                }
            }
        }
    }

    /**
     * 设置票价信息
     */
    private void setTicketPriceInfo(MnjxTicketPrice ticketPrice, MnjxPnrNm passenger, MnjxPnr pnr, List<MnjxPnrSeg> segments, boolean isInft) {
        // 获取FC信息
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                .eq(MnjxNmFc::getIsBaby, isInft ? 1 : 0)
                .list();

        if (CollUtil.isNotEmpty(nmFcList)) {
            // 婴儿的价格处理
            if (isInft) {
                // 成人价格在nmFc上，婴儿的价格可能在nmFc上，也可能在pnrFc上
                List<MnjxNmFc> infNmFc = nmFcList.stream()
                        .filter(f -> f.getIsBaby() == 1)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(infNmFc)) {
                    ticketPrice.setFcInfo(infNmFc.get(0).getInputValue());
                } else {
                    List<MnjxPnrFc> infPnrFcList = iMnjxPnrFcService.lambdaQuery()
                            .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrFc::getIsBaby, 1)
                            .list();
                    ticketPrice.setFcInfo(infPnrFcList.get(0).getInputValue());
                }
            } else {
                ticketPrice.setFcInfo(nmFcList.get(0).getInputValue());
            }
        } else {
            List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getIsBaby, isInft ? 1 : 0)
                    .list();
            if (CollUtil.isNotEmpty(pnrFcList)) {
                // 婴儿的价格处理
                if (isInft) {
                    ticketPrice.setFcInfo(pnrFcList.stream().filter(f -> f.getIsBaby() == 1).collect(Collectors.toList()).get(0).getInputValue());
                } else {
                    ticketPrice.setFcInfo(pnrFcList.stream().filter(f -> f.getIsBaby() == 0).collect(Collectors.toList()).get(0).getInputValue());
                }
            }
        }

        // 获取FN信息
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, passenger.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInft ? 1 : 0)
                .list();

        if (CollUtil.isNotEmpty(nmFnList)) {
            // 婴儿的价格处理
            if (isInft) {
                // 成人价格在nmFn上，婴儿的价格可能在nmFn上，也可能在pnrFn上
                List<MnjxNmFn> infNmFn = nmFnList.stream()
                        .filter(f -> f.getIsBaby() == 1)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(infNmFn)) {
                    ticketPrice.setFnInfo(infNmFn.get(0).getInputValue());
                } else {
                    List<MnjxPnrFn> infPnrFnList = iMnjxPnrFnService.lambdaQuery()
                            .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrFn::getIsBaby, 1)
                            .list();
                    ticketPrice.setFnInfo(infPnrFnList.get(0).getInputValue());
                }
            } else {
                ticketPrice.setFnInfo(nmFnList.get(0).getInputValue());
            }
        } else {
            List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInft ? 1 : 0)
                    .list();
            if (CollUtil.isNotEmpty(pnrFnList)) {
                // 婴儿的价格处理
                if (isInft) {
                    ticketPrice.setFnInfo(pnrFnList.stream().filter(f -> f.getIsBaby() == 1).collect(Collectors.toList()).get(0).getInputValue());
                } else {
                    ticketPrice.setFnInfo(pnrFnList.stream().filter(f -> f.getIsBaby() == 0).collect(Collectors.toList()).get(0).getInputValue());
                }
            }
        }

        // 获取FP信息
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrNmId, passenger.getPnrNmId())
                .eq(MnjxNmFp::getIsBaby, isInft ? 1 : 0)
                .list();

        if (CollUtil.isNotEmpty(nmFpList)) {
            // 婴儿的价格处理
            if (isInft) {
                // 成人价格在nmFp上，婴儿的价格可能在nmFp上，也可能在pnrFp上
                List<MnjxNmFp> infNmFp = nmFpList.stream()
                        .filter(f -> f.getIsBaby() == 1)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(infNmFp)) {
                    ticketPrice.setPayType(infNmFp.get(0).getPayType());
                } else {
                    List<MnjxPnrFp> infPnrFpList = iMnjxPnrFpService.lambdaQuery()
                            .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrFp::getIsBaby, 1)
                            .list();
                    ticketPrice.setPayType(infPnrFpList.get(0).getPayType());
                }
            } else {
                ticketPrice.setPayType(nmFpList.get(0).getPayType());
            }
        } else {
            List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getIsBaby, isInft ? 1 : 0)
                    .list();
            if (CollUtil.isNotEmpty(pnrFpList)) {
                // 婴儿的价格处理
                if (isInft) {
                    ticketPrice.setPayType(pnrFpList.stream().filter(f -> f.getIsBaby() == 1).collect(Collectors.toList()).get(0).getPayType());
                } else {
                    ticketPrice.setPayType(pnrFpList.stream().filter(f -> f.getIsBaby() == 0).collect(Collectors.toList()).get(0).getPayType());
                }
            }
        }

        // 构建航段信息
        StringBuilder segInfoBuilder = new StringBuilder();
        for (int i = 0; i < segments.size(); i++) {
            MnjxPnrSeg segment = segments.get(i);
            if (!"SA".equals(segment.getPnrSegType())) {
                segInfoBuilder.append(String.format("%s %s %s %s %s %s %s",
                        segment.getFlightNo(), segment.getOrg(), segment.getDst(), segment.getFlightDate(), segment.getSellCabin(), segment.getEstimateOff(), segment.getEstimateArr()));
            } else {
                segInfoBuilder.append(String.format("SA %s %s", segment.getOrg(), segment.getDst()));
            }
            if (i < segments.size() - 1) {
                segInfoBuilder.append("/");
            }
        }
        ticketPrice.setSegInfo(segInfoBuilder.toString());
    }

    /**
     * 删除TK FC EI记录
     */
    private void deleteTkFcEiRecords(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, String newAtNo) {
        // 删除TK记录（除了T类型）
        MnjxPnrTk pnrTk = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrTk::getPnrTkType, "T")
                .one();
        if (ObjectUtil.isNotEmpty(pnrTk)) {
            // record更新修改标识
            MnjxPnrRecord tkRecord = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, pnrTk.getPnrIndex())
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .one();
            tkRecord.setChangeMark("X");
            tkRecord.setChangeAtNo(newAtNo);
            iMnjxPnrRecordService.updateById(tkRecord);
            iMnjxPnrTkService.removeById(pnrTk.getPnrTkId());
        }

        // 如果是全部出票，删除所有的pnfFc nmFc pnrEi nmEi nmEi
        if (dto.getIssue().getIssueItems().stream().noneMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
            this.deleteAllFcEiOiOtRmk(pnr, passengers, newAtNo);
        } else {
            // 部分出票，如果有pnrFc（成人、儿童、婴儿）或pnrEi（婴儿、非婴儿），需要判断该类型的旅客是否已经都出完票，都出完票才能删除
            this.deletePartialFcEiOiOtRmk(dto, pnr, newAtNo);
        }

        // 如果运价是PNR级别的，需要判断该pnr的旅客是不是全部出完票，才能删除pnrFc和pnrEi
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isNotEmpty(pnrFcList)) {
            List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .list();
            List<String> pnrNmIdList = pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
            List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, pnrNmIdList)
                    .list();
            List<MnjxPnrNmTn> existingTickets = new ArrayList<>();
            int allPsgCount = 0;
            if (pnrFcList.stream().anyMatch(f -> f.getIsBaby() == 0)) {
                existingTickets.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                        .list());
                allPsgCount += pnrNmList.size();
            }
            if (CollUtil.isNotEmpty(nmXnList) && pnrFcList.stream().anyMatch(f -> f.getIsBaby() == 1)) {
                existingTickets.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                        .list());
                allPsgCount += nmXnList.size();
            }
            // 是否全部出完，来确定是否可以删除FC EI
            boolean canDeleteFcEi = allPsgCount == existingTickets.size();
            if (canDeleteFcEi) {
                List<MnjxPnrRecord> fcRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, pnrFcList.stream().map(MnjxPnrFc::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                fcRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(fcRecordList);
                iMnjxPnrFcService.removeByIds(pnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));

                // 删除EI记录
                List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                        .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                        .list();
                if (CollUtil.isNotEmpty(pnrEiList)) {
                    List<MnjxPnrRecord> pnrEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                            .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                            .in(MnjxPnrRecord::getPnrIndex, pnrEiList.stream().map(MnjxPnrEi::getPnrIndex).collect(Collectors.toList()))
                            .isNull(MnjxPnrRecord::getChangeMark)
                            .list();
                    pnrEiRecordList.forEach(f -> {
                        f.setChangeMark("X");
                        f.setChangeAtNo(newAtNo);
                    });
                    iMnjxPnrRecordService.updateBatchById(pnrEiRecordList);
                    iMnjxPnrEiService.removeByIds(pnrEiList.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));
                }
            }
        }

        // 同时还可能包含旅客级别的（当旅客组成是成人、儿童、婴儿时，成人、儿童的fc是旅客级别的，婴儿是pnr级别的，因此上面处理完婴儿的pnr级别，这里还需要处理成人、儿童的旅客级别）
        // 旅客级别的直接删除对应出票旅客的nmFc和nmEi
        // 指定旅客出票时
        if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
            List<MnjxNmFc> toDeleteNmFcList = new ArrayList<>();
            List<MnjxPnrRecord> toDeleteRecordList = new ArrayList<>();
            List<MnjxNmEi> toDeleteNmEiList = new ArrayList<>();
            List<MnjxNmOi> toDeleteNmOiList = new ArrayList<>();
            List<MnjxNmRmk> toDeleteNmRmkList = new ArrayList<>();
            for (MnjxPnrNm passenger : passengers) {
                // size大于1时只能是同时指定了该成人旅客和其携带的婴儿
                if (dto.getIssue().getIssueItems().size() > 1) {
                    List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                            .list();
                    if (CollUtil.isNotEmpty(nmFcList)) {
                        List<MnjxPnrRecord> nmFcRecordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .in(MnjxPnrRecord::getPnrIndex, nmFcList.stream().map(MnjxNmFc::getPnrIndex).collect(Collectors.toList()))
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .list();
                        toDeleteNmFcList.addAll(nmFcList);
                        toDeleteRecordList.addAll(nmFcRecordList);
                    }
                    List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                            .list();
                    if (CollUtil.isNotEmpty(nmEiList)) {
                        List<MnjxPnrRecord> nmEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .in(MnjxPnrRecord::getPnrIndex, nmEiList.stream().map(MnjxNmEi::getPnrIndex).collect(Collectors.toList()))
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .list();
                        toDeleteNmEiList.addAll(nmEiList);
                        toDeleteRecordList.addAll(nmEiRecordList);
                    }
                    List<MnjxNmOi> nmOiList = iMnjxNmOiService.lambdaQuery()
                            .eq(MnjxNmOi::getPnrNmId, passenger.getPnrNmId())
                            .list();
                    if (CollUtil.isNotEmpty(nmOiList)) {
                        List<MnjxPnrRecord> nmOiRecordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .in(MnjxPnrRecord::getPnrIndex, nmOiList.stream().map(MnjxNmOi::getPnrIndex).collect(Collectors.toList()))
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .list();
                        toDeleteNmOiList.addAll(nmOiList);
                        toDeleteRecordList.addAll(nmOiRecordList);
                    }
                    List<MnjxNmRmk> nmRmkList = iMnjxNmRmkService.lambdaQuery()
                            .eq(MnjxNmRmk::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmRmk::getRmkName, "OT")
                            .list();
                    if (CollUtil.isNotEmpty(nmRmkList)) {
                        List<MnjxPnrRecord> nmRmkRecordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .in(MnjxPnrRecord::getPnrIndex, nmRmkList.stream().map(MnjxNmRmk::getPnrIndex).collect(Collectors.toList()))
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .list();
                        toDeleteNmRmkList.addAll(nmRmkList);
                        toDeleteRecordList.addAll(nmRmkRecordList);
                    }
                }
                // 只指定了婴儿，只删除婴儿的nmFc和nmEi
                else if ("INF".equals(dto.getIssue().getIssueItems().get(0).getPassengerType())) {
                    MnjxNmFc xnNmFc = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmFc::getIsBaby, 1)
                            .one();
                    if (xnNmFc != null) {
                        MnjxPnrRecord xnNmFcRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, xnNmFc.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmFcList.add(xnNmFc);
                        toDeleteRecordList.add(xnNmFcRecord);
                    }
                    MnjxNmEi xnNmEi = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                            .like(MnjxNmEi::getInputValue, "/IN/")
                            .one();
                    if (xnNmEi != null) {
                        MnjxPnrRecord xnNmEiRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, xnNmEi.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmEiList.add(xnNmEi);
                        toDeleteRecordList.add(xnNmEiRecord);
                    }
                    MnjxNmOi xnNmOi = iMnjxNmOiService.lambdaQuery()
                            .eq(MnjxNmOi::getPnrNmId, passenger.getPnrNmId())
                            .like(MnjxNmOi::getOiInfo, "/IN/")
                            .one();
                    if (xnNmOi != null) {
                        MnjxPnrRecord xnNmOiRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, xnNmOi.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmOiList.add(xnNmOi);
                        toDeleteRecordList.add(xnNmOiRecord);
                    }
                    MnjxNmRmk xnNmRmk = iMnjxNmRmkService.lambdaQuery()
                            .eq(MnjxNmRmk::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmRmk::getRmkName, "OT")
                            .like(MnjxNmRmk::getInputValue, "/IN")
                            .one();
                    if (xnNmRmk != null) {
                        MnjxPnrRecord xnNmRmkRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, xnNmRmk.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmRmkList.add(xnNmRmk);
                        toDeleteRecordList.add(xnNmRmkRecord);
                    }
                }
                // 只指定了成人或儿童
                else {
                    MnjxNmFc nmFc = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmFc::getIsBaby, 0)
                            .one();
                    if (nmFc != null) {
                        MnjxPnrRecord nmFcRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, nmFc.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmFcList.add(nmFc);
                        toDeleteRecordList.add(nmFcRecord);
                    }
                    MnjxNmEi nmEi = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                            .notLike(MnjxNmEi::getInputValue, "/IN/")
                            .one();
                    if (nmEi != null) {
                        MnjxPnrRecord nmEiRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, nmEi.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmEiList.add(nmEi);
                        toDeleteRecordList.add(nmEiRecord);
                    }
                    MnjxNmOi nmOi = iMnjxNmOiService.lambdaQuery()
                            .eq(MnjxNmOi::getPnrNmId, passenger.getPnrNmId())
                            .notLike(MnjxNmOi::getOiInfo, "/IN/")
                            .one();
                    if (nmOi != null) {
                        MnjxPnrRecord nmOiRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, nmOi.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmOiList.add(nmOi);
                        toDeleteRecordList.add(nmOiRecord);
                    }
                    MnjxNmRmk nmRmk = iMnjxNmRmkService.lambdaQuery()
                            .eq(MnjxNmRmk::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmRmk::getRmkName, "OT")
                            .notLike(MnjxNmRmk::getInputValue, "/IN")
                            .one();
                    if (nmRmk != null) {
                        MnjxPnrRecord nmRmkRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, nmRmk.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmRmkList.add(nmRmk);
                        toDeleteRecordList.add(nmRmkRecord);
                    }
                }
            }
            if (CollUtil.isNotEmpty(toDeleteRecordList)) {
                toDeleteRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(toDeleteRecordList);
            }
            if (CollUtil.isNotEmpty(toDeleteNmEiList)) {
                iMnjxNmEiService.removeByIds(toDeleteNmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(toDeleteNmFcList)) {
                iMnjxNmFcService.removeByIds(toDeleteNmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(toDeleteNmOiList)) {
                iMnjxNmOiService.removeByIds(toDeleteNmOiList.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(toDeleteNmRmkList)) {
                iMnjxNmRmkService.removeByIds(toDeleteNmRmkList.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
            }
        } else {
            List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                    .in(MnjxNmFc::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .list();
            if (CollUtil.isNotEmpty(nmFcList)) {
                List<MnjxPnrRecord> nmFcRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, nmFcList.stream().map(MnjxNmFc::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                nmFcRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(nmFcRecordList);
                iMnjxNmFcService.removeByIds(nmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
            }
            List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                    .in(MnjxNmEi::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .list();
            if (CollUtil.isNotEmpty(nmEiList)) {
                List<MnjxPnrRecord> nmEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, nmEiList.stream().map(MnjxNmEi::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                nmEiRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(nmEiRecordList);
                iMnjxNmEiService.removeByIds(nmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
            }
            List<MnjxNmOi> nmOiList = iMnjxNmOiService.lambdaQuery()
                    .in(MnjxNmOi::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .list();
            if (CollUtil.isNotEmpty(nmOiList)) {
                List<MnjxPnrRecord> nmOiRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, nmOiList.stream().map(MnjxNmOi::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                nmOiRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(nmOiRecordList);
                iMnjxNmOiService.removeByIds(nmOiList.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList()));
            }
            List<MnjxNmRmk> nmRmkList = iMnjxNmRmkService.lambdaQuery()
                    .in(MnjxNmRmk::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .eq(MnjxNmRmk::getRmkName, "OT")
                    .list();
            if (CollUtil.isNotEmpty(nmRmkList)) {
                List<MnjxPnrRecord> nmRmkRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, nmRmkList.stream().map(MnjxNmRmk::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                nmRmkRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(nmRmkRecordList);
                iMnjxNmRmkService.removeByIds(nmRmkList.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
            }
        }
    }

    private void deletePartialFcEiOiOtRmk(IssueTicketDto dto, MnjxPnr pnr, String newAtNo) {
        List<MnjxPnrRecord> pnrRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .list();
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        List<String> pnrNmIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        // 先查pnr级别的fc和ei，如果有，需要判断pnr中对应旅客类型的旅客是否全部出完票
        // 判断pnr中所有的婴儿是否都已经出票了
        boolean isAllXnIssued = false;
        boolean isAllAdtIssued = false;
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIdList)
                .list();
        if (CollUtil.isNotEmpty(nmXnList)) {
            List<MnjxPnrNmTn> existingXnTickets = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                    .list();
            if (existingXnTickets.size() == nmXnList.size()) {
                isAllXnIssued = true;
            }
        } else {
            isAllXnIssued = true;
        }
        List<MnjxPnrNmTn> existingNmTickets = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                .list();
        if (existingNmTickets.size() == pnrNmIdList.size()) {
            isAllAdtIssued = true;
        }

        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();
        // 处理pnrFc
        if (CollUtil.isNotEmpty(pnrFcList)) {
            if (pnrFcList.stream().anyMatch(p -> p.getIsBaby() == 1)) {
                List<MnjxPnrFc> inftPnrFcList = pnrFcList.stream()
                        .filter(p -> p.getIsBaby() == 1)
                        .collect(Collectors.toList());
                if (isAllXnIssued) {
                    // 删除婴儿的pnrFc
                    List<MnjxPnrRecord> inftPnrFcRecordList = pnrRecordList.stream()
                            .filter(r -> inftPnrFcList.stream().anyMatch(f -> f.getPnrIndex().intValue() == r.getPnrIndex()))
                            .collect(Collectors.toList());
                    inftPnrFcRecordList.forEach(f -> {
                        f.setChangeMark("X");
                        f.setChangeAtNo(newAtNo);
                    });
                    iMnjxPnrRecordService.updateBatchById(inftPnrFcRecordList);
                    iMnjxPnrFcService.removeByIds(inftPnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));
                }
            }
            if (pnrFcList.stream().anyMatch(p -> p.getIsBaby() == 0)) {
                if (isAllAdtIssued) {
                    List<MnjxPnrFc> adtPnrFcList = pnrFcList.stream()
                            .filter(p -> p.getIsBaby() == 0)
                            .collect(Collectors.toList());
                    List<MnjxPnrRecord> adtPnrFcRecordList = pnrRecordList.stream()
                            .filter(r -> adtPnrFcList.stream().anyMatch(f -> f.getPnrIndex().intValue() == r.getPnrIndex()))
                            .collect(Collectors.toList());
                    adtPnrFcRecordList.forEach(f -> {
                        f.setChangeMark("X");
                        f.setChangeAtNo(newAtNo);
                    });
                    iMnjxPnrRecordService.updateBatchById(adtPnrFcRecordList);
                    iMnjxPnrFcService.removeByIds(adtPnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));
                }
            }
        }
        // 处理pnrEi，和pnrFc一样的处理方式
        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isNotEmpty(pnrEiList)) {
            if (pnrEiList.stream().anyMatch(p -> p.getInputValue().contains("/IN"))) {
                List<MnjxPnrEi> inftPnrEiList = pnrEiList.stream()
                        .filter(p -> p.getInputValue().contains("/IN"))
                        .collect(Collectors.toList());
                if (isAllXnIssued) {
                    // 删除婴儿的pnrEi
                    List<MnjxPnrRecord> inftPnrEiRecordList = pnrRecordList.stream()
                            .filter(r -> inftPnrEiList.stream().anyMatch(f -> f.getPnrIndex().intValue() == r.getPnrIndex()))
                            .collect(Collectors.toList());
                    inftPnrEiRecordList.forEach(f -> {
                        f.setChangeMark("X");
                        f.setChangeAtNo(newAtNo);
                    });
                    iMnjxPnrRecordService.updateBatchById(inftPnrEiRecordList);
                    iMnjxPnrEiService.removeByIds(inftPnrEiList.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));
                }
            }
            if (pnrEiList.stream().anyMatch(p -> !p.getInputValue().contains("/IN"))) {
                if (isAllAdtIssued) {
                    List<MnjxPnrEi> adtPnrEiList = pnrEiList.stream()
                            .filter(p -> !p.getInputValue().contains("/IN"))
                            .collect(Collectors.toList());
                    List<MnjxPnrRecord> adtPnrEiRecordList = pnrRecordList.stream()
                            .filter(r -> adtPnrEiList.stream().anyMatch(f -> f.getPnrIndex().intValue() == r.getPnrIndex()))
                            .collect(Collectors.toList());
                    adtPnrEiRecordList.forEach(f -> {
                        f.setChangeMark("X");
                        f.setChangeAtNo(newAtNo);
                    });
                    iMnjxPnrRecordService.updateBatchById(adtPnrEiRecordList);
                    iMnjxPnrEiService.removeByIds(adtPnrEiList.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));
                }
            }
        }

        // 查nmFc和nmEi，删除本次部分出票中对应旅客序号的nmFc和nmEi
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .in(MnjxNmFc::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .in(MnjxNmEi::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmOi> nmOiList = iMnjxNmOiService.lambdaQuery()
                .in(MnjxNmOi::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmRmk> nmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, pnrNmIdList)
                .eq(MnjxNmRmk::getRmkName, "OT")
                .list();
        List<MnjxPnrRecord> recordList = new ArrayList<>();
        List<MnjxNmFc> toDeleteNmFcList = new ArrayList<>();
        List<MnjxNmEi> toDeleteNmEiList = new ArrayList<>();
        List<MnjxNmOi> toDeleteNmOiList = new ArrayList<>();
        List<MnjxNmRmk> toDeleteNmRmkList = new ArrayList<>();
        List<IssueTicketDto.IssueItemDto> issueItems = dto.getIssue().getIssueItems();
        for (IssueTicketDto.IssueItemDto issueItem : issueItems) {
            MnjxPnrNm pnrNm = pnrNmList.stream()
                    .filter(p -> p.getPsgIndex() == Integer.parseInt(issueItem.getPassengerId().replace("P", "")))
                    .collect(Collectors.toList())
                    .get(0);
            if ("INF".equals(issueItem.getPassengerType())) {
                if (CollUtil.isNotEmpty(nmFcList) && nmFcList.stream().anyMatch(n -> n.getPnrNmId().equals(pnrNm.getPnrNmId()) && n.getIsBaby() == 1)) {
                    nmFcList.stream()
                            .filter(n -> n.getPnrNmId().equals(pnrNm.getPnrNmId()) && n.getIsBaby() == 1)
                            .forEach(n -> {
                                pnrRecordList.stream()
                                        .filter(r -> r.getPnrIndex().intValue() == n.getPnrIndex())
                                        .forEach(r -> {
                                            r.setChangeMark("X");
                                            r.setChangeAtNo(newAtNo);
                                            recordList.add(r);
                                        });
                                toDeleteNmFcList.add(n);
                            });
                }
                if (CollUtil.isNotEmpty(nmEiList) && nmEiList.stream().anyMatch(e -> e.getPnrNmId().equals(pnrNm.getPnrNmId()) && e.getInputValue().contains("/IN"))) {
                    nmEiList.stream()
                            .filter(e -> e.getPnrNmId().equals(pnrNm.getPnrNmId()) && e.getInputValue().contains("/IN"))
                            .forEach(e -> {
                                pnrRecordList.stream()
                                        .filter(r -> r.getPnrIndex().intValue() == e.getPnrIndex())
                                        .forEach(r -> {
                                            r.setChangeMark("X");
                                            r.setChangeAtNo(newAtNo);
                                            recordList.add(r);
                                        });
                                toDeleteNmEiList.add(e);
                            });
                }
                if (CollUtil.isNotEmpty(nmOiList) && nmOiList.stream().anyMatch(e -> e.getPnrNmId().equals(pnrNm.getPnrNmId()) && e.getOiInfo().contains("/IN"))) {
                    nmOiList.stream()
                            .filter(e -> e.getPnrNmId().equals(pnrNm.getPnrNmId()) && e.getOiInfo().contains("/IN"))
                            .forEach(e -> {
                                pnrRecordList.stream()
                                        .filter(r -> r.getPnrIndex().intValue() == e.getPnrIndex())
                                        .forEach(r -> {
                                            r.setChangeMark("X");
                                            r.setChangeAtNo(newAtNo);
                                            recordList.add(r);
                                        });
                                toDeleteNmOiList.add(e);
                            });
                }
                if (CollUtil.isNotEmpty(nmRmkList) && nmRmkList.stream().anyMatch(e -> e.getPnrNmId().equals(pnrNm.getPnrNmId()) && e.getInputValue().contains("/IN"))) {
                    nmRmkList.stream()
                            .filter(e -> e.getPnrNmId().equals(pnrNm.getPnrNmId()) && e.getInputValue().contains("/IN"))
                            .forEach(e -> {
                                pnrRecordList.stream()
                                        .filter(r -> r.getPnrIndex().intValue() == e.getPnrIndex())
                                        .forEach(r -> {
                                            r.setChangeMark("X");
                                            r.setChangeAtNo(newAtNo);
                                            recordList.add(r);
                                        });
                                toDeleteNmRmkList.add(e);
                            });
                }
            }
        }
        iMnjxPnrRecordService.updateBatchById(recordList);
        iMnjxNmFcService.removeByIds(toDeleteNmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
        iMnjxNmEiService.removeByIds(toDeleteNmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
        iMnjxNmOiService.removeByIds(toDeleteNmOiList.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList()));
        iMnjxNmRmkService.removeByIds(toDeleteNmRmkList.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
    }

    private void deleteAllFcEiOiOtRmk(MnjxPnr pnr, List<MnjxPnrNm> passengers, String newAtNo) {
        // pnr级别
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isNotEmpty(pnrFcList)) {
            List<MnjxPnrRecord> pnrFcRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .in(MnjxPnrRecord::getPnrIndex, pnrFcList.stream().map(MnjxPnrFc::getPnrIndex).collect(Collectors.toList()))
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .list();
            pnrFcRecordList.forEach(f -> {
                f.setChangeMark("X");
                f.setChangeAtNo(newAtNo);
            });
            iMnjxPnrRecordService.updateBatchById(pnrFcRecordList);
            iMnjxPnrFcService.removeByIds(pnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));
        }

        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isNotEmpty(pnrEiList)) {
            List<MnjxPnrRecord> pnrEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .in(MnjxPnrRecord::getPnrIndex, pnrEiList.stream().map(MnjxPnrEi::getPnrIndex).collect(Collectors.toList()))
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .list();
            pnrEiRecordList.forEach(f -> {
                f.setChangeMark("X");
                f.setChangeAtNo(newAtNo);
            });
            iMnjxPnrRecordService.updateBatchById(pnrEiRecordList);
            iMnjxPnrEiService.removeByIds(pnrEiList.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));
        }

        // 旅客级别
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .in(MnjxNmFc::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                .list();
        if (CollUtil.isNotEmpty(nmFcList)) {
            List<MnjxPnrRecord> nmFcRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .in(MnjxPnrRecord::getPnrIndex, nmFcList.stream().map(MnjxNmFc::getPnrIndex).collect(Collectors.toList()))
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .list();
            nmFcRecordList.forEach(f -> {
                f.setChangeMark("X");
                f.setChangeAtNo(newAtNo);
            });
            iMnjxPnrRecordService.updateBatchById(nmFcRecordList);
            iMnjxNmFcService.removeByIds(nmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
        }

        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .in(MnjxNmEi::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                .list();
        if (CollUtil.isNotEmpty(nmEiList)) {
            List<MnjxPnrRecord> nmEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .in(MnjxPnrRecord::getPnrIndex, nmEiList.stream().map(MnjxNmEi::getPnrIndex).collect(Collectors.toList()))
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .list();
            nmEiRecordList.forEach(f -> {
                f.setChangeMark("X");
                f.setChangeAtNo(newAtNo);
            });
            iMnjxPnrRecordService.updateBatchById(nmEiRecordList);
            iMnjxNmEiService.removeByIds(nmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
        }

        List<MnjxNmOi> nmOiList = iMnjxNmOiService.lambdaQuery()
                .in(MnjxNmOi::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                .list();
        if (CollUtil.isNotEmpty(nmOiList)) {
            List<MnjxPnrRecord> nmOiRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .in(MnjxPnrRecord::getPnrIndex, nmOiList.stream().map(MnjxNmOi::getPnrIndex).collect(Collectors.toList()))
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .list();
            nmOiRecordList.forEach(f -> {
                f.setChangeMark("X");
                f.setChangeAtNo(newAtNo);
            });
            iMnjxPnrRecordService.updateBatchById(nmOiRecordList);
            iMnjxNmOiService.removeByIds(nmOiList.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList()));
        }

        List<MnjxNmRmk> nmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                .eq(MnjxNmRmk::getRmkName, "OT")
                .list();
        if (CollUtil.isNotEmpty(nmRmkList)) {
            List<MnjxPnrRecord> nmRmkRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .in(MnjxPnrRecord::getPnrIndex, nmRmkList.stream().map(MnjxNmRmk::getPnrIndex).collect(Collectors.toList()))
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .list();
            nmRmkRecordList.forEach(f -> {
                f.setChangeMark("X");
                f.setChangeAtNo(newAtNo);
            });
            iMnjxPnrRecordService.updateBatchById(nmRmkRecordList);
            iMnjxNmRmkService.removeByIds(nmRmkList.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
        }
    }

    /**
     * 生成TK记录
     */
    private void generateTkRecord(MnjxPnr pnr) {
        // 检查是否已存在T类型的TK记录
        long count = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrTk::getPnrTkType, "T")
                .count();

        if (count == 0) {
            MnjxPnrTk tk = new MnjxPnrTk();
            tk.setPnrTkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            tk.setPnrId(pnr.getPnrId());
            tk.setPnrTkType("T");
            tk.setPlanEtdzDate(DateUtil.today());
            tk.setInputValue("T");

            iMnjxPnrTkService.save(tk);
        }
    }

    /**
     * 生成RMK记录
     */
    private void generateRmkRecord(MnjxPnr pnr, MnjxOffice office) {
        // 只生成一次，部分出票时后续出票不重复生成
        boolean hasTj = false;
        boolean hasTr = false;
        Integer tjCount = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrRmk::getRmkName, "TJ")
                .count();
        if (tjCount > 0) {
            hasTj = true;
        }
        Integer trCount = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrRmk::getRmkName, "TR")
                .count();
        if (trCount > 0) {
            hasTr = true;
        }
        String tjInfo = "RMK TJ " + office.getOfficeNo();
        String trInfo = "RMK TR " + office.getOfficeNo();

        // 生成RMK TJ记录
        if (!hasTj) {
            MnjxPnrRmk rmkTj = new MnjxPnrRmk();
            rmkTj.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            rmkTj.setPnrId(pnr.getPnrId());
            rmkTj.setRmkName("TJ");
            rmkTj.setRmkInfo(tjInfo);
            rmkTj.setInputValue(tjInfo);

            iMnjxPnrRmkService.save(rmkTj);
        }

        // 生成RMK TR记录
        if (!hasTr) {
            MnjxPnrRmk rmkTr = new MnjxPnrRmk();
            rmkTr.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            rmkTr.setPnrId(pnr.getPnrId());
            rmkTr.setRmkName("TR");
            rmkTr.setRmkInfo(trInfo);
            rmkTr.setInputValue(trInfo);

            iMnjxPnrRmkService.save(rmkTr);
        }
    }

    /**
     * 生成psgCki和psgSeat数据
     */
    private void generatePsgCkiAndSeat(MnjxPnr pnr, List<MnjxPnrNm> passengers) {
        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrNm passenger : passengers) {
            for (MnjxPnrSeg segment : segments) {
                if (!"SA".equals(segment.getPnrSegType())) {
                    // 生成psgCki记录
                    MnjxPsgCki psgCki = new MnjxPsgCki();
                    psgCki.setPsgCkiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    psgCki.setPnrNmId(passenger.getPnrNmId());
                    psgCki.setPnrSegNo(StrUtil.toString(segment.getPnrSegNo()));
                    psgCki.setCabinClass(segment.getCabinClass());
                    psgCki.setSellCabin(segment.getSellCabin());
                    psgCki.setAbdStatusInfi("0");
                    psgCki.setCkiStatus("NACC"); // 未值机

                    iMnjxPsgCkiService.save(psgCki);

                    // 生成psgSeat记录
                    MnjxPsgSeat psgSeat = new MnjxPsgSeat();
                    psgSeat.setPsgSeatId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    psgSeat.setPsgCkiId(psgCki.getPsgCkiId());

                    iMnjxPsgSeatService.save(psgSeat);
                }
            }
        }
    }

    /**
     * 生成客票操作记录
     */
    private void generateTicketOperateRecord(List<MnjxPnrNmTicket> ticketList, MnjxSi currentUser) {
        List<MnjxTicketOperateRecord> ticketOperateRecordList = new ArrayList<>();
        for (MnjxPnrNmTicket ticket : ticketList) {
            MnjxTicketOperateRecord record = new MnjxTicketOperateRecord();
            record.setTicketOperateRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
            record.setTicketNo(ticket.getTicketNo());
            record.setTicketStatus1(ticket.getTicketStatus1());
            record.setTicketStatus2(ticket.getTicketStatus2());
            record.setOperateTime(DateUtil.now());
            record.setSettlementCode(ticket.getTicketNo().substring(0, 3));
            record.setSiNo(currentUser.getSiNo());

            ticketOperateRecordList.add(record);
        }
        iMnjxTicketOperateRecordService.saveBatch(ticketOperateRecordList);
    }

    /**
     * PNR重新排序和封口
     */
    private void reorderAndSeal(MnjxPnr pnr, String newAtNo, MnjxSi currentUser) {
        // 重新排序所有项的pnr_index
        List<MnjxPnrRecord> recordList = new ArrayList<>();
        iUpdatePnrService.reorderAllPnrIndexesAndUpdate(pnr, recordList);

        // 生成封口记录
        this.generateSealingRecord(pnr, recordList, newAtNo, currentUser);
    }

    /**
     * 生成封口记录
     */
    private void generateSealingRecord(MnjxPnr pnr, List<MnjxPnrRecord> recordList, String atNo, MnjxSi currentUser) {
        // 生成封口记录
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(atNo);
        pnrAt.setAtSiId(currentUser.getSiId());
        pnrAt.setAtDateTime(new Date());

        iMnjxPnrAtService.save(pnrAt);

        // 历史记录，先删除以前changeMark为空的，再批量添加这次封口所有已排好序的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();
        // 保存历史记录
        iMnjxPnrRecordService.saveBatch(recordList);
    }

    /**
     * 构建返回结果
     */
    private IssueTicketVo buildResult(IssueTicketDto dto) {
        IssueTicketVo result = new IssueTicketVo();

        List<IssueTicketVo.BopBookTicketResVo> bookTicketResVoList = new ArrayList<>();
        if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
            for (IssueTicketDto.IssueItemDto issueItem : dto.getIssue().getIssueItems()) {
                // 构建BOP出票结果
                IssueTicketVo.BopBookTicketResVo bopRes = new IssueTicketVo.BopBookTicketResVo();
                bopRes.setSuccessType(true);
                bopRes.setPassenger(issueItem.getPassengerType());
                String passengerName = new String(Base64.getDecoder().decode(issueItem.getName()), StandardCharsets.UTF_8);
                bopRes.setPassengerNames(passengerName);
                bopRes.setHasTkneFail(false);
                bopRes.setTxnTraceKey("XXXSAT" + System.currentTimeMillis());
                bopRes.setIssueDate(DateUtil.now());
                bopRes.setDescription(null);

                bookTicketResVoList.add(bopRes);
            }
        } else {
            // 构建BOP出票结果
            IssueTicketVo.BopBookTicketResVo bopRes = new IssueTicketVo.BopBookTicketResVo();
            bopRes.setSuccessType(true);
            bopRes.setPassenger("");
            bopRes.setPassengerNames("");
            bopRes.setHasTkneFail(false);
            bopRes.setTxnTraceKey("XXXSAT" + System.currentTimeMillis());
            bopRes.setIssueDate(DateUtil.now());
            bopRes.setDescription(null);

            bookTicketResVoList.add(bopRes);
        }


        result.setBopBookTicketRes(bookTicketResVoList);

        // 构建出票结果
        IssueTicketVo.IssueResVo issueRes = new IssueTicketVo.IssueResVo();
        issueRes.setResult("2");
        issueRes.setTxnTraceKey(null);

        result.setIssueRes(issueRes);
        result.setValidateIetFail(false);
        result.setCheckSameName(false);

        return result;
    }
}
