package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrRecord;
import com.swcares.obj.dto.UpdatePnrDto;
import com.swcares.obj.vo.UpdatePnrVo;

import java.util.List;

/**
 * 更新PNR服务接口
 *
 * <AUTHOR>
 * @date 2025/5/26 16:00
 */
public interface IUpdatePnrService {

    /**
     * 更新PNR
     *
     * @param dto 更新PNR请求参数
     * @return 更新结果
     * @throws SguiResultException 异常
     */
    UpdatePnrVo updatePnr(UpdatePnrDto dto) throws SguiResultException;

    void reorderAllPnrIndexesAndUpdate(MnjxPnr pnr, List<MnjxPnrRecord> recordList);

    void batchExecuteOperations(List<Object> addList, List<Object> updateList, List<Object> deleteList);
}
