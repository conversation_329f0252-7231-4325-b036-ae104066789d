package com.swcares.service.sor.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.QueryFareDomesticDto;
import com.swcares.obj.dto.QueryFareDomesticDto.SelectedPassenger;
import com.swcares.obj.dto.QueryFareDomesticDto.VoyageSegment;
import com.swcares.obj.vo.QueryFareDomesticVo;
import com.swcares.obj.vo.QueryFareDomesticVo.*;
import com.swcares.service.IMnjxCityService;
import com.swcares.service.IMnjxCnYqService;
import com.swcares.service.IMnjxStandardPatService;
import com.swcares.service.ISguiCommonService;
import com.swcares.service.sor.IFareDomesticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国内运价服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
@Slf4j
@Service
public class FareDomesticServiceImpl implements IFareDomesticService {

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxCnYqService iMnjxCnYqService;

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Override
    public QueryFareDomesticVo queryFareDomestic(QueryFareDomesticDto dto) throws SguiResultException {
        // 参数校验
        if (CollUtil.isEmpty(dto.getSelectedPassengers())) {
            throw new SguiResultException("旅客信息不能为空");
        }
        if (CollUtil.isEmpty(dto.getVoyages())) {
            throw new SguiResultException("航段信息不能为空");
        }

        // 获取基建燃油费用配置
        MnjxCnYq cnYq = this.getCnYq();
        if (cnYq == null) {
            throw new SguiResultException("未找到基建燃油费用配置");
        }

        // 创建返回对象
        QueryFareDomesticVo vo = new QueryFareDomesticVo();
        vo.setFareType("A"); // 运价类型始终设置为A
        Map<String, List<FareItem>> fareItems = new HashMap<>();

        // 处理每种旅客类型
        for (SelectedPassenger passenger : dto.getSelectedPassengers()) {
            List<FareItem> fareItemList = new ArrayList<>();
            FareItem fareItem = this.calculateFareItem(passenger, dto.getVoyages(), cnYq, dto.getOption());
            fareItemList.add(fareItem);
            fareItems.put(passenger.getPassengerType(), fareItemList);
        }

        vo.setFareItems(fareItems);
        return vo;
    }

    /**
     * 计算运价项目
     *
     * @param passenger 旅客信息
     * @param voyages   航段信息
     * @param cnYq      基建燃油费用配置
     * @param option    运价选项
     * @return 运价项目
     * @throws SguiResultException 异常
     */
    private FareItem calculateFareItem(SelectedPassenger passenger, List<List<VoyageSegment>> voyages, MnjxCnYq cnYq, String option) throws SguiResultException {
        FareItem fareItem = new FareItem();
        String passengerType = passenger.getPassengerType();

        // 设置货币
        fareItem.setCurrency("CNY");
        fareItem.setTotalAmountCurrency("CNY");

        // 设置EI信息
//        fareItem.setEi("EI/Q/GAITUISHOUFEI改退收费");

        // 计算所有航段的总票面价和航段信息
        List<SegInfo> segInfos = new ArrayList<>();
        BigDecimal totalTicketAmount = BigDecimal.ZERO;

        // 处理每个航段组
        for (List<VoyageSegment> voyageGroup : voyages) {
            for (VoyageSegment voyage : voyageGroup) {
                SegInfo segInfo = this.createSegInfo(voyage, passengerType);
                segInfos.add(segInfo);

                // 累加票面价
                if (StrUtil.isNotEmpty(segInfo.getTicketAmount())) {
                    totalTicketAmount = totalTicketAmount.add(new BigDecimal(segInfo.getTicketAmount()));
                }
            }
        }

        fareItem.setSegInfos(segInfos);

        // 根据旅客类型计算折扣后的票面价
        BigDecimal discountedTicketAmount = this.calculateDiscountedTicketAmount(totalTicketAmount, passengerType, cnYq, segInfos);
        fareItem.setTktAmount(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP).toString());

        // 计算基建费和燃油费
        BigDecimal fundAmount = this.calculateFundAmount(passengerType, cnYq, segInfos);
        BigDecimal fuelAmount = this.calculateFuelAmount(passengerType, cnYq, segInfos);

        fareItem.setFund(fundAmount.setScale(2, RoundingMode.HALF_UP).toString());
        fareItem.setFuel(fuelAmount.setScale(2, RoundingMode.HALF_UP).toString());

        // 计算航段数量
        int segmentCount = segInfos.size();

        // 设置代理费
        if ("INF".equals(passengerType)) {
            fareItem.setRmkcms(null);
        } else {
            // 根据航段数量设置代理费
            if (segmentCount <= 1) {
                fareItem.setRmkcms("5.00");
            } else {
                StringBuilder rmkcms = new StringBuilder();
                for (int i = 0; i < segmentCount; i++) {
                    rmkcms.append("5.00");
                    if (i < segmentCount - 1) {
                        rmkcms.append("+");
                    }
                }
                fareItem.setRmkcms(rmkcms.toString());
            }
        }

        // 计算总税费
        BigDecimal totalTaxAmount = fundAmount.add(fuelAmount);
        fareItem.setTotalTaxAmount(totalTaxAmount.setScale(2, RoundingMode.HALF_UP).toString());

        // 计算总价
        BigDecimal totalAmount = discountedTicketAmount.add(totalTaxAmount);
        fareItem.setTotalAmount(totalAmount.setScale(1, RoundingMode.HALF_UP).toString());

        // 设置税费信息
        List<TaxDom> taxDomList = this.createTaxDomList(fundAmount, fuelAmount, segInfos);
        fareItem.setTaxDom(taxDomList);

        // 构造FC、FN、FP信息
        fareItem.setFc(this.buildFcInfo(segInfos, passengerType, discountedTicketAmount, option));
        fareItem.setFn(this.buildFnInfo(passengerType, discountedTicketAmount, fundAmount, fuelAmount, option));
        fareItem.setFp(this.buildFpInfo(passengerType, option));
        fareItem.setEi(this.buildEiInfo(passengerType));

        return fareItem;
    }

    private String buildEiInfo(String passengerType) {
        if ("INF".equals(passengerType)) {
            return "EI/IN/Q/GAITUISHOUFEI改退收费";
        } else {
            return "EI/Q/GAITUISHOUFEI改退收费";
        }
    }

    /**
     * 创建航段信息
     *
     * @param voyage        航段信息
     * @param passengerType 旅客类型
     * @return 航段信息
     * @throws SguiResultException 异常
     */
    private SegInfo createSegInfo(VoyageSegment voyage, String passengerType) throws SguiResultException {
        SegInfo segInfo = new SegInfo();

        // 设置基本信息
        segInfo.setDepartureDateTime(voyage.getDepartureDateTime());
        segInfo.setCompanyCode(voyage.getCompanyCode());
        segInfo.setDepartureCode(voyage.getDepartureAirport());
        segInfo.setArrivalCode(voyage.getArrivalAirport());
        segInfo.setResBookDesigCode(voyage.getCabinCode());
        segInfo.setFlightNumber(voyage.getFlightNumber());

        // 设置规则ID和规则信息
        List<String> ruleIds = new ArrayList<>();
        ruleIds.add("AV1H");
        segInfo.setRuleIds(ruleIds);

        List<RuleInfo> ruleInfoList = new ArrayList<>();
        RuleInfo ruleInfo = new RuleInfo();
        ruleInfo.setRuleId("AV1H");
        ruleInfo.setLocation("PUB");
        ruleInfoList.add(ruleInfo);
        segInfo.setRuleInfoList(ruleInfoList);

        // 获取销售舱位的销售运价
        BigDecimal ticketAmount = this.getTicketAmountForSegment(voyage);
        segInfo.setTicketAmount(ticketAmount.setScale(2, RoundingMode.HALF_UP).toString());

        // 设置运价基础代码
        String fareBasisCode = voyage.getCabinCode();
        if ("CHD".equals(passengerType)) {
            fareBasisCode += "CH";
        } else if ("INF".equals(passengerType)) {
            fareBasisCode += "IN";
        }
        segInfo.setFareBasisCodes(fareBasisCode);

        return segInfo;
    }

    /**
     * 模拟获取航段的票面价（实际应该查询运价表）
     */
    private BigDecimal getTicketAmountForSegment(VoyageSegment segment) {
        String cabinCode = segment.getCabinCode();
        List<MnjxOpenCabin> openCabinList = iSguiCommonService.getOpenCabinListByFlightNo(
                segment.getCompanyCode() + segment.getFlightNumber(),
                segment.getDepartureDateTime().substring(0, 10),
                segment.getDepartureAirport(),
                segment.getArrivalAirport()
        );
        Integer sellCabinPrice = openCabinList.stream()
                .filter(o -> o.getSellCabin().equals(cabinCode))
                .collect(Collectors.toList())
                .get(0)
                .getSellCabinPrice();
        return new BigDecimal(sellCabinPrice);
    }

    /**
     * 计算折扣后的票面价
     *
     * @param ticketAmount  票面价
     * @param passengerType 旅客类型
     * @param cnYq          基建燃油费用配置
     * @return 折扣后的票面价
     */
    private BigDecimal calculateDiscountedTicketAmount(BigDecimal ticketAmount, String passengerType, MnjxCnYq cnYq, List<SegInfo> segInfos) {
        if (StrUtil.equalsAny(passengerType, "CHD", "GM", "JC")) {
            // 儿童、军警残票价为成人票价的50%
            segInfos.forEach(s -> s.setTicketAmount(new BigDecimal(s.getTicketAmount()).multiply(new BigDecimal(cnYq.getChildDiscount())).setScale(2, RoundingMode.HALF_UP).toString()));
            return ticketAmount.multiply(new BigDecimal(cnYq.getChildDiscount()));
        } else if ("INF".equals(passengerType)) {
            // 婴儿票价为成人票价的10%
            segInfos.forEach(s -> s.setTicketAmount(new BigDecimal(s.getTicketAmount()).multiply(new BigDecimal(cnYq.getInfiDiscount())).setScale(2, RoundingMode.HALF_UP).toString()));
            return ticketAmount.multiply(new BigDecimal(cnYq.getInfiDiscount()));
        }
        // 成人票价不打折
        return ticketAmount;
    }

    /**
     * 计算基建费
     *
     * @param passengerType 旅客类型
     * @param cnYq          基建燃油费用配置
     * @param segInfos      航段信息列表
     * @return 基建费
     */
    private BigDecimal calculateFundAmount(String passengerType, MnjxCnYq cnYq, List<SegInfo> segInfos) {
        // 婴儿和儿童无基建费
        if ("INF".equals(passengerType) || "CHD".equals(passengerType)) {
            return BigDecimal.ZERO;
        }

        // 成人基建费，每个航段都有基建费
        int segmentCount = segInfos.size();
        return new BigDecimal(cnYq.getCn()).multiply(new BigDecimal(segmentCount));
    }

    /**
     * 计算燃油费
     *
     * @param passengerType 旅客类型
     * @param cnYq          基建燃油费用配置
     * @param segInfos      航段信息列表
     * @return 燃油费
     */
    private BigDecimal calculateFuelAmount(String passengerType, MnjxCnYq cnYq, List<SegInfo> segInfos) {
        // 婴儿无燃油费
        if ("INF".equals(passengerType)) {
            return BigDecimal.ZERO;
        }

        // 计算每个航段的燃油费，然后累加
        BigDecimal totalFuelAmount = BigDecimal.ZERO;

        for (SegInfo segInfo : segInfos) {
            // 获取当前航段的飞行距离
            Integer segmentDistance = this.calculateSegmentDistance(segInfo);

            // 根据飞行距离确定燃油费
            Integer segmentFuelAmount;
            if (segmentDistance != null && segmentDistance > cnYq.getThreshold()) {
                segmentFuelAmount = cnYq.getYqHigh();
            } else {
                segmentFuelAmount = cnYq.getYqLow();
            }

            // 儿童、军警残燃油费为成人的50%，并且个位向下取0
            if (StrUtil.equalsAny(passengerType, "CHD", "GM", "JC")) {
                BigDecimal childFuel = new BigDecimal(segmentFuelAmount).multiply(new BigDecimal(cnYq.getChildDiscount()));
                // 向下取整到10的倍数
                childFuel = new BigDecimal(Math.floor(childFuel.doubleValue() / 10) * 10);
                totalFuelAmount = totalFuelAmount.add(childFuel);
            } else {
                // 成人燃油费
                totalFuelAmount = totalFuelAmount.add(new BigDecimal(segmentFuelAmount));
            }
        }

        return totalFuelAmount;
    }

    /**
     * 计算单个航段的飞行距离
     *
     * @param segInfo 航段信息
     * @return 飞行距离
     */
    private Integer calculateSegmentDistance(SegInfo segInfo) {
        // 获取出发和到达城市
        MnjxAirport depAirport = iSguiCommonService.getAirportByCode(segInfo.getDepartureCode());
        MnjxAirport arrAirport = iSguiCommonService.getAirportByCode(segInfo.getArrivalCode());

        if (depAirport != null && arrAirport != null) {
            // 通过城市ID查询城市
            MnjxCity depCity = iMnjxCityService.getById(depAirport.getCityId());
            MnjxCity arrCity = iMnjxCityService.getById(arrAirport.getCityId());

            if (depCity != null && arrCity != null) {
                // 查询基准运价表获取距离
                MnjxStandardPat standardPat = iMnjxStandardPatService.lambdaQuery()
                        .eq(MnjxStandardPat::getOrgCityId, depCity.getCityId())
                        .eq(MnjxStandardPat::getDstCityId, arrCity.getCityId())
                        .one();

                // 如果查询结果为空，尝试将城市对反过来再查询一次
                if (standardPat == null) {
                    standardPat = iMnjxStandardPatService.lambdaQuery()
                            .eq(MnjxStandardPat::getOrgCityId, arrCity.getCityId())
                            .eq(MnjxStandardPat::getDstCityId, depCity.getCityId())
                            .one();
                }

                if (standardPat != null && standardPat.getDistance() != null) {
                    return standardPat.getDistance();
                }
            }
        }

        return 0; // 如果无法获取距离，返回0
    }

    /**
     * 计算总飞行距离
     *
     * @param segInfos 航段信息列表
     * @return 总飞行距离
     */
    private Integer calculateTotalDistance(List<SegInfo> segInfos) {
        Integer totalDistance = 0;

        for (SegInfo segInfo : segInfos) {
            Integer segmentDistance = this.calculateSegmentDistance(segInfo);
            totalDistance += segmentDistance;
        }

        return totalDistance;
    }

    /**
     * 创建税费列表
     *
     * @param fundAmount    基建费
     * @param fuelAmount    燃油费
     * @param segInfos      航段信息列表
     * @return 税费列表
     */
    private List<TaxDom> createTaxDomList(BigDecimal fundAmount, BigDecimal fuelAmount, List<SegInfo> segInfos) {
        List<TaxDom> taxDomList = new ArrayList<>();

        // 只有成人有基建费
        if (fundAmount.compareTo(BigDecimal.ZERO) > 0) {
            TaxDom cnTax = new TaxDom();
            cnTax.setTaxCode("CN");

            Price cnPrice = new Price();
            cnPrice.setAmount(fundAmount.setScale(2, RoundingMode.HALF_UP).toString());
            cnPrice.setCurrency("CNY");

            cnTax.setPrice(cnPrice);
            taxDomList.add(cnTax);
        }

        // 成人和儿童有燃油费
        if (fuelAmount.compareTo(BigDecimal.ZERO) > 0) {
            TaxDom yqTax = new TaxDom();
            yqTax.setTaxCode("YQ");

            Price yqPrice = new Price();
            yqPrice.setAmount(fuelAmount.setScale(2, RoundingMode.HALF_UP).toString());
            yqPrice.setCurrency("CNY");

            yqTax.setPrice(yqPrice);
            taxDomList.add(yqTax);
        }

        return taxDomList;
    }

    /**
     * 构建FC信息
     *
     * @param segInfos               航段信息列表
     * @param passengerType          旅客类型
     * @param discountedTicketAmount 折扣后的票面价
     * @param option                 运价选项
     * @return FC信息
     */
    private String buildFcInfo(List<SegInfo> segInfos, String passengerType, BigDecimal discountedTicketAmount, String option) {
        StringBuilder fc = new StringBuilder();

        // 判断是自动运价还是手工运价
        boolean isManual = "M".equals(option);

        if (isManual) {
            // 手工运价
            if ("INF".equals(passengerType)) {
                fc.append("FC /IN/");
            } else {
                fc.append("FC ");
            }

            // 判断是单程还是多程
            if (segInfos.size() == 1) {
                // 单程
                SegInfo segInfo = segInfos.get(0);
                fc.append(segInfo.getDepartureCode()).append(" ")
                        .append(segInfo.getCompanyCode()).append(" ")
                        .append(segInfo.getArrivalCode()).append(" ")
                        .append(new BigDecimal(segInfo.getTicketAmount()).setScale(2, RoundingMode.HALF_UP)).append(segInfo.getFareBasisCodes());

                // 添加总价
                fc.append("  \n-  CNY ").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP)).append(" END");
            } else {
                // 多程
                // 检查航段是否连续
                boolean isConsecutive = this.checkConsecutiveSegments(segInfos);

                // 添加第一个航段信息
                SegInfo firstSegInfo = segInfos.get(0);
                fc.append(firstSegInfo.getDepartureCode()).append(" ")
                        .append(firstSegInfo.getCompanyCode()).append(" ")
                        .append(firstSegInfo.getArrivalCode()).append(" ")
                        .append(new BigDecimal(firstSegInfo.getTicketAmount()).setScale(2, RoundingMode.HALF_UP)).append(firstSegInfo.getFareBasisCodes());

                // 添加后续航段信息
                for (int i = 1; i < segInfos.size(); i++) {
                    SegInfo segInfo = segInfos.get(i);

                    // 判断是否连续
                    if (!isConsecutive) {
                        // 不连续的航段使用 // 连接
                        fc.append(" // ");
                        fc.append(segInfo.getDepartureCode()).append(" ");
                    } else {
                        fc.append(" ");
                    }

                    fc.append(segInfo.getCompanyCode()).append(" ")
                            .append(segInfo.getArrivalCode()).append(" ")
                            .append(new BigDecimal(segInfo.getTicketAmount()).setScale(2, RoundingMode.HALF_UP)).append(segInfo.getFareBasisCodes());
                }

                // 添加总价
                fc.append("  \n-  CNY ").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP)).append(" END");
            }
        } else {
            // 自动运价
            if ("INF".equals(passengerType)) {
                fc.append("FC/IN/");
            } else {
                fc.append("FC/");
            }

            // 判断是单程还是多程
            if (segInfos.size() == 1) {
                // 单程
                SegInfo segInfo = segInfos.get(0);
//                String departureDate = segInfo.getDepartureDateTime().substring(0, 10);
//                String formattedDate = DateUtils.ymd2Com(departureDate);

                fc.append(segInfo.getDepartureCode()).append(" ")
//                        .append("A-").append(formattedDate).append(" ")
                        .append(segInfo.getCompanyCode()).append(" ")
                        .append(segInfo.getArrivalCode()).append(" ")
                        .append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP)).append(segInfo.getFareBasisCodes())
                        .append(" CNY ").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP)).append(" END");

                // 添加旅客类型标记
                if ("CHD".equals(passengerType)) {
                    fc.append(" **(CH)");
                } else if ("INF".equals(passengerType)) {
                    fc.append(" **(IN)");
                } else if ("GM".equals(passengerType)) {
                    fc.append(" **(GM)");
                } else if ("JC".equals(passengerType)) {
                    fc.append(" **(JC)");
                }
            } else {
                // 多程
                // 检查航段是否连续
                boolean isConsecutive = this.checkConsecutiveSegments(segInfos);

                // 添加第一个航段信息
                SegInfo firstSegInfo = segInfos.get(0);
//                String firstDepartureDate = firstSegInfo.getDepartureDateTime().substring(0, 10);
//                String firstFormattedDate = DateUtils.ymd2Com(firstDepartureDate);

                fc.append(firstSegInfo.getDepartureCode()).append(" ")
//                        .append("A-").append(firstFormattedDate).append(" ")
                        .append(firstSegInfo.getCompanyCode()).append(" ")
                        .append(firstSegInfo.getArrivalCode()).append(" ")
                        .append(new BigDecimal(firstSegInfo.getTicketAmount()).setScale(2, RoundingMode.HALF_UP)).append(firstSegInfo.getFareBasisCodes());

                // 添加后续航段信息
                for (int i = 1; i < segInfos.size(); i++) {
                    SegInfo segInfo = segInfos.get(i);
//                    String departureDate = segInfo.getDepartureDateTime().substring(0, 10);
//                    String formattedDate = DateUtils.ymd2Com(departureDate);

                    // 判断是否连续
//                    if (isConsecutive) {
//                        fc.append(" A-").append(formattedDate).append(" ");
//                    } else {
//                        fc.append(" // ").append(segInfo.getDepartureCode()).append(" B-0 A-0 ");
//                    }

                    if (isConsecutive) {
                        fc.append(" ");
                    } else {
                        fc.append(" // ");
                    }

                    fc.append(segInfo.getCompanyCode()).append(" ")
                            .append(segInfo.getArrivalCode()).append(" ")
                            .append(new BigDecimal(segInfo.getTicketAmount()).setScale(2, RoundingMode.HALF_UP)).append(segInfo.getFareBasisCodes());
                }

                // 添加总价
                fc.append(" CNY ").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP)).append(" END");

                // 添加旅客类型标记
                if ("CHD".equals(passengerType)) {
                    fc.append(" **(CH)");
                } else if ("INF".equals(passengerType)) {
                    fc.append(" **(IN)");
                } else if ("GM".equals(passengerType)) {
                    fc.append(" **(GM)");
                } else if ("JC".equals(passengerType)) {
                    fc.append(" **(JC)");
                }
            }
        }

        return fc.toString();
    }

    /**
     * 检查航段是否连续
     *
     * @param segInfos 航段信息列表
     * @return 是否连续
     */
    private boolean checkConsecutiveSegments(List<SegInfo> segInfos) {
        if (segInfos.size() <= 1) {
            return true;
        }

        for (int i = 0; i < segInfos.size() - 1; i++) {
            SegInfo current = segInfos.get(i);
            SegInfo next = segInfos.get(i + 1);

            // 如果当前航段的到达机场不等于下一个航段的出发机场，则不连续
            if (!current.getArrivalCode().equals(next.getDepartureCode())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 构建FN信息
     *
     * @param passengerType          旅客类型
     * @param discountedTicketAmount 折扣后的票面价
     * @param fundAmount             基建费
     * @param fuelAmount             燃油费
     * @param option                 运价选项
     * @return FN信息
     */
    private String buildFnInfo(String passengerType, BigDecimal discountedTicketAmount, BigDecimal fundAmount, BigDecimal fuelAmount, String option) {
        StringBuilder fn = new StringBuilder();

        // 判断是自动运价还是手工运价
        boolean isManual = "M".equals(option);

        if (isManual) {
            // 手工运价
            if ("INF".equals(passengerType)) {
                fn.append("FN /IN/");
            } else {
                fn.append("FN ");
            }

            fn.append("FCNY ").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP))
                    .append("/ SCNY").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP))
                    .append("/ C0.00/ ");

            // 添加基建费
            if (fundAmount.compareTo(BigDecimal.ZERO) > 0) {
                fn.append("TCNY ").append(fundAmount.setScale(2, RoundingMode.HALF_UP)).append("CN/ ");
            } else {
                fn.append("TEXEMPTCN/ ");
            }

            // 添加燃油费
            if (fuelAmount.compareTo(BigDecimal.ZERO) > 0) {
                fn.append("TCNY ").append(fuelAmount.setScale(2, RoundingMode.HALF_UP)).append("YQ");
            } else {
                fn.append("TEXEMPTYQ");
            }
        } else {
            // 自动运价
            if ("INF".equals(passengerType)) {
                fn.append("FN/IN/");
            } else {
                fn.append("FN/");
            }

            fn.append("FCNY").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP))
                    .append("/SCNY").append(discountedTicketAmount.setScale(2, RoundingMode.HALF_UP))
                    .append("/C0.00/");

            // 添加基建费
            if (fundAmount.compareTo(BigDecimal.ZERO) > 0) {
                fn.append("TCNY").append(fundAmount.setScale(2, RoundingMode.HALF_UP)).append("CN/");
            } else {
                fn.append("TEXEMPTCN/");
            }

            // 添加燃油费
            if (fuelAmount.compareTo(BigDecimal.ZERO) > 0) {
                fn.append("TCNY").append(fuelAmount.setScale(2, RoundingMode.HALF_UP)).append("YQ");
            } else {
                fn.append("TEXEMPTYQ");
            }
        }

        return fn.toString();
    }

    /**
     * 构建FP信息
     *
     * @param passengerType 旅客类型
     * @param option        运价选项
     * @return FP信息
     */
    private String buildFpInfo(String passengerType, String option) {
        StringBuilder fp = new StringBuilder();

        // 判断是自动运价还是手工运价
        boolean isManual = "M".equals(option);

        if (isManual) {
            // 手工运价
            if ("INF".equals(passengerType)) {
                fp.append("FP /IN/");
            } else {
                fp.append("FP ");
            }
            fp.append("CASH,CNY");
        } else {
            // 自动运价
            if ("INF".equals(passengerType)) {
                fp.append("FP/IN/");
            } else {
                fp.append("FP/");
            }
            fp.append("CASH,CNY");
        }

        return fp.toString();
    }

    /**
     * 获取基建燃油费用配置
     *
     * @return 基建燃油费用配置
     */
    private MnjxCnYq getCnYq() {
        List<MnjxCnYq> cnYqList = iMnjxCnYqService.list();
        if (CollUtil.isNotEmpty(cnYqList)) {
            return cnYqList.get(0);
        }
        return null;
    }
}
