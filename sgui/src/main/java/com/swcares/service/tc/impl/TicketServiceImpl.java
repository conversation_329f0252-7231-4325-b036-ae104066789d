package com.swcares.service.tc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.QueryTicketByCertDto;
import com.swcares.obj.dto.QueryTicketByDetrDto;
import com.swcares.obj.dto.QueryTicketByPnrDto;
import com.swcares.obj.dto.QueryTicketDetailDto;
import com.swcares.obj.vo.QueryTicketByDetrVo;
import com.swcares.obj.vo.QueryTicketByPnrVo;
import com.swcares.obj.vo.QueryTicketDetailVo;
import com.swcares.obj.vo.TicketByRtktVo;
import com.swcares.service.*;
import com.swcares.service.tc.ITicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 客票服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/30 14:00
 */
@Slf4j
@Service
public class TicketServiceImpl implements ITicketService {

    /**
     * 解析fn历史项
     */
    //改签前的解析正则，即默认正常格式下
    private static final Pattern H_FN = Pattern.compile("FN(/A)?/[A-Z]{4}([\\d.]+)/[A-Z]{4}([\\d.]+)/[A-Z]([\\d.]+)/[A-Z]{4}([\\d.]+)/[A-Z]{4}([\\d.]+)[A-Z]{2}/[A-Z]{4}([\\d.]+)[A-Z]{2}/[A-Z]{4}([\\d.]+)(/P[\\d]+)?");
    //改签后的FN项解析正则，格式如：FN/A/RCNY800.00/SCNY0.00/C0.00/XCNY80.00/TCNY80.00OB/OCNY50.00CN/OCNY140.00YQ/ACNY80.00/P1
    private static final Pattern OI_FN = Pattern.compile("FN(/IN)?(/A)?/[A-Z]{4}([\\d.]+)/[A-Z]{4}([\\d.]+)/[A-Z]([\\d.]+)/[A-Z]{4}([\\d.]+)/[A-Z]{4}([\\d.]+)OB/[A-Z]{4}([\\d.]+)[A-Z]{2}/[A-Z]{4}([\\d.]+)[A-Z]{2}/[A-Z]{4}([\\d.]+)(/P[\\d]+)?");

    /**
     * 婴儿
     */
    private static final Pattern IN_FN = Pattern.compile("FN(/IN)?(/A)?/[A-Z]{4}([\\d.]+)/[A-Z]{4}([\\d.]+)/[A-Z]([\\d.]+)/([A-Z]+)/([A-Z]+)/[A-Z]{4}([\\d.]+)(/P[\\d]+)?");

    /**
     * 儿童
     */
    private static final Pattern CH_FN = Pattern.compile("FN(/A)?/[A-Z]{4}([\\d.]+)/[A-Z]{4}([\\d.]+)/[A-Z]([\\d.]+)/[A-Z]{4}([\\d.]+)/([A-Z]+)[A-Z]{2}/[A-Z]{4}([\\d.]+)[A-Z]{2}/[A-Z]{4}([\\d.]+)(/P[\\d]+)?");

    private static final Pattern REG_OI = Pattern.compile("[A-Z]{2}/A/(\\d{3}-?\\d{10})#\\d{4}([A-Z]{3})([A-Z0-9]{7})\\s(\\d+)/P\\d+");

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Override
    public List<QueryTicketDetailVo> queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException {
        String ticketNo = dto.getTicketNo();
        if (StrUtil.isEmpty(ticketNo)) {
            throw new SguiResultException("票号不能为空");
        }
        if (ObjectUtil.isEmpty(dto.getSecondFactor()) || StrUtil.isEmpty(dto.getSecondFactor().getSecondFactorCode())) {
            throw new SguiResultException("缺少查询条件");
        }

        // 通过票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo.replace("-", ""))
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("没有找到票面信息");
        }

        String pnrNmId;
        String nmXnId;
        // 获取票与航段的关系信息
        String pnrNmTnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTnId);
        if (pnrNmTn == null) {
            // tn为空可能是改签票，需要用票号去record查被删除的tn
            MnjxPnrRecord xeTnRecord = iMnjxPnrRecordService.lambdaQuery()
                    .like(MnjxPnrRecord::getInputValue, ticketNo.contains("-") ? ticketNo : ticketNo.substring(0, 3) + "-" + ticketNo.substring(3))
                    .eq(MnjxPnrRecord::getPnrType, "TN")
                    .eq(MnjxPnrRecord::getChangeMark, "X")
                    .one();
            if (ObjectUtil.isEmpty(xeTnRecord)) {
                throw new SguiResultException("无票面信息");
            }
            if (iMnjxPnrNmService.getById(xeTnRecord.getPnrNmId()) != null) {
                pnrNmId = xeTnRecord.getPnrNmId();
                nmXnId = null;
            } else {
                pnrNmId = null;
                nmXnId = xeTnRecord.getPnrNmId();
            }
        } else {
            pnrNmId = pnrNmTn.getPnrNmId();
            nmXnId = pnrNmTn.getNmXnId();
        }

        // 通过tnId查询该旅客的所有票信息
        List<MnjxPnrNmTicket> allTickets = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTnId)
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();

        if (CollUtil.isEmpty(allTickets)) {
            throw new SguiResultException("没有找到票面信息");
        }

        MnjxPnrNm pnrNm;
        MnjxNmXn nmXn = null;
        // 获取旅客信息
        if (StrUtil.isNotEmpty(pnrNmId)) {
            // 非婴儿旅客
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                throw new SguiResultException("无票面信息");
            }
        } else if (StrUtil.isNotEmpty(nmXnId)) {
            // 婴儿旅客
            nmXn = iMnjxNmXnService.getById(nmXnId);
            if (nmXn == null) {
                throw new SguiResultException("无票面信息");
            }
            pnrNmId = nmXn.getPnrNmId();
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        } else {
            throw new SguiResultException("无票面信息");
        }
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());

        if (pnr == null) {
            throw new SguiResultException("没有找到票面信息");
        }
        String pnrCrs = pnr.getPnrCrs();

        // 根据secondFactorCode和secondFactorValue验证
        if (dto.getSecondFactor() != null) {
            String secondFactorCode = dto.getSecondFactor().getSecondFactorCode();
            String secondFactorValue = dto.getSecondFactor().getSecondFactorValue();

            if (StrUtil.isNotEmpty(secondFactorCode) && StrUtil.isNotEmpty(secondFactorValue)) {
                if ("NI".equals(secondFactorCode)) {
                    // 通过身份证号验证
                    List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "FOID")
                            .list();

                    boolean found = false;
                    if (CollUtil.isNotEmpty(ssrList)) {
                        for (MnjxNmSsr ssr : ssrList) {
                            if (StrUtil.isNotEmpty(ssr.getSsrInfo()) &&
                                    ssr.getSsrInfo().contains(" NI") &&
                                    secondFactorValue.equals(ssr.getSsrInfo().split(" NI")[1].split("/")[0])) {
                                found = true;
                                break;
                            }
                        }
                    }

                    if (!found) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("PP".equals(secondFactorCode)) {
                    // 通过护照号验证
                    List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "DOCS")
                            .list();

                    boolean found = false;
                    if (CollUtil.isNotEmpty(ssrList)) {
                        for (MnjxNmSsr ssr : ssrList) {
                            if (StrUtil.isNotEmpty(ssr.getSsrInfo())) {
                                // 解析DOCS格式：SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
                                // 需要提取护照号码部分（第三个斜杠后的内容）
                                String ssrInfo = ssr.getSsrInfo();
                                String[] parts = ssrInfo.split("/");
                                if (parts.length >= 3) {
                                    // 护照号在第三部分
                                    String passportNumber = parts[2];
                                    if (passportNumber.equals(secondFactorValue)) {
                                        found = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (!found) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("UU".equals(secondFactorCode)) {
                    // 通过特殊身份证查询
                    List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "FOID")
                            .list();

                    boolean found = false;
                    if (CollUtil.isNotEmpty(ssrList)) {
                        for (MnjxNmSsr ssr : ssrList) {
                            if (StrUtil.isNotEmpty(ssr.getSsrInfo()) &&
                                    ssr.getSsrInfo().contains(" UU") &&
                                    secondFactorValue.equals(ssr.getSsrInfo().split(" UU")[1].split("/")[0])) {
                                found = true;
                                break;
                            }
                        }
                    }

                    if (!found) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("NM".equals(secondFactorCode)) {
                    // 通过旅客姓名验证，姓名模糊验证
                    String name = nmXn != null ? nmXn.getXnCname() : pnrNm.getName();
                    if (!secondFactorValue.startsWith(name)) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("CN".equals(secondFactorCode)) {
                    // 通过PNR验证
                    if (!secondFactorValue.equals(pnrCrs)) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                }
            }
        }

        // 构建返回数据列表
        List<QueryTicketDetailVo> resultList = new ArrayList<>();

        // 构建联票信息
        String conjunctiveTicket = "";
        if (allTickets.size() > 1) {
            String firstTicketNo = allTickets.get(0).getTicketNo();
            String lastTicketNo = allTickets.get(allTickets.size() - 1).getTicketNo();
            conjunctiveTicket = firstTicketNo.substring(0, 3) + "-" + firstTicketNo.substring(3) + "/" + lastTicketNo.substring(lastTicketNo.length() - 2);
        }

        // 为每张票构建VO
        for (MnjxPnrNmTicket ticket : allTickets) {
            QueryTicketDetailVo vo = this.buildTicketDetailVo(ticket, pnrNm, nmXn, pnr, dto, conjunctiveTicket);
            resultList.add(vo);
        }

        return resultList;
    }

    /**
     * 构建单个票的详情VO
     *
     * @param ticket            票信息
     * @param pnrNm             旅客信息
     * @param nmXn              婴儿信息
     * @param pnr               PNR信息
     * @param dto               请求参数
     * @param conjunctiveTicket 联票信息
     * @return 票详情VO
     */
    private QueryTicketDetailVo buildTicketDetailVo(MnjxPnrNmTicket ticket, MnjxPnrNm pnrNm, MnjxNmXn nmXn, MnjxPnr pnr,
                                                    QueryTicketDetailDto dto, String conjunctiveTicket) {
        QueryTicketDetailVo vo = new QueryTicketDetailVo();
        vo.setEtNumber(ticket.getTicketNo().substring(0, 3) + "-" + ticket.getTicketNo().substring(3));

        // 设置旅客信息
        if (nmXn != null) {
            // 婴儿票
            vo.setPassengerName(nmXn.getXnCname());
            vo.setPassengerNameSuffix(nmXn.getXnCname());
            vo.setFullName(nmXn.getXnCname());
            vo.setPassengerType("INF");
            vo.setTicketPsgType("INF");
            vo.setSpecialPassengerType("INF");
        } else {
            // 成人或儿童票
            vo.setPassengerName(pnrNm.getName());
            vo.setPassengerNameSuffix(pnrNm.getName());
            vo.setFullName(pnrNm.getName());

            MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, "CHLD")
                    .one();
            if (ObjectUtil.isNotEmpty(chldSsr)) {
                vo.setPassengerType("CHD");
                vo.setTicketPsgType("CHD");
                vo.setSpecialPassengerType("CHD");
            } else {
                vo.setPassengerType("ADT");
                vo.setTicketPsgType("ADT");
                vo.setSpecialPassengerType("ADT");
                MnjxNmRmk gmjcNmRmk = iMnjxNmRmkService.lambdaQuery()
                        .eq(MnjxNmRmk::getPnrNmId, pnrNm.getPnrNmId())
                        .eq(MnjxNmRmk::getRmkName, "GMJC")
                        .one();
                if (ObjectUtil.isNotEmpty(gmjcNmRmk)) {
                    String specialType = "GMJC";
                    if (pnrNm.getName().endsWith("GM JC") || pnrNm.getName().endsWith("JC GM")) {
                        specialType = "GMJC";
                    } else if (pnrNm.getName().endsWith("GM")) {
                        specialType = "GM";
                    } else if (pnrNm.getName().endsWith("JC")) {
                        specialType = "JC";
                    }
                    vo.setTicketPsgType(specialType);
                    vo.setSpecialPassengerType(specialType);
                    vo.setPassengerName(pnrNm.getName() + "(GMJC)");
                    vo.setPassengerNameSuffix(pnrNm.getName() + "(GMJC)");
                }
            }
        }

        vo.setNameSuffix("");
        vo.setTicketTypeCode("D");
        vo.setCdsTicket(false);
        vo.setEtType("BSP");
        vo.setGovernmentPurchase(false);
        vo.setReceiptPrinted("1".equals(ticket.getReceiptPrint()));
        vo.setConjunctiveTicket(conjunctiveTicket);

        // 设置出票日期和时间
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, ticket.getTicketNo())
                .one();
        String issueInfo = ticketPrice.getIssueInfo();
        String[] issueSplit = issueInfo.split(" ");
        DateTime currentDate = DateUtil.parseDateTime(issueSplit[2] + " " + issueSplit[3]);
        vo.setIssueTicketDate(DateUtil.format(currentDate, "yyyy-MM-dd"));
        vo.setIssueTime(DateUtil.format(currentDate, "HH:mm"));

        // 设置二次验证因素
        if (dto.getSecondFactor() != null) {
            QueryTicketDetailVo.SecondFactorVo secondFactorVo = new QueryTicketDetailVo.SecondFactorVo();
            secondFactorVo.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
            secondFactorVo.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
            vo.setSecondFactor(secondFactorVo);
        }

        // 构建航段信息
        List<QueryTicketDetailVo.AirSegVo> airSegList = this.buildAirSegList(ticket, pnr);
        vo.setAirSeg(airSegList);
        vo.setAirSegCrsPnr("DEL".equals(pnr.getPnrStatus()) ? null : pnr.getPnrCrs());

        return vo;
    }

    /**
     * 构建航段信息列表
     *
     * @param ticket 票信息
     * @param pnr    PNR信息
     * @return 航段信息列表
     */
    private List<QueryTicketDetailVo.AirSegVo> buildAirSegList(MnjxPnrNmTicket ticket, MnjxPnr pnr) {
        List<QueryTicketDetailVo.AirSegVo> airSegList = new ArrayList<>();

        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, ticket.getTicketNo())
                .one();
        String segInfo = ticketPrice.getSegInfo();
        String issueInfo = ticketPrice.getIssueInfo();
        String[] issueSplit = issueInfo.split(" ");
        String selectSegNo = issueSplit[issueSplit.length - 1];
        String[] split = segInfo.split("/");
        String[] selectSplit = selectSegNo.split("-");
        String first = split[Integer.parseInt(selectSplit[0]) - 1];
        QueryTicketDetailVo.AirSegVo firstAirSegVo = this.buildAirSegVo(first.split(" "), pnr, Integer.parseInt(selectSplit[0]));

        if (!firstAirSegVo.getSegANRK()) {
            firstAirSegVo.setTicketStatus(ticket.getTicketStatus1());
            if (Constant.TICKET_STATUS_EXCHANGED.equals(ticket.getTicketStatus1())) {
                firstAirSegVo.setFlightNo("OPEN");
                firstAirSegVo.setDepartureDate("OPEN");
                firstAirSegVo.setDepTime(null);
                firstAirSegVo.setArrTime(null);
                firstAirSegVo.setPnrNo(null);
                firstAirSegVo.setCrsPnrNo(null);
                firstAirSegVo.setSegOPEN(false);
                firstAirSegVo.setCrsType(null);
            }
        }

        airSegList.add(firstAirSegVo);
        if (selectSplit.length == 2) {
            String last = split[Integer.parseInt(selectSplit[1]) - 1];
            QueryTicketDetailVo.AirSegVo lastAirSegVo = this.buildAirSegVo(last.split(" "), pnr, Integer.parseInt(selectSplit[1]));
            if (!lastAirSegVo.getSegANRK()) {
                lastAirSegVo.setTicketStatus(ticket.getTicketStatus2());
                if (Constant.TICKET_STATUS_EXCHANGED.equals(ticket.getTicketStatus2())) {
                    lastAirSegVo.setFlightNo("OPEN");
                    lastAirSegVo.setDepartureDate("OPEN");
                    lastAirSegVo.setDepTime(null);
                    lastAirSegVo.setArrTime(null);
                    lastAirSegVo.setPnrNo(null);
                    lastAirSegVo.setCrsPnrNo(null);
                    lastAirSegVo.setSegOPEN(false);
                    lastAirSegVo.setCrsType(null);
                }
            }
            airSegList.add(lastAirSegVo);
        }

        return airSegList;
    }

    /**
     * 构建ARNK航段信息VO
     *
     * @param seg          航段实体
     * @param pnr          PNR
     * @param segmentIndex 航段序号
     * @return ARNK航段信息VO
     */
    private QueryTicketDetailVo.AirSegVo buildArnkSegVo(MnjxPnrSeg seg, MnjxPnr pnr, int segmentIndex) {
        QueryTicketDetailVo.AirSegVo airSegVo = new QueryTicketDetailVo.AirSegVo();

        airSegVo.setDepAirportCode(seg.getOrg());
        airSegVo.setCrsPnrNo(pnr.getPnrCrs());
        airSegVo.setCabin("");
        airSegVo.setFlightNo("ARNK");
        airSegVo.setDepTime(null);
        airSegVo.setArrTime(null);
        airSegVo.setArrAirportCode(seg.getDst());
        airSegVo.setTicketStatus("VOID");
        airSegVo.setPnrNo(pnr.getPnrIcs());
        airSegVo.setCrsType("1E");
        airSegVo.setAirline("");
        airSegVo.setDepartureDate("VOID");
        airSegVo.setChangeReason("");
        airSegVo.setSegmentIndex(segmentIndex);
        airSegVo.setSegANRK(true);
        airSegVo.setSegOPEN(false);

        return airSegVo;
    }

    /**
     * 构建航段信息VO
     *
     * @param segSplit     航段信息
     * @param pnr          PNR
     * @param segmentIndex 航段序号
     * @return 航段信息VO
     */
    private QueryTicketDetailVo.AirSegVo buildAirSegVo(String[] segSplit, MnjxPnr pnr, int segmentIndex) {
        QueryTicketDetailVo.AirSegVo airSegVo = new QueryTicketDetailVo.AirSegVo();
        String flightNo = segSplit[0];
        airSegVo.setDepAirportCode(segSplit[1]);
        airSegVo.setArrAirportCode(segSplit[2]);
        airSegVo.setCrsPnrNo(pnr.getPnrCrs());
        airSegVo.setPnrNo(pnr.getPnrIcs());
        airSegVo.setSegmentIndex(((segmentIndex + 1) % 2) + 1);
        airSegVo.setCrsType("1E");
        airSegVo.setChangeReason("");
        if ("SA".equals(flightNo)) {
            airSegVo.setCabin("");
            airSegVo.setFlightNo("ARNK");
            airSegVo.setDepTime(null);
            airSegVo.setArrTime(null);
            airSegVo.setTicketStatus("VOID");
            airSegVo.setAirline("");
            airSegVo.setDepartureDate("VOID");
            airSegVo.setSegANRK(true);
            airSegVo.setSegOPEN(false);

        } else {
            airSegVo.setCabin(segSplit[4]);
            airSegVo.setFlightNo(flightNo);

            // 设置出发时间
            String depDate = segSplit[3];
            airSegVo.setDepartureDate(depDate);
            String depTime = segSplit[5];
            if (StrUtil.isNotEmpty(depDate) && StrUtil.isNotEmpty(depTime)) {
                airSegVo.setDepTime(depDate + " " + depTime.substring(0, 2) + ":" + depTime.substring(2) + ":00");
            }

            // 设置到达时间
            String arrTime = segSplit[6];
            if (StrUtil.isNotEmpty(depDate) && StrUtil.isNotEmpty(arrTime)) {
                airSegVo.setArrTime(depDate + " " + arrTime.substring(0, 2) + ":" + arrTime.substring(2) + ":00");
            }

            // 设置航司
            if (StrUtil.isNotEmpty(flightNo) && flightNo.length() >= 2) {
                airSegVo.setAirline(flightNo.substring(0, 2));
            }

            airSegVo.setSegANRK(false);
            airSegVo.setSegOPEN(false);
        }

        if ("DEL".equals(pnr.getPnrStatus())) {
            airSegVo.setFlightNo("OPEN");
            airSegVo.setDepartureDate("OPEN");
            airSegVo.setDepTime(null);
            airSegVo.setArrTime(null);
            airSegVo.setPnrNo(null);
            airSegVo.setCrsPnrNo(null);
            airSegVo.setSegOPEN(false);
            airSegVo.setCrsType(null);
        }

        return airSegVo;
    }

    @Override
    public QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 验证detrType
        if (StrUtil.isEmpty(dto.getDetrType()) || !Arrays.asList("D", "H", "X", "F").contains(dto.getDetrType())) {
            throw new SguiResultException("选项参数错误");
        }

        // 去掉票号中的"-"
        String ticketNo = dto.getTicketNo().replace("-", "");

        // 通过票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("无票面信息");
        }

        String pnrNmId;
        String nmXnId;
        boolean ticketExchanged = false;
        // 获取票与航段的关系信息
        String pnrNmTnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTnId);
        if (pnrNmTn == null) {
            // tn为空可能是改签票，需要用票号去record查被删除的tn
            MnjxPnrRecord xeTnRecord = iMnjxPnrRecordService.lambdaQuery()
                    .like(MnjxPnrRecord::getInputValue, ticketNo.substring(0, 3) + "-" + ticketNo.substring(3))
                    .eq(MnjxPnrRecord::getPnrType, "TN")
                    .eq(MnjxPnrRecord::getChangeMark, "X")
                    .one();
            if (ObjectUtil.isEmpty(xeTnRecord)) {
                throw new SguiResultException("无票面信息");
            }
            ticketExchanged = true;
            if (iMnjxPnrNmService.getById(xeTnRecord.getPnrNmId()) != null) {
                pnrNmId = xeTnRecord.getPnrNmId();
                nmXnId = null;
            } else {
                pnrNmId = null;
                nmXnId = xeTnRecord.getPnrNmId();
            }
        } else {
            // 获取旅客信息
            pnrNmId = pnrNmTn.getPnrNmId();
            nmXnId = pnrNmTn.getNmXnId();
        }
        // 旅客姓名和类型
        String passengerName;
        String passengerType = ""; // 空为成人，CHD为儿童，INF为婴儿
        String xnBirthDate = null;
        MnjxPnrNm pnrNm;
        // 获取旅客信息
        if (StrUtil.isNotEmpty(pnrNmId)) {
            // 非婴儿旅客
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                throw new SguiResultException("无票面信息");
            }
            passengerName = pnrNm.getName();

            // 判断是否为儿童
            MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, "CHLD")
                    .one();
            if (ObjectUtil.isNotEmpty(chldSsr)) {
                passengerType = "CHD";
            }
            MnjxNmRmk nmRmk = iMnjxNmRmkService.lambdaQuery()
                    .eq(MnjxNmRmk::getRmkName, "GMJC")
                    .eq(MnjxNmRmk::getPnrNmId, pnrNmId)
                    .one();
            if (ObjectUtil.isNotEmpty(nmRmk)) {
                if (passengerName.endsWith("GMJC")) {
                    passengerType = "GMJC";
                } else if (passengerName.endsWith("GM")) {
                    passengerType = "GM";
                } else if (passengerName.endsWith("JC")) {
                    passengerType = "JC";
                }
            }
        } else if (StrUtil.isNotEmpty(nmXnId)) {
            // 婴儿旅客
            MnjxNmXn nmXn = iMnjxNmXnService.getById(nmXnId);
            if (nmXn == null) {
                throw new SguiResultException("无票面信息");
            }
            passengerName = nmXn.getXnCname();
            passengerType = "INF";
            xnBirthDate = nmXn.getXnBirthday();
            pnrNmId = nmXn.getPnrNmId();
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        } else {
            throw new SguiResultException("无票面信息");
        }
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        List<MnjxNmSsr> foidList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .in(MnjxNmSsr::getSsrType, "FOID", "DOCS")
                .list();

        // 构建返回对象
        QueryTicketByDetrVo vo = new QueryTicketByDetrVo();

        // 获取航段信息
        String s1Id = pnrNmTicket.getS1Id();
        String s2Id = pnrNmTicket.getS2Id();

        MnjxPnrSeg firstSeg = null;
        MnjxPnrSeg lastSeg = null;

        if (StrUtil.isNotEmpty(s1Id)) {
            firstSeg = iMnjxPnrSegService.getById(s1Id);
        }

        if (StrUtil.isNotEmpty(s2Id)) {
            lastSeg = iMnjxPnrSegService.getById(s2Id);
        }

        if (firstSeg == null && lastSeg == null && !ticketExchanged) {
            throw new SguiResultException("无票面信息");
        }
        // 根据detrType类型处理不同的返回数据
        switch (dto.getDetrType()) {
            case "D":
                List<String> originalTicketNos = new ArrayList<>();
                List<String> conjunctionTicketNos = new ArrayList<>();
                vo.setOriginalTicketNos(originalTicketNos);
                vo.setConjunctionTicketNos(conjunctionTicketNos);
                // 票面信息
                String openSourceText;
                if (ticketExchanged) {
                    openSourceText = this.buildOpenSourceTextForExchanged(vo, dto.getTicketNo(), passengerName, passengerType, xnBirthDate, pnrNmTicket);
                } else {
                    openSourceText = this.buildOpenSourceText(vo, dto.getTicketNo(), passengerName, passengerType, xnBirthDate, firstSeg, lastSeg, pnrNmTicket, pnr, pnrNmId);
                }
                vo.setOpenSourceText(openSourceText);
                vo.setInternational(false);

                // 设置二次筛选条件
                QueryTicketByDetrVo.SecondFactor secondFactor = new QueryTicketByDetrVo.SecondFactor();
                secondFactor.setSecondFactorCode("NM");
                secondFactor.setSecondFactorValue(passengerName);
                vo.setSecondFactor(secondFactor);
                break;

            case "H":
                // 客票历史信息
                String tktHistoryText = this.buildTktHistoryText(dto.getTicketNo(), passengerName, pnr);
                vo.setTktHistoryText(tktHistoryText);
                break;

            case "F":
                // 证件及其他信息
                QueryTicketByDetrVo.Credential credential = this.buildCredential(dto.getTicketNo(), passengerName, passengerType, foidList, xnBirthDate);
                vo.setCredential(credential);
                break;

            case "X":
                // 税项明细，暂时返回空数据
                vo.setCredential(null);
                MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                        .eq(MnjxTicketPrice::getTicketNo, ticketNo)
                        .one();
                String fnInfo = ticketPrice.getFnInfo();
                String[] split = fnInfo.split("/");
                String cn = "0.00";
                String yq = "0.00";
                for (String s : split) {
                    if (s.endsWith("CN")) {
                        cn = s.replace("TCNY", "").replace("OCNY", "").replace("CN", "");
                        if (!cn.matches("[\\d.]+")) {
                            cn = "0.00";
                        }
                    } else if (s.endsWith("YQ")) {
                        yq = s.replace("TCNY", "").replace("OCNY", "").replace("YQ", "");
                        if (!yq.matches("[\\d.]+")) {
                            yq = "0.00";
                        }
                    }
                }
                vo.setTicketFareInfoText(StrUtil.format("TAX LIST : \n CNY  {}CN|CNY  {}YQ", cn, yq));
                vo.setOpenSourceText(null);
                vo.setSecondFactor(null);
                vo.setTktHistoryText(null);
                break;

            default:
                throw new SguiResultException("无票面信息");
        }

        return vo;
    }

    /**
     * 解析历史记录中的航段
     * CA1831 Y   TH10JUL25  PEKSHA RR1   0730 1000          350 M 0  R E T2T2
     */
    private static final Pattern SEG_PATTERN = Pattern.compile("(\\w{5,7})\\s+([A-Z])\\s+([A-Z]{2})(\\w{5,7})\\s+([A-Z]{6})\\s+([A-Z]{2})(\\d)\\s+(\\d{4})\\s+(\\d{4})\\s+[\\w\\s]+");

    private String buildOpenSourceTextForExchanged(QueryTicketByDetrVo vo, String ticketNo, String passengerName, String passengerType, String xnBirthDate, MnjxPnrNmTicket pnrNmTicket) {

        ticketNo = ticketNo.contains("-") ? ticketNo : StrUtil.format("{}-{}", ticketNo.substring(0, 3), ticketNo.substring(3));
        MnjxPnrRecord pnrRecord = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrType, Constant.TN)
                .like(MnjxPnrRecord::getInputValue, ticketNo)
                .one();
        List<MnjxPnrSeg> exchangedSegList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(pnrRecord)) {
            List<String> segIds = Stream.of(pnrNmTicket.getS1Id(), pnrNmTicket.getS2Id())
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.toList());
            List<MnjxPnrRecord> pnrRecordSegs = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrType, Constant.PNR_SEG)
                    .in(MnjxPnrRecord::getPnrSegId, segIds)
                    .list();
            // 解析航段信息，构建MnjxPnrSeg对象
            if (CollUtil.isEmpty(pnrRecordSegs)) {
                return null;
            }
            exchangedSegList = pnrRecordSegs.stream()
                    .filter(record -> ReUtil.isMatch(SEG_PATTERN, record.getInputValue().trim()))
                    .map(record -> {
                        List<String> groups = ReUtil.getAllGroups(SEG_PATTERN, record.getInputValue());
                        MnjxPnrSeg pnrSeg = new MnjxPnrSeg();
                        pnrSeg.setFlightNo(groups.get(1));
                        pnrSeg.setSellCabin(groups.get(2));
                        String flightDate = DateUtils.com2ymd(groups.get(4));
                        pnrSeg.setFlightDate(flightDate);
                        pnrSeg.setOrg(groups.get(5).substring(0, 3));
                        pnrSeg.setDst(groups.get(5).substring(3));
                        pnrSeg.setActionCode(groups.get(6));
                        pnrSeg.setSeatNumber(Integer.valueOf(groups.get(7)));
                        pnrSeg.setEstimateOff(groups.get(8));
                        pnrSeg.setEstimateArr(groups.get(9));
                        pnrSeg.setPnrId(record.getPnrId());
                        return pnrSeg;
                    }).collect(Collectors.toList());
            // 排序
            exchangedSegList = exchangedSegList.stream()
                    .sorted(Comparator.comparing(MnjxPnrSeg::getFlightDate).thenComparing(MnjxPnrSeg::getEstimateOff).thenComparing(MnjxPnrSeg::getEstimateArr))
                    .collect(Collectors.toList());
            AtomicInteger i = new AtomicInteger(1);
            exchangedSegList.forEach(k -> k.setPnrSegNo(i.getAndIncrement()));
        }

        StringBuilder sb = new StringBuilder();
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, ticketNo.replace("-", ""))
                .one();
        // 第一行：ISSUED BY: ORG/DST: #{第一段出发城市三字码}/#{最后一段到达城市三字码} BSP-D
        sb.append("ISSUED BY:                           ORG/DST: ");

        String segInfo = ticketPrice.getSegInfo();
        String[] segInfoSplit = segInfo.split("/");
        String[] firstSegSplit = segInfoSplit[0].split(" ");
        String[] lastSegSplit = segInfoSplit[segInfoSplit.length - 1].split(" ");
        String orgAirportCode = firstSegSplit[1];
        String dstAirportCode = lastSegSplit[2];
        String orgCityCode = iSguiCommonService.getCityByAirportCode(orgAirportCode).getCityCode();
        String dstCityCode = iSguiCommonService.getCityByAirportCode(dstAirportCode).getCityCode();
        sb.append(orgCityCode).append("/").append(dstCityCode);
        sb.append("                 BSP-D\r\n");

        // 第二行：E/R: Q/改退收费
        sb.append("E/R: Q/改退收费\r\n");

        // 第三行：TOUR CODE:
        sb.append("TOUR CODE:\r\n");

        // 第四行：PASSENGER: #{旅客姓名} #{如果是儿童，设置为CHD (CHILD)；如果是婴儿，设置为INF(婴儿出生月年，格式如DEC24)}
        sb.append("PASSENGER: ").append(passengerName.replace("CHD", "").trim());
        if ("CHD".equals(passengerType)) {
            sb.append(" CHD(CHILD)");
        } else if ("INF".equals(passengerType) && StrUtil.isNotEmpty(xnBirthDate)) {
            sb.append(" INF(").append(DateUtils.ym2Com(xnBirthDate)).append(")");
        } else if (StrUtil.equalsAny(passengerType, "GM", "JC", "GMJC")) {
            sb.append(" (GMJC)");
        }
        sb.append("\r\n");

        // 第五行：EXCH: CONJ TKT:
        String originalTicket = this.getOriginalTicket(pnrNmTicket, pnrRecord.getPnrId());
        if (StrUtil.isNotEmpty(originalTicket)) {
            sb.append("EXCH: ").append(originalTicket);
            vo.getOriginalTicketNos().add(originalTicket);
        } else {
            sb.append("EXCH:                               ");
        }
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTicket.getPnrNmTnId())
                .list();
        if (nmTicketList.size() > 1) {
            MnjxPnrNmTn tn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
            String inputValue = tn.getInputValue().replace("TN/", "").replaceAll("/P\\d+", "");
            inputValue = StrUtil.replaceLast(inputValue, "-", "/");
            sb.append("CONJ TKT: ").append(inputValue).append("\r\n");
            vo.getConjunctionTicketNos().add(inputValue);
        } else {
            sb.append("CONJ TKT:\r\n");
        }

        // 第六行：O FM:1#{第一段出发机场三字码} #{第一段航司} #{第一段无航司的航班号} #{第一段销售舱位} #{第一段航班日期} #{第一段起飞时间} OK #{第一段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
        // 第七行
        sb.append("O FM:1");
        MnjxPnrSeg firstSeg = exchangedSegList.get(0);
        sb.append(firstSeg.getOrg())
                .append(" ")
                .append(firstSeg.getFlightNo().substring(0, 2))
                .append("    ")
                .append("OPEN")
                .append("  ")
                .append(firstSeg.getSellCabin())
                .append(" ")
                .append("OPEN          ")
                .append(firstSeg.getSellCabin())
                .append("                        ")
                .append("20K ")
                .append("EXCHANGED")
                .append("\r\n");
        // 8行前可能有第二段的信息
        if (exchangedSegList.size() > 1) {
            MnjxPnrSeg lastSeg = exchangedSegList.get(1);
            // 第六行：O FM:2#{第二段出发机场三字码} #{第二段航司} #{第二段无航司的航班号} #{第二段销售舱位} #{第二段航班日期} #{第二段起飞时间} OK #{第二段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
            sb.append("O FM:2");
            sb.append(firstSeg.getOrg())
                    .append(" ")
                    .append(lastSeg.getFlightNo().substring(0, 2))
                    .append("    ")
                    .append("OPEN")
                    .append("  ")
                    .append(lastSeg.getSellCabin())
                    .append(" ")
                    .append("OPEN          ")
                    .append(lastSeg.getSellCabin())
                    .append("                        ")
                    .append("20K ")
                    .append("EXCHANGED")
                    .append("\r\n");

            // 第八行：TO: #{最后一段的到达机场三字码}
            sb.append("  TO: ").append(dstAirportCode).append("\r\n");
        } else {
            // 第八行：TO: #{最后一段的到达机场三字码}
            sb.append("  TO: ").append(dstAirportCode).append("\r\n");
        }

        // 第九行：FC:
        sb.append("FC: ").append(ticketPrice.getFcInfo().replace("FC/", "").replaceAll("/P\\d+", "")).append("\r\n");

        // 第十行：FARE: |FOP:
        // FN/A/FCNY3000.00/SCNY3000.00/C0.00/XCNY380.00/TCNY100.00CN/TCNY280.00YQ/ACNY3380.00
        String fnInfo = ticketPrice.getFnInfo();
        String tCnPrice = "0.00";
        String tYqPrice = "0.00";
        String fPrice = "0.00";
        String aPrice = "0.00";
        String ob = "";

        if (ReUtil.isMatch(H_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(H_FN, fnInfo);
            fPrice = allGroups.get(2);
            tCnPrice = allGroups.get(6);
            tYqPrice = allGroups.get(7);
            aPrice = allGroups.get(8);
        } else if (ReUtil.isMatch(IN_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(IN_FN, fnInfo);
            fPrice = allGroups.get(3);
            aPrice = allGroups.get(8);
        } else if (ReUtil.isMatch(CH_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(CH_FN, fnInfo);
            fPrice = allGroups.get(2);
            tYqPrice = allGroups.get(7);
            aPrice = allGroups.get(8);
        } else if (ReUtil.isMatch(OI_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(OI_FN, fnInfo);
            fPrice = allGroups.get(3);
            ob = allGroups.get(7);
            tCnPrice = allGroups.get(8);
            tYqPrice = allGroups.get(9);
            aPrice = allGroups.get(10);
        }
        sb.append("FARE:           ")
                .append("CNY")
                .append(StrUtil.fill(fPrice, ' ', 8, true))
                .append("|FOP:CASH\r\n");

        if (new BigDecimal(tCnPrice).compareTo(BigDecimal.ZERO) != 0) {
            if (StrUtil.isNotEmpty(originalTicket)) {
                MnjxPnrRecord oiRecord = iMnjxPnrRecordService.lambdaQuery()
                        .like(MnjxPnrRecord::getInputValue, originalTicket)
                        .eq(MnjxPnrRecord::getPnrType, "OI")
                        .one();
                sb.append("TAX:            ")
                        .append("PD ")
                        .append(StrUtil.fill(tCnPrice, ' ', 6, true))
                        .append("CN");
                sb.append("|OI:").append(oiRecord.getInputValue().replace("OI/", " ").replaceAll("#\\d+", " ").replaceAll("/P\\d+", "")).append("\r\n");
            } else {
                sb.append("TAX:            ")
                        .append("CNY")
                        .append(StrUtil.fill(tCnPrice, ' ', 6, true))
                        .append("CN")
                        .append("|OI:\r\n");
            }
        } else {
            sb.append("TAX:                       ").append("|OI:\r\n");
        }
        if (new BigDecimal(tYqPrice).compareTo(BigDecimal.ZERO) != 0) {
            if (StrUtil.isNotEmpty(originalTicket)) {
                sb.append("TAX:            ")
                        .append("PD ")
                        .append(StrUtil.fill(tYqPrice, ' ', 6, true))
                        .append("YQ")
                        .append("|\r\n");
                sb.append("TAX:            ")
                        .append("PD ")
                        .append(StrUtil.fill(ob, ' ', 6, true))
                        .append("OB")
                        .append("|\r\n");
            } else {
                sb.append("TAX:            ")
                        .append("CNY")
                        .append(StrUtil.fill(tYqPrice, ' ', 6, true))
                        .append("YQ")
                        .append("|\r\n");
            }
        }

        // 第十二行：TOTAL: |TKTN: #{票号}
        sb.append("TOTAL:          ")
                .append("CNY")
                .append(StrUtil.fill(NumberUtils.formatBigDecimalStr(aPrice), ' ', 8, true))
                .append("|TKTN: ").append(ticketNo).append("\r\n");

        // Base64编码
        return Base64.getEncoder().encodeToString(sb.toString().getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(ticketNo)) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = ticketNo.replace("-", "");

        // 通过票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("无票面信息");
        }

        MnjxPnrNm pnrNm;
        MnjxNmXn nmXn;
        // 获取票与航段的关系信息
        String pnrNmTnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTnId);
        if (pnrNmTn == null) {
            MnjxPnrRecord tnRecord = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrType, "TN")
                    .like(MnjxPnrRecord::getInputValue, ticketNo)
                    .one();
            if (tnRecord == null) {
                throw new SguiResultException("无票面信息");
            }
            // 查询的是改签票
            // 获取旅客信息
            String pnrNmId = tnRecord.getPnrNmId();
            if (StrUtil.isNotEmpty(pnrNmId)) {
                pnrNm = iMnjxPnrNmService.getById(pnrNmId);
                if (pnrNm == null) {
                    throw new SguiResultException("无票面信息");
                }
            } else {
                nmXn = iMnjxNmXnService.getById(tnRecord.getPnrNmId());
                pnrNmId = nmXn.getPnrNmId();
                pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            }
        } else {
            // 获取旅客信息
            String pnrNmId = pnrNmTn.getPnrNmId();
            if (StrUtil.isNotEmpty(pnrNmId)) {
                pnrNm = iMnjxPnrNmService.getById(pnrNmId);
                if (pnrNm == null) {
                    throw new SguiResultException("无票面信息");
                }
            } else {
                nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
                pnrNmId = nmXn.getPnrNmId();
                pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            }
        }

        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());

        // 构建返回对象
        return this.buildTicketByRtktVo(pnr, pnrNmTicket, pnrNm);
    }

    /**
     * 构建openSourceText
     *
     * @param ticketNo      票号
     * @param passengerName 旅客姓名
     * @param passengerType 旅客类型
     * @param xnBirthDate   出生日期（婴儿）
     * @param firstSeg      第一航段
     * @param lastSeg       最后航段
     * @param pnrNmTicket
     * @param pnr
     * @param pnrNmId
     * @return Base64编码后的openSourceText
     */
    private String buildOpenSourceText(QueryTicketByDetrVo vo, String ticketNo, String passengerName, String passengerType, String xnBirthDate,
                                       MnjxPnrSeg firstSeg, MnjxPnrSeg lastSeg, MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr, String pnrNmId) {
        boolean pnrCancelled = "DEL".equals(pnr.getPnrStatus());

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .list();
        // 票中第一段是空的时候，说明第一段一定是SA航段
        if (firstSeg == null) {
            MnjxPnrSeg tmpLastSeg = lastSeg;
            firstSeg = pnrSegList.stream()
                    .filter(p -> p.getPnrSegNo() + 1 == tmpLastSeg.getPnrSegNo())
                    .collect(Collectors.toList())
                    .get(0);
        }
        // 票中第二段是空的时候，需要查询第一段的segNo+1是否存在
        if (lastSeg == null) {
            MnjxPnrSeg tmpFirstSeg = firstSeg;
            List<MnjxPnrSeg> tmpList = pnrSegList.stream()
                    .filter(p -> p.getPnrSegNo() == tmpFirstSeg.getPnrSegNo() + 1)
                    .collect(Collectors.toList());
            // 存在表示第二段是SA航段
            if (CollUtil.isNotEmpty(tmpList)) {
                lastSeg = tmpList.get(0);
            }
        }

        StringBuilder sb = new StringBuilder();
        // 第一行：ISSUED BY: ORG/DST: #{第一段出发城市三字码}/#{最后一段到达城市三字码} BSP-D
        sb.append("ISSUED BY: CHINA EASTERN             ORG/DST: ");

        MnjxCity orgCity = iSguiCommonService.getCityByAirportCode(firstSeg.getOrg());
        String orgCityCode = orgCity.getCityCode();
        String dstCityCode;
        if (ObjectUtil.isNotEmpty(lastSeg)) {
            MnjxCity dstCity = iSguiCommonService.getCityByAirportCode(lastSeg.getDst());
            dstCityCode = dstCity.getCityCode();
        } else {
            MnjxCity dstCity = iSguiCommonService.getCityByAirportCode(firstSeg.getDst());
            dstCityCode = dstCity.getCityCode();
        }
        sb.append(orgCityCode).append("/").append(dstCityCode);
        sb.append("                 BSP-D\r\n");

        // 第二行：E/R: Q/改退收费
        sb.append("E/R: Q/改退收费\r\n");

        // 第三行：TOUR CODE:
        sb.append("TOUR CODE:\r\n");

        // 第四行：PASSENGER: #{旅客姓名} #{如果是儿童，设置为CHD (CHILD)；如果是婴儿，设置为INF(婴儿出生月年，格式如DEC24)}
        sb.append("PASSENGER: ").append(passengerName.replace("CHD", "").trim());
        if ("CHD".equals(passengerType)) {
            sb.append(" CHD(CHILD)");
        } else if ("INF".equals(passengerType) && StrUtil.isNotEmpty(xnBirthDate)) {
            sb.append(" INF(").append(DateUtils.ym2Com(xnBirthDate)).append(")");
        } else if (StrUtil.equalsAny(passengerType, "GM", "JC", "GMJC")) {
            sb.append(" (GMJC)");
        }
        sb.append("\r\n");

        // 第五行：EXCH: CONJ TKT:
        String originalTicket = this.getOriginalTicket(pnrNmTicket, pnr.getPnrId());
        if (StrUtil.isNotEmpty(originalTicket)) {
            sb.append("EXCH: ").append(originalTicket);
            vo.getOriginalTicketNos().add(originalTicket);
        } else {
            sb.append("EXCH:                               ");
        }
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTicket.getPnrNmTnId())
                .list();
        vo.getConjunctionTicketNos().add(ticketNo);
        if (nmTicketList.size() > 1) {
            MnjxPnrNmTn tn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
            String inputValue = tn.getInputValue().replace("TN/", "").replaceAll("/P\\d+", "");
            inputValue = StrUtil.replaceLast(inputValue, "-", "/");
            sb.append("CONJ TKT: ").append(inputValue).append("\r\n");
            vo.getConjunctionTicketNos().add(inputValue);
        } else {
            sb.append("CONJ TKT:\r\n");
        }

        // 第六行：O FM:1#{第一段出发机场三字码} #{第一段航司} #{第一段无航司的航班号} #{第一段销售舱位} #{第一段航班日期} #{第一段起飞时间} OK #{第一段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
        // 第七行
        sb.append("O FM:1");
        sb.append(this.buildSegInfoSourceText(firstSeg, pnrCancelled, passengerType, pnrNmTicket, pnr));

        // 8行前可能有第二段的信息
        if (ObjectUtil.isNotEmpty(lastSeg)) {
            // 第六行：O FM:2#{第二段出发机场三字码} #{第二段航司} #{第二段无航司的航班号} #{第二段销售舱位} #{第二段航班日期} #{第二段起飞时间} OK #{第二段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
            sb.append("O FM:2");
            sb.append(this.buildSegInfoSourceText(lastSeg, pnrCancelled, passengerType, pnrNmTicket, pnr));

            // 第八行：TO: #{最后一段的到达机场三字码}
            sb.append("  TO: ").append(lastSeg.getDst()).append("\r\n");
        } else {
            // 第八行：TO: #{最后一段的到达机场三字码}
            sb.append("  TO: ").append(firstSeg.getDst()).append("\r\n");
        }

        // 第九行：FC:
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, ticketNo.replace("-", ""))
                .one();
        sb.append("FC: ").append(ticketPrice.getFcInfo().replace("FC/", "").replaceAll("/P\\d+", "")).append("\r\n");

        // 第十行：FARE: |FOP:
        // FN/A/FCNY3000.00/SCNY3000.00/C0.00/XCNY380.00/TCNY100.00CN/TCNY280.00YQ/ACNY3380.00
        // FN/A/RCNY3000.00/SCNY3000.00/C0.00/XCNY380.00/TCNY90.00OB/OCNY100.00CN/OCNY280.00YQ/ACNY380.00
        String fnInfo = ticketPrice.getFnInfo();
        String tCnPrice = "0.00";
        String tYqPrice = "0.00";
        String fPrice = "0.00";
        String aPrice = "0.00";
        String ob = "";

        if (ReUtil.isMatch(H_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(H_FN, fnInfo);
            fPrice = allGroups.get(2);
            tCnPrice = allGroups.get(6);
            tYqPrice = allGroups.get(7);
            aPrice = allGroups.get(8);
        } else if (ReUtil.isMatch(IN_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(IN_FN, fnInfo);
            fPrice = allGroups.get(3);
            aPrice = allGroups.get(8);
        } else if (ReUtil.isMatch(CH_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(CH_FN, fnInfo);
            fPrice = allGroups.get(2);
            tYqPrice = allGroups.get(7);
            aPrice = allGroups.get(8);
        } else if (ReUtil.isMatch(OI_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(OI_FN, fnInfo);
            fPrice = allGroups.get(3);
            ob = allGroups.get(7);
            tCnPrice = allGroups.get(8);
            tYqPrice = allGroups.get(9);
            aPrice = allGroups.get(10);
        }
        sb.append("FARE:           ")
                .append("CNY")
                .append(StrUtil.fill(fPrice, ' ', 8, true))
                .append("|FOP:CASH\r\n");

        if (new BigDecimal(tCnPrice).compareTo(BigDecimal.ZERO) != 0) {
            if (StrUtil.isNotEmpty(originalTicket)) {
                MnjxPnrRecord oiRecord = iMnjxPnrRecordService.lambdaQuery()
                        .like(MnjxPnrRecord::getInputValue, originalTicket)
                        .eq(MnjxPnrRecord::getPnrType, "OI")
                        .one();
                sb.append("TAX:            ")
                        .append("PD ")
                        .append(StrUtil.fill(tCnPrice, ' ', 6, true))
                        .append("CN");
                sb.append("|OI:").append(oiRecord.getInputValue().replace("OI/", " ").replaceAll("#\\d+", " ").replaceAll("/P\\d+", "")).append("\r\n");
            } else {
                sb.append("TAX:            ")
                        .append("CNY")
                        .append(StrUtil.fill(tCnPrice, ' ', 6, true))
                        .append("CN");
                sb.append("|OI:\r\n");
            }
        } else {
            sb.append("TAX:                       ").append("|OI:\r\n");
        }
        if (new BigDecimal(tYqPrice).compareTo(BigDecimal.ZERO) != 0) {
            if (StrUtil.isNotEmpty(originalTicket)) {
                sb.append("TAX:            ")
                        .append("PD ")
                        .append(StrUtil.fill(tYqPrice, ' ', 6, true))
                        .append("YQ")
                        .append("|\r\n");
                sb.append("TAX:            ")
                        .append("PD ")
                        .append(StrUtil.fill(ob, ' ', 6, true))
                        .append("OB")
                        .append("|\r\n");
            } else {
                sb.append("TAX:            ")
                        .append("CNY")
                        .append(StrUtil.fill(tYqPrice, ' ', 6, true))
                        .append("YQ")
                        .append("|\r\n");
            }
        }

        // 第十二行：TOTAL: |TKTN: #{票号}
        sb.append("TOTAL:          ")
                .append("CNY")
                .append(StrUtil.fill(NumberUtils.formatBigDecimalStr(aPrice), ' ', 8, true))
                .append("|TKTN: ").append(ticketNo).append("\r\n");

        // Base64编码
        return Base64.getEncoder().encodeToString(sb.toString().getBytes(StandardCharsets.UTF_8));
    }

    private String buildSegInfoSourceText(MnjxPnrSeg pnrSeg, boolean pnrCancelled, String passengerType, MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr) {
        StringBuilder sb = new StringBuilder();
        boolean segIsSa = "SA".equals(pnrSeg.getPnrSegType());
        // 改签只改了某个航段也会导致查票的时候该航段显示OPEN，可以利用pnrCancelled的规则处理
        if (!pnrCancelled) {
            pnrCancelled = "1".equals(pnrSeg.getExchanged());
        }
        // 第六行：O FM:1#{第一段出发机场三字码} #{第一段航司} #{第一段无航司的航班号} #{第一段销售舱位} #{第一段航班日期} #{第一段起飞时间} OK #{第一段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
        sb.append(pnrSeg.getOrg()).append(" ");
        if (segIsSa) {
            sb.append("  ");
        } else {
            sb.append(pnrSeg.getFlightNo().substring(0, 2));
        }
        sb.append("    ");

        if (segIsSa) {
            sb.append("VOID");
        } else {
            if (pnrCancelled) {
                sb.append("OPEN");
            } else {
                sb.append(pnrSeg.getFlightNo().substring(2));
            }
        }

        sb.append("  ");
        if (segIsSa) {
            sb.append(" ");
        } else {
            sb.append(pnrSeg.getSellCabin());
        }
        sb.append(" ");

        if (pnrCancelled) {
            if (segIsSa) {
                sb.append("VOID          ");
            } else {
                sb.append("OPEN          ");
            }
        } else {
            if (segIsSa) {
                sb.append("VOID").append("   ")
                        .append(" ")
                        .append("        ");
            } else {
                sb.append(DateUtils.ymd2Com(pnrSeg.getFlightDate()).substring(0, 5))
                        .append(" ")
                        .append(pnrSeg.getEstimateOff()).append(" OK ");
            }
        }

        if (segIsSa) {
            sb.append(" ");
        } else {
            sb.append(pnrSeg.getSellCabin());
        }

        if (!segIsSa) {
            if ("CHD".equals(passengerType)) {
                sb.append("CH50");
            } else if ("INF".equals(passengerType)) {
                sb.append("INF90");
            }
        }

        sb.append("                        ");
        if (segIsSa) {
            sb.append("    VOID").append("\r\n");
        } else {
            sb.append("20K ");
            if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) && pnrSeg.getPnrSegId().equals(pnrNmTicket.getS1Id())) {
                sb.append(pnrNmTicket.getTicketStatus1()).append("\r\n");
            } else if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) && pnrSeg.getPnrSegId().equals(pnrNmTicket.getS2Id())) {
                sb.append(pnrNmTicket.getTicketStatus2()).append("\r\n");
            }
        }

        // 第七行：RL:#{PNR CRS编码} /#{PNR ICS编码}1E
        if (pnrCancelled || segIsSa) {
            sb.append("          RL:\r\n");
        } else {
            sb.append("          RL:").append(pnr.getPnrCrs()).append("  /").append(pnr.getPnrIcs()).append("1E\r\n");
        }
        return sb.toString();
    }

    /**
     * 构建客票历史信息
     *
     * @param ticketNo      票号
     * @param passengerName 旅客姓名
     * @return 客票历史信息
     */
    private String buildTktHistoryText(String ticketNo, String passengerName, MnjxPnr pnr) {
        StringBuilder sb = new StringBuilder();

        // 格式化票号，添加"-"
        String formattedTicketNo = ticketNo;
        if (!ticketNo.contains("-") && ticketNo.length() == 13) {
            formattedTicketNo = ticketNo.substring(0, 3) + "-" + ticketNo.substring(3);
        }

        // 第一行：NAME: #{旅客姓名} TKTN:#{票号}
        sb.append("NAME: ").append(passengerName).append("  TKTN:").append(formattedTicketNo).append("\r\n");

        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, ticketNo.replace("-", ""))
                .one();
        String issueInfo = ticketPrice.getIssueInfo();
        String[] issueInfoSplit = issueInfo.split(" ");
        MnjxSi mnjxSi = iMnjxSiService.getById(issueInfoSplit[0]);
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        MnjxAgent mnjxAgent = iMnjxAgentService.getById(mnjxOffice.getOrgId());
        MnjxPrinter printer = iMnjxPrinterService.getById(issueInfoSplit[4]);

        // 第二行：IATA OFFC: #{IATA办公室} ISSUED: #{出票日期} RVAL: 00 EXP: #{过期日期}
        DateTime issuedDateTime = DateUtils.parse(issueInfoSplit[2] + " " + issueInfoSplit[3]);
        String issuedDate = DateUtils.ymd2Com(issueInfoSplit[2]);

        // 计算过期日期（出票日期加一年）
        DateTime expDateTime = DateUtils.offsetMonth(issuedDateTime, 12);
        String expDate = DateUtils.format(expDateTime, "yyyy-MM-dd");
        expDate = DateUtils.ymd2Com(expDate);

        sb.append("IATA OFFC: ").append(mnjxAgent.getAgentIata()).append(" ISSUED: ").append(issuedDate).append(" RVAL: 00 EXP: ").append(expDate).append("\r\n");

        Map<Integer, String> historyTextMap = new HashMap<>();

        // 查询客票操作记录，构建客票历史信息
        List<MnjxTicketOperateRecord> operateRecordList = iMnjxTicketOperateRecordService.lambdaQuery()
                .eq(MnjxTicketOperateRecord::getTicketNo, ticketNo.replace("-", ""))
                .orderByAsc(MnjxTicketOperateRecord::getOperateTime)
                .list();
        String allSegInfo = ticketPrice.getSegInfo();
        String selectedSegNo = issueInfoSplit[5];
        String firstSegInfo = "";
        String lastSegInfo = "";
        String[] selectSplit = selectedSegNo.split("-");
        if (selectSplit.length == 1) {
            firstSegInfo = allSegInfo.split("/")[Integer.parseInt(selectSplit[0]) - 1];
        } else {
            firstSegInfo = allSegInfo.split("/")[Integer.parseInt(selectSplit[0]) - 1];
            lastSegInfo = allSegInfo.split("/")[Integer.parseInt(selectSplit[1]) - 1];
        }
        if (StrUtil.isNotEmpty(firstSegInfo) && "SA".equals(firstSegInfo.split(" ")[0])) {
            firstSegInfo = "";
        }
        if (StrUtil.isNotEmpty(lastSegInfo) && "SA".equals(lastSegInfo.split(" ")[0])) {
            lastSegInfo = "";
        }

        int index = 2;
        if (CollUtil.isNotEmpty(operateRecordList) && operateRecordList.size() > 1) {
            boolean hasBuildDelHistory = false;
            if (operateRecordList.stream().anyMatch(o -> StrUtil.equalsAny(Constant.TICKET_STATUS_EXCHANGED, o.getTicketStatus1(), o.getTicketStatus2()))) {
                index = this.buildDelHistory(historyTextMap, index, pnr, firstSegInfo, lastSegInfo, mnjxSi);
                hasBuildDelHistory = true;
            }
            for (int i = 0; i < operateRecordList.size() - 1; i++) {
                MnjxTicketOperateRecord record = operateRecordList.get(i);
                MnjxTicketOperateRecord nextRecord = operateRecordList.get(i + 1);

                DateTime dateTime = DateUtil.parse(nextRecord.getOperateTime());

                // 如果PNR被删除，需要额外构建删除历史信息
                if ("DEL".equals(pnr.getPnrStatus()) && DateUtil.compare(pnr.getUpdateTime(), DateUtil.parse(record.getOperateTime())) < 0 && !hasBuildDelHistory) {
                    index = this.buildDelHistory(historyTextMap, index, pnr, firstSegInfo, lastSegInfo, mnjxSi);
                    hasBuildDelHistory = true;
                }

                String date = DateUtil.format(dateTime, "yyyy-MM-dd");
                date = DateUtils.ymd2Com(date).substring(0, 5);
                String time = DateUtil.format(dateTime, "HHmm");

                if (StrUtil.isNotEmpty(firstSegInfo) && !record.getTicketStatus1().equals(nextRecord.getTicketStatus1())) {

                    String operateSb = "  " +
                            StrUtil.toString(index) +
                            " " +
                            selectSplit[0] +
                            "  " +
                            date +
                            "/" +
                            time +
                            "/" +
                            mnjxAgent.getAgentIata() +
                            " CRSU " +
                            record.getTicketStatus1().substring(0, 1).toUpperCase() +
                            "/" +
                            nextRecord.getTicketStatus1().substring(0, 1).toUpperCase() +
                            "  ";
                    if (StrUtil.equalsAny(Constant.TICKET_STATUS_EXCHANGED, record.getTicketStatus1(), nextRecord.getTicketStatus1())) {
                        // 查改签的TN信息
                        MnjxPnrRecord exchangedRecordTn = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrType, "TN")
                                .like(MnjxPnrRecord::getInputValue, ticketNo)
                                .eq(MnjxPnrRecord::getChangeMark, "X")
                                .one();
                        // 获取变动封口号
                        String changeAtNo = exchangedRecordTn.getChangeAtNo();
                        // 用pnrNmId查TN信息，获取该旅客的所有TN，筛选变动封口号大于当前封口号的数据，为改签后产生的票号TN，如果查不到，则查变动封口号为空的
                        List<MnjxPnrRecord> recordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrType, "TN")
                                .eq(MnjxPnrRecord::getPnrNmId, exchangedRecordTn.getPnrNmId())
                                .orderByAsc(MnjxPnrRecord::getChangeAtNo)
                                .list();
                        if (recordList.stream().anyMatch(r -> StrUtil.isNotEmpty(r.getChangeAtNo()) && Integer.parseInt(r.getChangeAtNo()) > Integer.parseInt(changeAtNo))) {
                            for (MnjxPnrRecord pnrRecord : recordList) {
                                if (StrUtil.isNotEmpty(pnrRecord.getChangeAtNo()) && Integer.parseInt(pnrRecord.getChangeAtNo()) > Integer.parseInt(changeAtNo)) {
                                    operateSb = operateSb + pnrRecord.getInputValue().replace("TN/", "").replace("IN/", "").replaceAll("/P\\d+", "") +
                                            "\r\n";
                                    break;
                                }
                            }
                        } else {
                            MnjxPnrRecord nextTnRecord = recordList.stream()
                                    .filter(r -> StrUtil.isEmpty(r.getChangeAtNo()))
                                    .collect(Collectors.toList())
                                    .get(0);
                            operateSb = operateSb + nextTnRecord.getInputValue().replace("TN/", "").replace("IN/", "").replaceAll("/P\\d+", "") +
                                    "\r\n";
                        }
                    } else if (StrUtil.equalsAny(Constant.TICKET_STATUS_SUSPENDED, record.getTicketStatus1(), nextRecord.getTicketStatus1())) {
                        operateSb = operateSb + "\r\n";
                    } else if (StrUtil.isNotEmpty(nextRecord.getRefundNo())) {
                        operateSb = operateSb + nextRecord.getRefundNo() +
                                "\r\n";
                    } else {
                        operateSb = operateSb + "\r\n";
                    }

                    historyTextMap.put(index, operateSb);
                    index++;
                }
                else if (StrUtil.isNotEmpty(lastSegInfo) && !record.getTicketStatus2().equals(nextRecord.getTicketStatus2())) {
                    String operateSb = "  " +
                            StrUtil.toString(index) +
                            " " +
                            selectSplit[1] +
                            "  " +
                            date +
                            "/" +
                            time +
                            "/" +
                            mnjxAgent.getAgentIata() +
                            " CRSU " +
                            record.getTicketStatus2().substring(0, 1).toUpperCase() +
                            "/" +
                            nextRecord.getTicketStatus2().substring(0, 1).toUpperCase() +
                            "  ";
                    if (StrUtil.equalsAny(Constant.TICKET_STATUS_EXCHANGED, record.getTicketStatus2(), nextRecord.getTicketStatus2())) {
                        // 查改签的TN信息
                        MnjxPnrRecord exchangedRecordTn = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrType, "TN")
                                .like(MnjxPnrRecord::getInputValue, ticketNo)
                                .eq(MnjxPnrRecord::getChangeMark, "X")
                                .one();
                        // 获取变动封口号
                        String changeAtNo = exchangedRecordTn.getChangeAtNo();
                        // 用pnrNmId查TN信息，获取该旅客的所有TN，筛选变动封口号大于当前封口号的数据，为改签后产生的票号TN，如果查不到，则查变动封口号为空的
                        List<MnjxPnrRecord> recordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrType, "TN")
                                .eq(MnjxPnrRecord::getPnrNmId, exchangedRecordTn.getPnrNmId())
                                .orderByAsc(MnjxPnrRecord::getChangeAtNo)
                                .list();
                        if (recordList.stream().anyMatch(r -> StrUtil.isNotEmpty(r.getChangeAtNo()) && Integer.parseInt(r.getChangeAtNo()) > Integer.parseInt(changeAtNo))) {
                            for (MnjxPnrRecord pnrRecord : recordList) {
                                if (StrUtil.isNotEmpty(pnrRecord.getChangeAtNo()) && Integer.parseInt(pnrRecord.getChangeAtNo()) > Integer.parseInt(changeAtNo)) {
                                    operateSb = operateSb + pnrRecord.getInputValue().replace("TN/", "").replace("IN/", "").replaceAll("/P\\d+", "") +
                                            "\r\n";
                                    break;
                                }
                            }
                        } else {
                            MnjxPnrRecord nextTnRecord = recordList.stream()
                                    .filter(r -> StrUtil.isEmpty(r.getChangeAtNo()))
                                    .collect(Collectors.toList())
                                    .get(0);
                            operateSb = operateSb + nextTnRecord.getInputValue().replace("TN/", "").replace("IN/", "").replaceAll("/P\\d+", "") +
                                    "\r\n";
                        }
                    } else if (StrUtil.equalsAny(Constant.TICKET_STATUS_SUSPENDED, record.getTicketStatus2(), nextRecord.getTicketStatus2())) {
                        operateSb = operateSb + "\r\n";
                    } else if (StrUtil.isNotEmpty(nextRecord.getRefundNo())) {
                        operateSb = operateSb + nextRecord.getRefundNo() +
                                "\r\n";
                    } else {
                        operateSb = operateSb + "\r\n";
                    }

                    historyTextMap.put(index, operateSb);
                    index++;
                }
            }
        }
        // 如果PNR被删除，需要额外构建删除历史信息
        else if ("DEL".equals(pnr.getPnrStatus())) {
            index = this.buildDelHistory(historyTextMap, index, pnr, firstSegInfo, lastSegInfo, mnjxSi);
        }

        if (MapUtil.isNotEmpty(historyTextMap)) {
            // 按key从大到小排序
            List<Integer> keys = historyTextMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
            for (Integer key : keys) {
                sb.append(historyTextMap.get(key));
            }
        }

        // 最后一行：#{序号} #{出票时间}/#{终端ID} TRMK #{备注}
        sb.append("  ")
                .append("1")
                .append("    ")
                .append(issuedDate.substring(0, 5))
                .append("/")
                .append(DateUtils.format(issuedDateTime, "HHmm"))
                .append("/")
                .append(mnjxSi.getSiNo())
                .append("     TRMK XIZANG JIASHENG+")
                .append(mnjxOffice.getOfficeNo())
                .append("+DEV-")
                .append(printer.getPrinterNo())
                .append("\r\n");
        return sb.toString();
    }

    private int buildDelHistory(Map<Integer, String> historyTextMap, int index, MnjxPnr pnr, String firstSegInfo, String secondSegInfo, MnjxSi mnjxSi) {
        String otherSb = "1" +
                "  " +
                DateUtils.ymd2Com(DateUtil.format(pnr.getUpdateTime(), "yyyy-MM-dd")).substring(0, 5) +
                "/" +
                DateUtils.format(pnr.getUpdateTime(), "HHmm") +
                "/" +
                mnjxSi.getSiNo() +
                "     " +
                "EOTU" +
                " ";
        // CZ3349 SHA CTU 2025-07-20 K 0810 1135
        if (StrUtil.isAllNotEmpty(firstSegInfo, secondSegInfo)) {
            String[] firstSplit = firstSegInfo.split(" ");
            String firstFlightNo = firstSplit[0];
            String firstOrg = firstSplit[1];
            String firstDst = firstSplit[2];
            String firstFlightDate = firstSplit[3];
            String firstSellCabin = firstSplit[4];
            String[] secondSplit = secondSegInfo.split(" ");
            String secondFlightNo = secondSplit[0];
            String secondOrg = secondSplit[1];
            String secondDst = secondSplit[2];
            String secondFlightDate = secondSplit[3];
            String secondSellCabin = secondSplit[4];
            StringBuilder sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("RES RL ")
                    .append(pnr.getPnrIcs())
                    .append("   CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("MKG RL ")
                    .append(pnr.getPnrCrs())
                    .append("1E CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append(StrUtil.format("CHG FLT FROM {}/{}/{}/{} TO\r\n", firstFlightNo, DateUtils.ymd2Com(firstFlightDate), firstSellCabin, firstOrg + firstDst))
                    .append("                               ")
                    .append(StrUtil.format("{}OPEN/OPEN/{}/{}\r\n", firstFlightNo.substring(0, 2), firstSellCabin, firstOrg + firstDst));
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("RES RL ")
                    .append(pnr.getPnrIcs())
                    .append("   CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("MKG RL ")
                    .append(pnr.getPnrCrs())
                    .append("1E CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append(StrUtil.format("CHG FLT FROM {}/{}/{}/{} TO\r\n", secondFlightNo, DateUtils.ymd2Com(secondFlightDate), secondSellCabin, secondOrg + secondDst))
                    .append("                               ")
                    .append(StrUtil.format("{}OPEN/OPEN/{}/{}\r\n", secondFlightNo.substring(0, 2), secondSellCabin, secondOrg + secondDst));
            historyTextMap.put(index, sb.toString());
            index++;
        } else if (StrUtil.isNotEmpty(firstSegInfo)) {
            String[] firstSplit = firstSegInfo.split(" ");
            String firstFlightNo = firstSplit[0];
            String firstOrg = firstSplit[1];
            String firstDst = firstSplit[2];
            String firstFlightDate = firstSplit[3];
            String firstSellCabin = firstSplit[4];
            StringBuilder sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("RES RL ")
                    .append(pnr.getPnrIcs())
                    .append("   CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("MKG RL ")
                    .append(pnr.getPnrCrs())
                    .append("1E CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append(StrUtil.format("CHG FLT FROM {}/{}/{}/{} TO\r\n", firstFlightNo, DateUtils.ymd2Com(firstFlightDate), firstSellCabin, firstOrg + firstDst))
                    .append("                               ")
                    .append(StrUtil.format("{}OPEN/OPEN/{}/{}\r\n", firstFlightNo.substring(0, 2), firstSellCabin, firstOrg + firstDst));
            historyTextMap.put(index, sb.toString());
            index++;
        } else if (StrUtil.isNotEmpty(secondSegInfo)) {
            String[] secondSplit = secondSegInfo.split(" ");
            String secondFlightNo = secondSplit[0];
            String secondOrg = secondSplit[1];
            String secondDst = secondSplit[2];
            String secondFlightDate = secondSplit[3];
            String secondSellCabin = secondSplit[4];
            StringBuilder sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("RES RL ")
                    .append(pnr.getPnrIcs())
                    .append("   CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append("MKG RL ")
                    .append(pnr.getPnrCrs())
                    .append("1E CLEARED\r\n");
            historyTextMap.put(index, sb.toString());
            index++;

            sb = new StringBuilder();
            sb.append("  ")
                    .append(StrUtil.toString(index))
                    .append(" ")
                    .append(otherSb)
                    .append(StrUtil.format("CHG FLT FROM {}/{}/{}/{} TO\r\n", secondFlightNo, DateUtils.ymd2Com(secondFlightDate), secondSellCabin, secondOrg + secondDst))
                    .append("                               ")
                    .append(StrUtil.format("{}OPEN/OPEN/{}/{}\r\n", secondFlightNo.substring(0, 2), secondSellCabin, secondOrg + secondDst));
            historyTextMap.put(index, sb.toString());
            index++;
        }
        return index;
    }

    /**
     * 构建证件信息
     *
     * @param ticketNo      票号
     * @param passengerName 旅客姓名
     * @param passengerType 旅客类型
     * @param xnBirthDate   出生日期（婴儿）
     * @return 证件信息
     */
    private QueryTicketByDetrVo.Credential buildCredential(String ticketNo, String passengerName, String passengerType, List<MnjxNmSsr> foidList, String xnBirthDate) {
        QueryTicketByDetrVo.Credential credential = new QueryTicketByDetrVo.Credential();

        // 构建证件文本
        StringBuilder sb = new StringBuilder();

        // 格式化票号，添加"-"
        String formattedTicketNo = ticketNo;
        if (!ticketNo.contains("-") && ticketNo.length() == 13) {
            formattedTicketNo = ticketNo.substring(0, 3) + "-" + ticketNo.substring(3);
        }

        // 第一行：票号： #{票号}
        sb.append("票号： ").append(formattedTicketNo).append("\n");

        // SSR DOCS FM HK1 IP/CN/1122233/CN/06JUN24/MI/14AUG25/QQ/QQ/H/P1
        // SSR FOID CA HK1 NI110105201605091560/P1
        // SSR FOID CA HK1 UU123111211/P1
        String foidInputValue = "";
        if ("INF".equals(passengerType)) {
            List<MnjxNmSsr> inftDocs = foidList.stream()
                    .filter(s -> "DOCS".equals(s.getSsrType()) && (s.getInputValue().contains("/FI/") || s.getInputValue().contains("/MI/")))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(inftDocs)) {
                foidInputValue = inftDocs.get(0).getInputValue();
            } else {
                List<MnjxNmSsr> otherFoid = foidList.stream()
                        .filter(s -> "FOID".equals(s.getSsrType()) || ("DOCS".equals(s.getSsrType()) && !s.getInputValue().contains("/FI/") && !s.getInputValue().contains("/MI/")))
                        .collect(Collectors.toList());
                foidInputValue = otherFoid.get(0).getInputValue();
            }
        } else {
            List<MnjxNmSsr> otherFoid = foidList.stream()
                    .filter(s -> "FOID".equals(s.getSsrType()) || ("DOCS".equals(s.getSsrType()) && !s.getInputValue().contains("/FI/") && !s.getInputValue().contains("/MI/")))
                    .collect(Collectors.toList());
            foidInputValue = otherFoid.get(0).getInputValue();
        }

        // 第二行：乘机人姓名： #{旅客姓名}
        sb.append("乘机人姓名： ");
        if ("CHD".equals(passengerType)) {
            sb.append(passengerName.replace("CHD", "").trim()).append(" CHD");
        } else if ("INF".equals(passengerType)) {
            sb.append(passengerName).append(" INF(").append(DateUtils.ym2Com(xnBirthDate)).append(")");
        } else {
            sb.append(passengerName);
        }
        sb.append("\n");

        // 第三行：证件号码
        if (foidInputValue.contains(" NI")) {
            String niNo = foidInputValue.split(" NI")[1].split("/")[0];
            sb.append("NI").append(niNo);
        } else if (foidInputValue.contains(" UU")) {
            String niNo = foidInputValue.split(" UU")[1].split("/")[0];
            sb.append("UU").append(niNo);
        } else if (foidInputValue.contains(" DOCS ")) {
            String niNo = foidInputValue.split("/")[2];
            String type = foidInputValue.split("/")[0].split(" ")[foidInputValue.split("/")[0].split(" ").length - 1];
            sb.append(type).append(niNo);
        }

        // Base64编码
        credential.setCertificatesText(Base64.getEncoder().encodeToString(sb.toString().getBytes(StandardCharsets.UTF_8)));

        // 构建证件列表
        List<QueryTicketByDetrVo.Certificate> certificatesList = new ArrayList<>();
        QueryTicketByDetrVo.Certificate certificate = new QueryTicketByDetrVo.Certificate();
        certificate.setCertType("NI");
        certificate.setCertNumber("************");
        certificate.setEncryptCertNumber("7741FEA60721501A318B6522524D51470C72C5129D6E58A8C069713104031984CDC5C2BDB679129DB20142522DEC54674EE1DB62A8652C957A89FB651AC37DC1C2012FD8544E47E5560711C3355C888AB4FA7A1073C3E70546E084434E63B011660D84FD39ACEE0CEE9C4B1FF269A35F207E");

        if ("INF".equals(passengerType)) {
            certificate.setBirthday(xnBirthDate.replace("-", ""));
        }
//        else {
//            String birthDateStr = niNo.substring(6, 14);
//            String birthYear = birthDateStr.substring(0, 4);
//            String birthMonth = birthDateStr.substring(4, 6);
//            String birthDay = birthDateStr.substring(6, 8);
//            certificate.setBirthday(birthYear + birthMonth + birthDay);
//        }
        certificatesList.add(certificate);

        credential.setCertificatesList(certificatesList);

        return credential;
    }

    /**
     * 构建TicketByRtktVo对象
     *
     * @param pnr
     * @param pnrNmTicket 票务信息
     * @param pnrNm       旅客信息
     * @return TicketByRtktVo
     */
    private TicketByRtktVo buildTicketByRtktVo(MnjxPnr pnr, MnjxPnrNmTicket pnrNmTicket, MnjxPnrNm pnrNm) {
        TicketByRtktVo vo = new TicketByRtktVo();
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, pnrNmTicket.getTicketNo())
                .one();

        String issueInfo = ticketPrice.getIssueInfo();
        String[] issueInfoSplit = issueInfo.split(" ");
        MnjxSi mnjxSi = iMnjxSiService.getById(issueInfoSplit[0]);
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        MnjxAgent mnjxAgent = iMnjxAgentService.getById(mnjxOffice.getOrgId());
        MnjxPrinter printer = iMnjxPrinterService.getById(issueInfoSplit[4]);
        // 构建票务信息
        TicketByRtktVo.Ticket ticket = new TicketByRtktVo.Ticket();
        ticket.setIssueType("    " + mnjxAgent.getAgentContactAddress() + " " + mnjxOffice.getOfficeNo() + "                  DEV-" + printer.getPrinterNo() + "         ");
        ticket.setIataCode(mnjxAgent.getAgentIata());
        ticket.setOffice(mnjxOffice.getOfficeNo());
        ticket.setIssueAirline(issueInfoSplit[1]);

        ticket.setTicketState("issue");
        ticket.setCode(pnr.getPnrCrs());
        ticket.setIssueDate(issueInfoSplit[2] + " " + issueInfoSplit[3]);
        ticket.setRecordCreateDateTime(DateUtils.ymd2Com(issueInfoSplit[2]));
        ticket.setAccountNumber(mnjxSi.getSiNo());
        ticket.setTicketNumber(pnrNmTicket.getTicketNo());
        ticket.setEi("变更退票收费");
        ticket.setIssueSignCode("CN");
        ticket.setPrintNumber(printer.getPrinterNo());
        ticket.setInternationalIndicator("D");
        ticket.setIssueAirlineCode(pnrNmTicket.getTicketNo().substring(0, 3));
//        ticket.setFp("CASH,CNY");
        ticket.setTicketManagementOrganizationCode("BSP");
        ticket.setTicketType("");
        ticket.setRefundVoidTag("");
//        ticket.setTicketManagementOrganizationCodeByRtkt("BSP");
        ticket.setDescribe(StrUtil.format("{}\n{}\nDEV-{}", mnjxAgent.getAgentContactAddress(), mnjxOffice.getOfficeNo(), printer.getPrinterNo()));
        if (StrUtil.equalsAny(Constant.TICKET_STATUS_REFOUND, pnrNmTicket.getTicketStatus1(), pnrNmTicket.getTicketStatus2())) {
//            ticket.setRefundPrintNumber(printer.getPrinterNo());
            ticket.setTicketState("refund");
        }
        if (StrUtil.equalsAny(Constant.TICKET_STATUS_EXCHANGED, pnrNmTicket.getTicketStatus1(), pnrNmTicket.getTicketStatus2())) {
            // 改签的状态
            ticket.setTicketState("exchange");
        }
        if (Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1())) {
            if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2())) {
                if (Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
//                    ticket.setRefundPrintNumber(printer.getPrinterNo());
                }
            } else {
//                ticket.setRefundPrintNumber(printer.getPrinterNo());
            }
        }

        ticket.setOriginalTicket(this.getOriginalTicket(pnrNmTicket, pnr.getPnrId()));

//        ticket.setTicketTypeByRtkt("BSP");
        vo.setTicket(ticket);

        // 构建旅客信息
        TicketByRtktVo.Passenger passenger = new TicketByRtktVo.Passenger();
        passenger.setPassengerName("/");

        // 判断旅客类型
//        String passengerType = "ADT";
//        if (pnrNm != null) {
//        MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
//                .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
//                .eq(MnjxNmSsr::getSsrType, "CHLD")
//                .one();
//            if (ObjectUtil.isNotEmpty(chldSsr)) {
//                passengerType = "CHD";
//            }
//        }
//        passenger.setPassengerType(passengerType);

        // 构建航段信息
        List<TicketByRtktVo.Segment> segments = this.constructRtktPassengerSegment(pnr, ticketPrice.getSegInfo(), issueInfoSplit[5]);
        passenger.setSegments(segments);

        ticket.setOrigin(iSguiCommonService.getCityByAirportCode(segments.get(0).getDepartureCity()).getCityCode());
        ticket.setDestination(iSguiCommonService.getCityByAirportCode(segments.get(segments.size() - 1).getArrivalCity()).getCityCode());

        // 构建连接航段信息
//        List<TicketByRtktVo.Segment> conjunctionSegments = this.constructRtktPassengerSegment(pnr, ticketPrice.getSegInfo(), issueInfoSplit[5], true);
//        conjunctionSegments.forEach(c -> c.setTicketNo(ticket.getTicketNumber()));
//        passenger.setConjunctionSegments(conjunctionSegments);

        vo.setPassenger(passenger);

        // 构建价格信息
        BigDecimal scny = BigDecimal.ZERO;
        BigDecimal fareAmount = BigDecimal.ZERO;
        BigDecimal taxCn = BigDecimal.ZERO;
        BigDecimal taxYq = BigDecimal.ZERO;
        String fnInfo = ticketPrice.getFnInfo();
        if (ReUtil.isMatch(H_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(H_FN, fnInfo);
            scny = new BigDecimal(allGroups.get(2));
            taxCn = new BigDecimal(allGroups.get(6));
            taxYq = new BigDecimal(allGroups.get(7));
            fareAmount = new BigDecimal(allGroups.get(8));
        } else if (ReUtil.isMatch(IN_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(IN_FN, fnInfo);
            scny = new BigDecimal(allGroups.get(4));
            fareAmount = new BigDecimal(allGroups.get(8));
        } else if (ReUtil.isMatch(CH_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(CH_FN, fnInfo);
            scny = new BigDecimal(allGroups.get(3));
            taxYq = new BigDecimal(allGroups.get(7));
            fareAmount = new BigDecimal(allGroups.get(8));
        } else if (ReUtil.isMatch(OI_FN, fnInfo)) {
            List<String> allGroups = ReUtil.getAllGroups(OI_FN, fnInfo);
            scny = new BigDecimal(allGroups.get(4));
            taxCn = new BigDecimal(allGroups.get(8));
            taxYq = new BigDecimal(allGroups.get(9));
            fareAmount = new BigDecimal(allGroups.get(10));
        }
        BigDecimal taxAmount = taxCn.add(taxYq);

        String fc = ticketPrice.getFcInfo();
        TicketByRtktVo.Price price = new TicketByRtktVo.Price();
        price.setNegotiatedFareCode(null);
        price.setQueryExclusiveNegotiatedFare(false);
        price.setFc(fc.replace("FC/", "").replace("A/", "").replaceAll("/P\\d+", ""));
        price.setTaxAmount("CNY" + taxAmount);
        price.setTaxDetail("CN：CNY" + taxCn + " / YQ：CNY" + taxYq);

        // 构建税费列表
        List<TicketByRtktVo.Tax> taxes = new ArrayList<>();
        TicketByRtktVo.Tax tax1 = new TicketByRtktVo.Tax();
        tax1.setTaxCode("CN");
        tax1.setTaxAmount(taxCn.toString());
        tax1.setCurrencyCode("CNY");
        tax1.setNewOldRefundTax("T");
        taxes.add(tax1);

        TicketByRtktVo.Tax tax2 = new TicketByRtktVo.Tax();
        tax2.setTaxCode("YQ");
        tax2.setTaxAmount(taxYq.toString());
        tax2.setCurrencyCode("CNY");
        tax2.setNewOldRefundTax("T");
        taxes.add(tax2);

        price.setTaxes(taxes);
        price.setTotalTaxAmount(taxAmount.toString());
        price.setNewTaxTotalAmount(taxAmount.toString());

        TicketByRtktVo.OriginalTicketInfo originalTicketInfo = new TicketByRtktVo.OriginalTicketInfo();
        if (StrUtil.isNotEmpty(ticket.getOriginalTicket())) {
            originalTicketInfo.setTicketNumber(ticket.getOriginalTicket());
            originalTicketInfo.setCityCode(mnjxOffice.getOfficeNo().substring(0, 3));
            originalTicketInfo.setIataNumber(mnjxAgent.getAgentIata());
            originalTicketInfo.setIssueDate(issueInfoSplit[2] + " " + issueInfoSplit[3]);
        }
        ticket.setOriginalTicketInfo(originalTicketInfo);
        price.setFsnAmount(scny.toString());
        price.setTicketAmount("");
//        price.setCommissionFare("CNY0.00");
        price.setCommissionFareOrRate("0.00");
        price.setCommissionMode(true);
        price.setGpSign("");

        // 计算总价
        price.setFareAmount(fareAmount.toString());
        price.setFareAmountCode("CNY");

        price.setCreditCardDetail("");
        price.setCurrency("CNY");
        price.setPaymentTypeCode("CASH");
        price.setCreditCard(null);
        price.setScny("CNY" + scny);
        price.setScnyNoCode(scny.toString());
        price.setTc("");
        price.setTicketAmountFOrRAmount(scny.toString());
        price.setTicketAmountFOrRCode("CNY");
        price.setAutoFareType("A");
//        price.setTicketAmountOri("0");
        price.setFormOfPaymentText("CASH,CNY");
        vo.setPrice(price);

        return vo;
    }

    private String getOriginalTicket(MnjxPnrNmTicket pnrNmTicket, String pnrId) {
        String orginalTicket = "";
        // 改签后原票号
        List<MnjxPnrRecord> oiRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnrId)
                .eq(MnjxPnrRecord::getPnrType, "OI")
                .eq(MnjxPnrRecord::getChangeMark, "X")
                .orderByDesc(MnjxPnrRecord::getChangeAtNo)
                .list();
        if (CollUtil.isNotEmpty(oiRecordList)) {
            if (oiRecordList.stream().anyMatch(o -> o.getInputValue().contains(pnrNmTicket.getTicketNo().substring(3)))) {
                // 查的是改签票，且该票也是改签后产生的票
                // 获取这次改签的封口次数
                String changeAtNo = oiRecordList.stream()
                        .filter(o -> "OI".equals(o.getPnrType()) && "X".equals(o.getChangeMark()) && o.getInputValue().contains(pnrNmTicket.getTicketNo().substring(3)))
                        .collect(Collectors.toList())
                        .get(0)
                        .getChangeAtNo();
                // 找最近的上个封口次数的oi信息，里面为原票号
                for (MnjxPnrRecord record : oiRecordList) {
                    if (Integer.parseInt(record.getChangeAtNo()) < Integer.parseInt(changeAtNo)) {
                        orginalTicket = record.getInputValue().replace("IN/", "").split("OI/")[1].split("#")[0];
                        break;
                    }
                }
            } else {
                // 可能多次改签，当前正常票号的原始票号找最近的一条oi记录
                List<String> oiTicketNoList = oiRecordList.stream()
                        .map(o -> o.getInputValue().split("OI/")[1].split("#")[0])
                        .collect(Collectors.toList());
                orginalTicket = oiTicketNoList.get(0);
            }
        }
        return orginalTicket;
    }

    /**
     * Title: constructRtktPassengerSegment
     * Description: 构建passenger中的segments和conjunctionSegments<br>
     *
     * @param pnr
     * @param segInfo
     * @param selectedSeg
     * @return {@link TicketByRtktVo.Segment}
     * <AUTHOR>
     * @date 2025/5/23 15:22
     */
    private List<TicketByRtktVo.Segment> constructRtktPassengerSegment(MnjxPnr pnr, String segInfo, String selectedSeg) {
        List<TicketByRtktVo.Segment> segmentList = new ArrayList<>();
        // 1-2  3-4
        String[] selectSplit = selectedSeg.split("-");
        // HO1071 SHA CSX 2025-07-18 K 2100 2300/ZH9508 SHA SZX 2025-07-17 K 0900 1200/FM9397 SHA CSX 2025-07-17 P 1500 1845
        String[] segInfoSplit = segInfo.split("/");
        for (String selectSegNo : selectSplit) {
            // ZH9508 SHA SZX 2025-07-17 K 0900 1200
            String selectSegInfo = segInfoSplit[Integer.parseInt(selectSegNo) - 1];
            if ("SA".equals(selectSegInfo.split(" ")[0])) {
                continue;
            }
            String[] infoSplit = selectSegInfo.split(" ");
            TicketByRtktVo.Segment segment = new TicketByRtktVo.Segment();

            segment.setDepartureCity(infoSplit[1]);
            segment.setDepartureCityName(iSguiCommonService.getCityByAirportCode(infoSplit[1]).getCityCname());
            segment.setArrivalCity(infoSplit[2]);
            segment.setArrivalCityName(iSguiCommonService.getCityByAirportCode(infoSplit[2]).getCityCname());
            segment.setDepartureDateTime(infoSplit[3] + " " + infoSplit[5].substring(0, 2) + ":" + infoSplit[5].substring(2));
            segment.setDepartureDate(DateUtils.ymd2Com(infoSplit[3]));
            segment.setDepartureTime(infoSplit[5]);

            String arrivalDate = infoSplit[3];
            if (Integer.parseInt(infoSplit[5]) > Integer.parseInt(infoSplit[6])) {
                DateTime offset = DateUtil.parseDate(arrivalDate).offset(DateField.DAY_OF_YEAR, 1);
                arrivalDate = DateUtil.format(offset, DatePattern.NORM_DATE_PATTERN);
            }
            segment.setArrivalDateTime(arrivalDate + " " + infoSplit[6].substring(0, 2) + ":" + infoSplit[6].substring(2));
            segment.setArrivalDate(DateUtils.ymd2Com(arrivalDate));
            segment.setArrivalTime(infoSplit[6]);
            segment.setCabin(infoSplit[4]);
            segment.setFlightNo(infoSplit[0]);
            segment.setAirline(infoSplit[0].substring(0, 2));
            segment.setFlightNoWithAirline(infoSplit[0].substring(2));
            segment.setFareBasis(infoSplit[4]);
            segment.setBaggage("20K");
            if ("DEL".equals(pnr.getPnrStatus())) {
                segment.setFlightNo(null);
                segment.setDepartureDateTime(null);
                segment.setArrivalDateTime(null);
            }
            segment.setCodeshareType("0");
            segment.setReservationStatusCode("OK");
            segmentList.add(segment);
        }
        return segmentList;
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            return Collections.emptyList();
        }
        if ("DEL".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("查询PNR被取消");
        }

        // 查询PNR下的所有旅客
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();

        if (CollUtil.isEmpty(pnrNmList)) {
            return Collections.emptyList();
        }

        // 查询PNR下的所有航段
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isEmpty(pnrSegList)) {
            return Collections.emptyList();
        }

        List<String> pnrNmIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxNmOsi> allNmOsiList = iMnjxNmOsiService.lambdaQuery()
                .in(MnjxNmOsi::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmRmk> allNmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxPnrNmTn> allNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmFn> allNmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .list();
        // 构建返回结果
        List<QueryTicketByPnrVo> resultList = new ArrayList<>();

        // 处理成人和儿童旅客
        for (MnjxPnrNm pnrNm : pnrNmList) {
            String pnrNmId = pnrNm.getPnrNmId();
            // 查询旅客的票务信息
            List<MnjxPnrNmTn> nmTnList = allNmTnList.stream()
                    .filter(t -> pnrNmId.equals(t.getPnrNmId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(nmTnList)) {
                continue;
            }

            List<MnjxNmRmk> nmRmkList = allNmRmkList.stream()
                    .filter(r -> pnrNmId.equals(r.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxNmFn> nmFnList = allNmFnList.stream()
                    .filter(f -> pnrNmId.equals(f.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxNmOsi> nmOsiList = allNmOsiList.stream()
                    .filter(o -> pnrNmId.equals(o.getPnrNmId()))
                    .collect(Collectors.toList());
            for (MnjxPnrNmTn pnrNmTn : nmTnList) {
                List<MnjxPnrNmTicket> pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                        .list();

                if (CollUtil.isEmpty(pnrNmTicketList)) {
                    continue;
                }

                for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
                    QueryTicketByPnrVo vo = this.buildTicketByPnrVo(pnrNmTicket, pnrNm, null, pnr, nmOsiList, nmRmkList, nmFnList, pnrFnList);
                    // 设置二次筛选条件
                    QueryTicketByPnrVo.SecondFactor secondFactor = new QueryTicketByPnrVo.SecondFactor();
                    secondFactor.setSecondFactorCode("CN");
                    secondFactor.setSecondFactorValue(pnr.getPnrCrs());
                    vo.setSecondFactor(secondFactor);
                    resultList.add(vo);
                }
            }
        }

        // 处理婴儿旅客
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIdList)
                .list();

        if (CollUtil.isNotEmpty(nmXnList)) {
            List<MnjxPnrNmTn> allPnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                    .list();
            for (MnjxNmXn nmXn : nmXnList) {
                // 查询婴儿的票务信息
                List<MnjxPnrNmTn> pnrNmTnList = allPnrNmTnList.stream()
                        .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(pnrNmTnList)) {
                    continue;
                }

                for (MnjxPnrNmTn pnrNmTn : pnrNmTnList) {
                    List<MnjxPnrNmTicket> pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                            .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                            .list();

                    if (CollUtil.isEmpty(pnrNmTicketList)) {
                        continue;
                    }

                    for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
                        MnjxPnrNm adultPnrNm = iMnjxPnrNmService.getById(nmXn.getPnrNmId());
                        QueryTicketByPnrVo vo = this.buildTicketByPnrVo(pnrNmTicket, adultPnrNm, nmXn, pnr, null, null, null, null);
                        // 设置二次筛选条件
                        QueryTicketByPnrVo.SecondFactor secondFactor = new QueryTicketByPnrVo.SecondFactor();
                        secondFactor.setSecondFactorCode("CN");
                        secondFactor.setSecondFactorValue(pnr.getPnrCrs());
                        vo.setSecondFactor(secondFactor);
                        resultList.add(vo);
                    }
                }
            }
        }

        return resultList;
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByCert(QueryTicketByCertDto dto) throws SguiResultException {
        // 参数验证
        if (!StrUtil.isAllNotEmpty(dto.getCertNo(), dto.getCertCode())) {
            throw new SguiResultException("证件信息不能为空");
        }

        // 根据证件类型不同查询ssr表证件数据
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getSsrType, "PP".equals(dto.getCertCode()) ? "DOCS" : "FOID")
                .like(MnjxNmSsr::getSsrInfo, dto.getCertNo())
                .list();

        if (CollUtil.isEmpty(ssrList)) {
            return Collections.emptyList();
        }
        // 再进行详细的筛选
        if ("PP".equals(dto.getCertCode())) {
            ssrList = ssrList.stream()
                    .filter(s -> s.getSsrInfo().split("/")[2].equals(dto.getCertNo()))
                    .collect(Collectors.toList());
        } else if ("NI".equals(dto.getCertCode())) {
            ssrList = ssrList.stream()
                    .filter(s -> s.getSsrInfo().split("NI")[1].split("/P")[0].equals(dto.getCertNo()))
                    .collect(Collectors.toList());
        } else {
            ssrList = ssrList.stream()
                    .filter(s -> s.getSsrInfo().split("UU")[1].split("/P")[0].equals(dto.getCertNo()))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(ssrList)) {
            return Collections.emptyList();
        }

        List<String> pnrNmIdList = ssrList.stream()
                .map(MnjxNmSsr::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.listByIds(pnrNmIdList);
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIdList)
                .list();
        List<String> queryTnRecordNmIdXnIdList = new ArrayList<>();
        boolean isInf;
        // 如果筛选出来是婴儿的证件，查TN record用婴儿的id
        if (ssrList.stream().anyMatch(s -> "DOCS".equals(s.getSsrType()) && (s.getInputValue().contains("/MI/") || s.getInputValue().contains("/FI/")))) {
            isInf = true;
            queryTnRecordNmIdXnIdList.addAll(nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()));
        } else {
            isInf = false;
            queryTnRecordNmIdXnIdList.addAll(pnrNmIdList);
        }
        List<String> pnrIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrId)
                .collect(Collectors.toList());
        List<MnjxPnr> pnrList = iMnjxPnrService.listByIds(pnrIdList);
        List<MnjxNmOsi> allNmOsiList = iMnjxNmOsiService.lambdaQuery()
                .in(MnjxNmOsi::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmRmk> allNmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, pnrNmIdList)
                .list();
//        List<MnjxPnrNmTn> allNmTnList = iMnjxPnrNmTnService.lambdaQuery()
//                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
//                .list();
        List<MnjxPnrRecord> tnRecordList = iMnjxPnrRecordService.lambdaQuery()
                .in(MnjxPnrRecord::getPnrNmId, queryTnRecordNmIdXnIdList)
                .eq(MnjxPnrRecord::getPnrType, "TN")
                .list();
        List<MnjxNmFn> allNmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnrIdList)
                .list();

        // 构建返回结果
        List<QueryTicketByPnrVo> resultList = new ArrayList<>();
        // 处理旅客
        for (MnjxPnrNm pnrNm : pnrNmList) {
            String pnrNmId = pnrNm.getPnrNmId();
            String pnrId = pnrNm.getPnrId();
            // 查询旅客的票务信息
            List<MnjxPnrRecord> tnRecords = tnRecordList.stream()
                    .filter(t -> pnrNmId.equals(t.getPnrNmId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(tnRecords)) {
                nmXnList.stream()
                        .filter(x -> pnrNmId.equals(x.getPnrNmId()))
                        .forEach(x -> {
                            List<MnjxPnrRecord> xnTnRecords = tnRecordList.stream()
                                    .filter(t -> x.getNmXnId().equals(t.getPnrNmId()))
                                    .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(xnTnRecords)) {
                                tnRecords.addAll(xnTnRecords);
                            }
                        });
                if (CollUtil.isEmpty(tnRecords)) {
                    continue;
                }
            }

            MnjxPnr pnr = pnrList.stream()
                    .filter(p -> pnrId.equals(p.getPnrId()))
                    .collect(Collectors.toList())
                    .get(0);
            List<MnjxNmRmk> nmRmkList = new ArrayList<>();
            List<MnjxNmOsi> nmOsiList = new ArrayList<>();
            if (!isInf) {
                nmRmkList = allNmRmkList.stream()
                        .filter(r -> pnrNmId.equals(r.getPnrNmId()))
                        .collect(Collectors.toList());
                nmOsiList = allNmOsiList.stream()
                        .filter(o -> pnrNmId.equals(o.getPnrNmId()))
                        .collect(Collectors.toList());
            }
            List<MnjxNmFn> nmFnList = allNmFnList.stream()
                    .filter(f -> pnrNmId.equals(f.getPnrNmId()) && isInf ? f.getIsBaby() == 1 : f.getIsBaby() == 0)
                    .collect(Collectors.toList());
            for (MnjxPnrRecord pnrRecord : tnRecords) {
                String ticketNo = pnrRecord.getInputValue().replace("TN/", "").replace("IN/", "").replaceAll("/P\\d+", "").replaceFirst("-", "");
                // TN有多张票 999-1000213091-93，只需要取第一张去查nmTicket，最终都是通过tnId反查nmTicket获取所有票列表
                if (ticketNo.contains("-")) {
                    String[] split = ticketNo.split("-");
                    ticketNo = split[0];
                }
                MnjxPnrNmTicket nmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                        .one();
                List<MnjxPnrNmTicket> pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, nmTicket.getPnrNmTnId())
                        .list();

                // 构建联票信息
                String conjunctiveTicket = "";
                if (pnrNmTicketList.size() > 1) {
                    String firstTicketNo = pnrNmTicketList.get(0).getTicketNo();
                    String lastTicketNo = pnrNmTicketList.get(pnrNmTicketList.size() - 1).getTicketNo();
                    conjunctiveTicket = firstTicketNo.substring(0, 3) + "-" + firstTicketNo.substring(3) + "/" + lastTicketNo.substring(lastTicketNo.length() - 2);
                }

                MnjxNmXn nmXn = null;
                if (isInf) {
                    nmXn = nmXnList.stream()
                            .filter(x -> x.getNmXnId().equals(pnrRecord.getPnrNmId()))
                            .collect(Collectors.toList())
                            .get(0);
                }
                for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
                    QueryTicketByPnrVo vo = this.buildTicketByPnrVo(pnrNmTicket, pnrNm, nmXn, pnr, nmOsiList, nmRmkList, nmFnList, pnrFnList);
                    // 设置二次筛选条件
                    QueryTicketByPnrVo.SecondFactor secondFactor = new QueryTicketByPnrVo.SecondFactor();
                    secondFactor.setSecondFactorCode(dto.getCertCode());
                    secondFactor.setSecondFactorValue(dto.getCertNo());
                    vo.setSecondFactor(secondFactor);
                    vo.setConjunctiveTicket(conjunctiveTicket);
                    resultList.add(vo);
                }
            }
        }
        return resultList;
    }

    /**
     * 构建按PNR查询客票信息VO
     *
     * @param pnrNmTicket 票务信息
     * @param pnrNm       旅客信息
     * @param nmXn        婴儿信息
     * @param pnr         PNR信息
     * @param nmOsiList   旅客OSI信息
     * @param nmRmkList   旅客RMK信息
     * @param nmFnList    旅客FN信息
     * @param pnrFnList   PNR FN信息
     * @return 按PNR查询客票信息VO
     */
    private QueryTicketByPnrVo buildTicketByPnrVo(MnjxPnrNmTicket pnrNmTicket, MnjxPnrNm pnrNm, MnjxNmXn nmXn,
                                                  MnjxPnr pnr, List<MnjxNmOsi> nmOsiList, List<MnjxNmRmk> nmRmkList,
                                                  List<MnjxNmFn> nmFnList, List<MnjxPnrFn> pnrFnList) {
        QueryTicketByPnrVo vo = new QueryTicketByPnrVo();

        // 设置票号
        vo.setEtNumber(pnrNmTicket.getTicketNo().substring(0, 3) + "-" + pnrNmTicket.getTicketNo().substring(3));

        // 设置旅客信息
        if (nmXn != null) {
            // 婴儿票
            vo.setPassengerName(nmXn.getXnCname() + " INF(" + DateUtils.ym2Com(nmXn.getXnBirthday()) + ")(INFANT)");
            vo.setPassengerNameSuffix(nmXn.getXnCname() + " INF(" + DateUtils.ym2Com(nmXn.getXnBirthday()) + ")(INFANT)");
            vo.setPassengerType("INF");
            vo.setFullName(pnrNm.getName() + " INF");
            vo.setSpecialPassengerType("INF");
            vo.setTicketPsgType("INF");
        } else {
            // 成人或儿童票
            MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, "CHLD")
                    .one();
            if (ObjectUtil.isNotEmpty(chldSsr)) {
                // 儿童
                vo.setPassengerName(pnrNm.getName());
                vo.setPassengerNameSuffix(pnrNm.getName() + " (CHILD)");
                vo.setPassengerType("CHD");
                vo.setSpecialPassengerType("CHD");
                vo.setTicketPsgType("CHD");
                vo.setFullName(pnrNm.getName() + " CHD");
            } else {
                // 成人
                vo.setPassengerName(pnrNm.getName());
                vo.setPassengerNameSuffix(pnrNm.getName());
                vo.setPassengerType("ADT");
                vo.setSpecialPassengerType("ADT");
                vo.setTicketPsgType("ADT");
                // 判断GMJC
                if (CollUtil.isNotEmpty(nmRmkList) && nmRmkList.stream().anyMatch(r -> Constant.RMK_TYPE_GMJC.equals(r.getRmkName()))) {
                    if (pnrFnList.stream().anyMatch(p -> p.getIsBaby() == 0)) {
                        MnjxPnrFn pnrFn = pnrFnList.stream()
                                .filter(p -> p.getIsBaby() == 0)
                                .collect(Collectors.toList())
                                .get(0);
                        if ("GM".equals(pnrFn.getPatType())) {
                            vo.setPassengerName(pnrNm.getName() + " GM(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " GM(GMJC)");
                            vo.setSpecialPassengerType("GM");
                            vo.setFullName(pnrNm.getName() + " GM");
                            vo.setTicketPsgType("GM");
                            vo.setPassengerType("GM");
                        } else if ("JC".equals(pnrFn.getPatType())) {
                            vo.setPassengerName(pnrNm.getName() + " JC(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " JC(GMJC)");
                            vo.setSpecialPassengerType("JC");
                            vo.setFullName(pnrNm.getName() + " JC");
                            vo.setTicketPsgType("JC");
                            vo.setPassengerType("JC");
                        }
                    } else if (CollUtil.isNotEmpty(nmFnList)) {
                        if (nmFnList.stream().anyMatch(f -> "GM".equals(f.getPatType()))) {
                            vo.setPassengerName(pnrNm.getName() + " GM(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " GM(GMJC)");
                            vo.setSpecialPassengerType("GM");
                            vo.setFullName(pnrNm.getName() + " GM");
                            vo.setTicketPsgType("GM");
                            vo.setPassengerType("GM");
                        } else if (nmFnList.stream().anyMatch(f -> "JC".equals(f.getPatType()))) {
                            vo.setPassengerName(pnrNm.getName() + " JC(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " JC(GMJC)");
                            vo.setSpecialPassengerType("JC");
                            vo.setFullName(pnrNm.getName() + " JC");
                            vo.setTicketPsgType("JC");
                            vo.setPassengerType("JC");
                        }
                    }
                }
                // 判断VIP
                if (CollUtil.isNotEmpty(nmOsiList) && nmOsiList.stream().anyMatch(o -> Constant.VIP_TYPE.equals(o.getPnrOsiType()))) {
                    vo.setPassengerName(pnrNm.getName() + " VIP");
                    vo.setPassengerNameSuffix(pnrNm.getName() + " VIP");
                    vo.setFullName(pnrNm.getName() + " VIP");
                    vo.setTicketPsgType("VIP");
                    vo.setSpecialPassengerType("VIP");
                }
            }
        }

        vo.setNameSuffix(null);
        vo.setTicketTypeCode("D");
        vo.setCdsTicket(false);
        vo.setEtType("BSP");
        vo.setGovernmentPurchase(false);
        vo.setReceiptPrinted(false);
        vo.setQueryExclusiveNegotiatedFare(false);
        vo.setConjunctiveTicket("");
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, pnrNmTicket.getTicketNo())
                .one();
        String issueInfo = ticketPrice.getIssueInfo();
        String[] split = issueInfo.split(" ");
        String issuedTime = split[2] + " " + split[3];
        if (StrUtil.isNotEmpty(issuedTime)) {
            DateTime dateTime = DateUtils.parse(issuedTime);
            String format = DateUtils.format(dateTime, "yyyy-MM-dd");
            vo.setIssueTicketDate(format);
            vo.setIssueTime(DateUtils.format(dateTime, "HH:mm"));
        }

        List<QueryTicketByPnrVo.AirSeg> airSegList = new ArrayList<>();
        List<QueryTicketDetailVo.AirSegVo> detailAirSegList = this.buildAirSegList(pnrNmTicket, pnr);
        detailAirSegList.forEach(seg -> {
            QueryTicketByPnrVo.AirSeg airSeg = new QueryTicketByPnrVo.AirSeg();
            BeanUtil.copyProperties(seg, airSeg);
            airSegList.add(airSeg);
        });
        vo.setAirSeg(airSegList);

        vo.setAirSegCrsPnr("DEL".equals(pnr.getPnrStatus()) ? null : pnr.getPnrCrs());

        return vo;
    }
}
