package com.swcares.service.et.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrRecord;
import com.swcares.entity.MnjxPnrSeg;
import com.swcares.obj.dto.ModifySegStatusDto;
import com.swcares.obj.vo.ModifySegStatusVo;
import com.swcares.service.IMnjxPnrRecordService;
import com.swcares.service.IMnjxPnrSegService;
import com.swcares.service.IMnjxPnrService;
import com.swcares.service.et.IModifySegStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 修改航段状态服务实现类
 *
 * <AUTHOR>
 * @date 2025/06/03 16:10
 */
@Slf4j
@Service
public class ModifySegStatusServiceImpl implements IModifySegStatusService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ModifySegStatusVo modifySegmentStatus(ModifySegStatusDto dto) throws SguiResultException {
        // 参数校验
        this.validateParameters(dto);

        // 查询PNR信息
        MnjxPnr pnr = this.queryPnrByRecordLocator(dto.getPassengerRecordLocator());

        // 查询航段信息
        MnjxPnrSeg pnrSeg = this.queryPnrSegment(pnr, dto.getSegment());

        // 修改航段状态
        this.updateSegmentStatus(pnrSeg, dto.getSegment().getActionCode());

        // 修改PNR记录
        this.updatePnrRecord(pnr, pnrSeg, dto.getSegment().getActionCode());

        // 构建返回结果
        return this.buildResult(dto);
    }

    /**
     * 参数校验
     */
    private void validateParameters(ModifySegStatusDto dto) throws SguiResultException {
        if (dto == null) {
            throw new SguiResultException("请求参数不能为空");
        }

        if (StrUtil.isEmpty(dto.getPassengerRecordLocator())) {
            throw new SguiResultException("旅客记录定位符不能为空");
        }

        if (dto.getSegment() == null) {
            throw new SguiResultException("航段信息不能为空");
        }

        ModifySegStatusDto.SegmentInfo segment = dto.getSegment();
        if (StrUtil.isEmpty(segment.getAirline())) {
            throw new SguiResultException("航司代码不能为空");
        }

        if (StrUtil.isEmpty(segment.getFlightNumber())) {
            throw new SguiResultException("航班号不能为空");
        }

        if (StrUtil.isEmpty(segment.getDepartureDate())) {
            throw new SguiResultException("起飞日期不能为空");
        }

        if (StrUtil.isEmpty(segment.getOrigin())) {
            throw new SguiResultException("出发地不能为空");
        }

        if (StrUtil.isEmpty(segment.getDestination())) {
            throw new SguiResultException("目的地不能为空");
        }

        if (StrUtil.isEmpty(segment.getActionCode())) {
            throw new SguiResultException("行动代码不能为空");
        }

        if (ObjectUtil.isEmpty(segment.getElementNumber())) {
            throw new SguiResultException("元素编号不能为空");
        }
    }

    /**
     * 根据记录定位符查询PNR
     */
    private MnjxPnr queryPnrByRecordLocator(String passengerRecordLocator) throws SguiResultException {
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, passengerRecordLocator)
                .one();

        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        return pnr;
    }

    /**
     * 查询航段信息
     */
    private MnjxPnrSeg queryPnrSegment(MnjxPnr pnr, ModifySegStatusDto.SegmentInfo segmentInfo) throws SguiResultException {
        // 构建完整航班号
        String fullFlightNumber = segmentInfo.getAirline() + segmentInfo.getFlightNumber();

        String flightDate = segmentInfo.getDepartureDate();

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrSeg::getFlightNo, fullFlightNumber)
                .eq(MnjxPnrSeg::getFlightDate, flightDate)
                .eq(MnjxPnrSeg::getOrg, segmentInfo.getOrigin())
                .eq(MnjxPnrSeg::getDst, segmentInfo.getDestination())
                .eq(MnjxPnrSeg::getPnrIndex, segmentInfo.getElementNumber())
                .list();

        if (CollUtil.isEmpty(pnrSegList)) {
            throw new SguiResultException("未找到匹配的航段信息");
        }

        if (pnrSegList.size() > 1) {
            throw new SguiResultException("找到多个匹配的航段信息");
        }

        return pnrSegList.get(0);
    }

    /**
     * 修改航段状态
     */
    private void updateSegmentStatus(MnjxPnrSeg pnrSeg, String newActionCode) {
        // 保存原始行动代码
        String oldActionCode = pnrSeg.getActionCode();

        // 修改行动代码
        pnrSeg.setActionCode(newActionCode);

        // 修改inputValue中的行动代码
        if (StrUtil.isNotEmpty(pnrSeg.getInputValue()) && StrUtil.isNotEmpty(oldActionCode)) {
            String newInputValue = pnrSeg.getInputValue().replace(oldActionCode, newActionCode);
            pnrSeg.setInputValue(newInputValue);
        }

        // 更新数据库
        iMnjxPnrSegService.updateById(pnrSeg);
    }

    /**
     * 修改PNR记录
     */
    private void updatePnrRecord(MnjxPnr pnr, MnjxPnrSeg pnrSeg, String newActionCode) {
        // 查询对应的PNR记录
        List<MnjxPnrRecord> pnrRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrRecord::getPnrIndex, pnrSeg.getPnrIndex())
                .eq(MnjxPnrRecord::getPnrType, "SEG")
                .isNull(MnjxPnrRecord::getChangeMark)
                .list();

        if (CollUtil.isNotEmpty(pnrRecords)) {
            for (MnjxPnrRecord record : pnrRecords) {
                if (StrUtil.isNotEmpty(record.getInputValue())) {
                    // 将inputValue中的HK替换为新的行动代码
                    String newInputValue = record.getInputValue().replace("HK", newActionCode);
                    record.setInputValue(newInputValue);
                }
            }

            // 批量更新
            iMnjxPnrRecordService.updateBatchById(pnrRecords);
        }
    }

    /**
     * 构建返回结果
     */
    private ModifySegStatusVo buildResult(ModifySegStatusDto dto) {
        ModifySegStatusVo vo = new ModifySegStatusVo();
        vo.setOrderId(dto.getOrderId());
        vo.setPassengerRecordLocator(dto.getPassengerRecordLocator());
        vo.setSuccess(true);
        return vo;
    }
}
