package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * PNR历史查询响应VO
 *
 * <AUTHOR>
 * @date 2025/5/12 17:18
 */
@Data
@ApiModel(value = "PNR历史查询响应VO")
public class PnrHistoryVo {

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "历史行内容")
    private List<LineContent> historyLineContents;

    /**
     * 行内容
     */
    @Data
    public static class LineContent {
        @ApiModelProperty(value = "索引")
        private String index;

        @ApiModelProperty(value = "内容")
        private String content;

        @ApiModelProperty(value = "是否可删除")
        private Boolean canDelete;

        @ApiModelProperty(value = "代码")
        private String code;

        @ApiModelProperty(value = "是否无效航段")
        private Boolean invalidSeg;
    }
}
