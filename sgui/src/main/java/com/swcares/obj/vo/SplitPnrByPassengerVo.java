package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 旅客分离响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Data
@ApiModel(value = "SplitPnrByPassengerVo", description = "旅客分离响应VO")
public class SplitPnrByPassengerVo {

    @ApiModelProperty(value = "原订单信息")
    private OrderInfo order;

    @ApiModelProperty(value = "分离后的订单信息")
    private OrderInfo splitedOrder;

    @Data
    @ApiModel(value = "OrderInfo", description = "订单信息")
    public static class OrderInfo {
        @ApiModelProperty(value = "订单ID")
        private String orderId;

        @ApiModelProperty(value = "PNR编号")
        private String passengerRecordLocator;
    }
}
