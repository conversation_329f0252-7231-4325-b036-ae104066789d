package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 生成PNR请求DTO
 *
 * <AUTHOR>
 * @date 2025/5/19 14:00
 */
@Data
@ApiModel(value = "BookPnrDto", description = "生成PNR请求DTO")
public class BookPnrDto {

    @ApiModelProperty(value = "航段信息")
    private List<Flight> flights;

    @ApiModelProperty(value = "旅客信息")
    private List<Passenger> passengers;

    @ApiModelProperty(value = "联系信息")
    private ContactInfo contactInfo;

    @ApiModelProperty(value = "备注信息")
    private List<Remark> remarks;

    @ApiModelProperty(value = "特殊服务")
    private List<SpecialService> specialServices;

    @ApiModelProperty(value = "是否需要写入价格信息")
    private Boolean notNeedWritePrice;

    @ApiModelProperty(value = "价格信息")
    private List<Price> price;

    @ApiModelProperty(value = "出票时限")
    private TimeLimit timeLimit;

    @ApiModelProperty(value = "是否国际航班")
    private Boolean international;

    @ApiModelProperty(value = "占位状态")
    private Boolean occupyStatus;

    @ApiModelProperty(value = "是否存在外国航空公司")
    private Boolean existForeignAirline;

    @ApiModelProperty(value = "信封类型")
    private String envelopType;

    @ApiModelProperty(value = "运价备注")
    private List<FareRemark> fareRemarks;

    /**
     * 航段信息
     */
    @Data
    public static class Flight {
        @ApiModelProperty(value = "航段列表")
        private List<Segment> segments;
    }

    /**
     * 航段详情
     */
    @Data
    public static class Segment {
        @ApiModelProperty(value = "操作代码")
        private String actionCode;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "到达日期")
        private String arrivalDate;

        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;

        @ApiModelProperty(value = "出发航站楼")
        private String departureTerminal;

        @ApiModelProperty(value = "到达航站楼")
        private String arrivalTerminal;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "营销航班号")
        private String marketingFlightNumber;

        @ApiModelProperty(value = "共享信息")
        private String sharedInfo;

        @ApiModelProperty(value = "营销飞机类型")
        private String marketingPlaneType;

        @ApiModelProperty(value = "营销航空公司")
        private String marketingAirline;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "航段类型")
        private String segmentType;
    }

    /**
     * 旅客信息
     */
    @Data
    public static class Passenger {
        @ApiModelProperty(value = "婴儿关联")
        private String infantRef;

        @ApiModelProperty(value = "旅客ID")
        private String id;

        @ApiModelProperty(value = "性别")
        private String sex;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "证件类型")
        private String certificateType;

        @ApiModelProperty(value = "证件号码")
        private String certificateNo;

        @ApiModelProperty(value = "证件签发国家")
        private String certificateIssueCountry;

        @ApiModelProperty(value = "证件持有人国际代码")
        private String certificateHolderInternational;

        @ApiModelProperty(value = "证件有效期")
        private String certificateExpiryDate;

        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "全名")
        private String fullName;

        @ApiModelProperty(value = "婴儿中文名")
        private String infantChineseName;

        @ApiModelProperty(value = "姓名类型")
        private String nameType;

        @ApiModelProperty(value = "联系方式和通信列表")
        private List<ContactAndCommunication> contactAndCommunicationList;

        @ApiModelProperty(value = "邮箱")
        private String email;

        @ApiModelProperty(value = "VIP类型")
        private String vipType;

        @ApiModelProperty(value = "VIP文本列表")
        private List<String> vipTextList;

        @ApiModelProperty(value = "身份文本")
        private String identityText;

        @ApiModelProperty(value = "补充身份信息列表")
        private List<SupplementaryIdentityInfo> supplementaryIdentityInfoList;

        @ApiModelProperty(value = "姓名后缀")
        private String nameSuffix;

        @ApiModelProperty(value = "护照文档")
        private String ppForDocs;

        @ApiModelProperty(value = "文档名称")
        private String docsName;

        @ApiModelProperty(value = "地址信息")
        private AddressInfo addressInfo;

        @ApiModelProperty(value = "身份证文档")
        private String niForDocs;

        @ApiModelProperty(value = "常旅客信息")
        private List<Frequenter> frequenters;

        @ApiModelProperty(value = "持有人")
        private String holder;

        @ApiModelProperty(value = "家庭ID")
        private Integer familyId;

        @ApiModelProperty(value = "婴儿选择的航段")
        private List<Segment> inftSelectedSegments;
    }

    /**
     * 联系方式和通信
     */
    @Data
    public static class ContactAndCommunication {
        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "文本")
        private String text;
    }

    /**
     * 地址信息
     */
    @Data
    public static class AddressInfo {
        @ApiModelProperty(value = "居住地址")
        private Address live;

        @ApiModelProperty(value = "到达地址")
        private Address arrival;
    }

    /**
     * 地址
     */
    @Data
    public static class Address {
        @ApiModelProperty(value = "国家代码")
        private String countryCode;

        @ApiModelProperty(value = "省份代码")
        private String provinceCode;

        @ApiModelProperty(value = "城市英文名")
        private String cityNameEn;

        @ApiModelProperty(value = "邮编")
        private String postcode;

        @ApiModelProperty(value = "详细地址")
        private String detailAddress;
    }

    /**
     * 常旅客信息
     */
    @Data
    public static class Frequenter {
        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "到达日期")
        private String arrivalDate;

        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;

        @ApiModelProperty(value = "出发航站楼")
        private String departureTerminal;

        @ApiModelProperty(value = "到达航站楼")
        private String arrivalTerminal;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "营销航班号")
        private String marketingFlightNumber;

        @ApiModelProperty(value = "共享信息")
        private String sharedInfo;

        @ApiModelProperty(value = "营销飞机类型")
        private String marketingPlaneType;

        @ApiModelProperty(value = "营销航空公司")
        private String marketingAirline;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "航段类型")
        private String segmentType;

        @ApiModelProperty(value = "常旅客号码 CA122211")
        private String frequentNumber;
    }

    /**
     * 联系信息
     */
    @Data
    public static class ContactInfo {
        @ApiModelProperty(value = "联系方式列表")
        private List<String> ctList;

        @ApiModelProperty(value = "邮箱")
        private String email;

        @ApiModelProperty(value = "电话信息列表")
        private List<PhoneInfo> phoneInfoList;
    }

    /**
     * 电话信息
     */
    @Data
    public static class PhoneInfo {
        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "电话号码")
        private String phoneNumber;
    }

    /**
     * 备注
     */
    @Data
    public static class Remark {
        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "航空公司代码")
        private String airlineCode;
    }

    /**
     * 特殊服务
     */
    @Data
    public static class SpecialService {
        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "旅客ID列表")
        private List<String> passengerIds;

        @ApiModelProperty(value = "特殊服务航段")
        private List<SpecialServiceSegment> specialServiceSegments;

        @ApiModelProperty(value = "特殊服务数量")
        private Integer specialServiceNumber;
    }

    /**
     * 特殊服务航段
     */
    @Data
    public static class SpecialServiceSegment {
        @ApiModelProperty(value = "操作代码")
        private String actionCode;

        @ApiModelProperty(value = "营销航空公司")
        private String marketingAirline;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "营销航班号")
        private String marketingFlightNumber;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;
    }

    /**
     * 价格
     */
    @Data
    public static class Price {
        @ApiModelProperty(value = "价格ID")
        private String priceId;

        @ApiModelProperty(value = "协议运价代码")
        private String negotiatedFareCode;

        @ApiModelProperty(value = "查询独家协议运价")
        private Boolean queryExclusiveNegotiatedFare;

        @ApiModelProperty(value = "基本运价")
        private String fareBasic;

        @ApiModelProperty(value = "运价类型")
        private String fareType;

        @ApiModelProperty(value = "定价来源")
        private String pricingSource;

        @ApiModelProperty(value = "货币代码")
        private String currencyCode;

        @ApiModelProperty(value = "出票航空公司")
        private String issuingAirline;

        @ApiModelProperty(value = "机场代码")
        private String airportCode;

        @ApiModelProperty(value = "计算最低价格")
        private Boolean calculateLowestPrice;

        @ApiModelProperty(value = "计算所有品牌")
        private Boolean calculateAllBrand;

        @ApiModelProperty(value = "支付方式")
        private String payMethod;

        @ApiModelProperty(value = "价格项")
        private List<PriceItem> priceItems;

        @ApiModelProperty(value = "团队旅客类型")
        private String groupPassengerType;

        @ApiModelProperty(value = "折扣代码")
        private String discountCode;
    }

    /**
     * 价格项
     */
    @Data
    public static class PriceItem {
        @ApiModelProperty(value = "价格项ID")
        private String priceItemId;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "序列号")
        private String sequenceNmbr;

        @ApiModelProperty(value = "票面价")
        private String ticketAmount;

        @ApiModelProperty(value = "总价")
        private String totalAmount;

        @ApiModelProperty(value = "货币")
        private String currency;

        @ApiModelProperty(value = "燃油费")
        private String fuel;

        @ApiModelProperty(value = "基建费")
        private String fund;

        @ApiModelProperty(value = "FN值")
        private String fn;

        @ApiModelProperty(value = "FC值")
        private String fc;

        @ApiModelProperty(value = "佣金率")
        private Integer commissionRate;

        @ApiModelProperty(value = "航段信息")
        private List<SegmentInfo> segmentInfos;

        @ApiModelProperty(value = "行李信息")
        private List<BaggageWithSegIndex> baggageWithSegIndex;

        @ApiModelProperty(value = "旅客数量")
        private Integer passengerNum;

        @ApiModelProperty(value = "旅客类型更新")
        private String passengerTypeUpdate;

        @ApiModelProperty(value = "旅客年龄")
        private Integer passengerAge;

        @ApiModelProperty(value = "旅客ID列表")
        private List<String> passengerIds;

        @ApiModelProperty(value = "儿童使用成人价格")
        private Boolean chdUsingAdtPrice;
    }

    /**
     * 航段信息
     */
    @Data
    public static class SegmentInfo {
        @ApiModelProperty(value = "运价基础代码")
        private String fareBasisCodes;

        @ApiModelProperty(value = "航空公司")
        private String airline;
    }

    /**
     * 行李信息
     */
    @Data
    public static class BaggageWithSegIndex {
        @ApiModelProperty(value = "航段索引")
        private Integer segIndex;

        @ApiModelProperty(value = "行李信息")
        private String baggageInfo;
    }

    /**
     * 出票时限
     */
    @Data
    public static class TimeLimit {
        @ApiModelProperty(value = "出票时限")
        private String timeLimit;

        @ApiModelProperty(value = "办公室")
        private String office;

        @ApiModelProperty(value = "票号")
        private String ticketNumber;
    }

    /**
     * 出票时限
     */
    @Data
    public static class FareRemark {
        @ApiModelProperty(value = "旅客序号列表")
        private List<String> passengerIds;

        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "类型")
        private String type;
    }

    /**
     *
     */
    @Data
    public static class SupplementaryIdentityInfo {
        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "类型")
        private String type;
    }
}
