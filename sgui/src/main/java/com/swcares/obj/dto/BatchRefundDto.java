package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量退票请求DTO
 *
 * <AUTHOR>
 * @date 2025/06/26 16:00
 */
@Data
@ApiModel(value = "批量退票请求DTO")
public class BatchRefundDto {

    @ApiModelProperty(value = "票号列表")
    private List<TicketInfo> ticketList;

    /**
     * 票号信息
     */
    @Data
    @ApiModel(value = "票号信息")
    public static class TicketInfo {
        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "国内标识")
        private Boolean domestic;

        @ApiModelProperty(value = "打印号")
        private String printNo;
    }
}
