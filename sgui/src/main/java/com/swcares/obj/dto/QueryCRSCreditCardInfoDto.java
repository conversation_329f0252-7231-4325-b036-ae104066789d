package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * QueryCRSCreditCardInfoDto
 *
 * <AUTHOR>
 * @date 2025/06/27 13:50
 */
@Data
@ApiModel(value = "QueryCRSCreditCardInfoDto")
public class QueryCRSCreditCardInfoDto {

    @ApiModelProperty(value = "航司结算码")
    private String airlineNumber;

    @ApiModelProperty(value = "开始票号")
    private String beginNumber;

    @ApiModelProperty(value = "打印号")
    private String deviceNumber;

    @ApiModelProperty(value = "结束票号")
    private String endNumber;
}
