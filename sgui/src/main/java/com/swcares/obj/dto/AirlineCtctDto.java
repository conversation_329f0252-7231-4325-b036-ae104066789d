package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 航司CTCT设置数据传输对象
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AirlineCtctDto", description = "航司CTCT设置数据传输对象")
public class AirlineCtctDto {

    @ApiModelProperty(value = "航司代码")
    private String airlineCode;

    @ApiModelProperty(value = "CTCT值")
    private String ctctValue;
}
