import{bU as c,bV as s,ao as i}from"./index-9381ab2b.js";const d=t=>c.get(`${s}/dict/getDictTypeTree`,{headers:{gid:t}}),r=(t,e)=>c.post(`${s}/dict/queryDictTypeList`,t,{headers:{gid:e}}),o=(t,e)=>c.post(`${s}/dict/queryDictEntryList`,t,{headers:{gid:e}}),p=(t,e)=>c.post(`${s}/dict/deleteDictType`,{dictTypeIds:t},{headers:{gid:e}}),y=(t,e)=>c.post(`${s}/dict/deleteDictEntry`,{dictEntryIds:t},{headers:{gid:e}}),n=(t,e)=>c.post(`${s}/dict/saveDictType`,t,{headers:{gid:e}}),D=(t,e)=>c.post(`${s}/dict/updateDictType`,t,{headers:{gid:e}}),T=(t,e)=>c.post(`${s}/dict/saveDictEntry`,t,{headers:{gid:e}}),h=(t,e)=>c.post(`${s}/dict/updateDictEntry`,t,{headers:{gid:e}}),E=(t,e)=>i(`${s}/dict/queryDictEntryList`,{headers:{gid:e}}).post(t).json();export{T as a,h as b,o as c,p as d,y as e,d as f,r as g,E as q,n as s,D as u};
