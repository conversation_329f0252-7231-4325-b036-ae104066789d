import{q as x,ab as y,r as B,aw as c,a9 as D,o as E,ay as k,av as C,x as s,B as l,G as t,z as a,ai as N,aj as M,y as V,ak as d,Q as r,A as _,az as b,P as Q,ag as S,aA as q,aB as z,H as A}from"./index-9381ab2b.js";import{E as j,a as F,b as G}from"./index-d6fb0de3.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";const P={class:"languages"},T={class:"el-dropdown-link"},J=x({__name:"I18n",props:{loginMode:{default:"agent"}},emits:["getQrCode"],setup(p,{emit:i}){const m=p,u=i,{locale:g}=y({useScope:"global"}),n=B(c()),f=D(),w=async e=>{q(e),m.loginMode==="qrCode"&&u("getQrCode",0),n.value=c(e),g.value=e,f.dispatch("setLocaleLang",e),await z("diLocalData")};return E(()=>{k(C())}),(e,K)=>{const v=A,L=j,I=F,h=G;return s(),l("div",P,[t(h,{onCommand:w},{dropdown:a(()=>[t(I,null,{default:a(()=>[(s(!0),l(N,null,M(_(b),o=>(s(),V(L,{key:o.value,class:"i18n-item-li",command:o.value,"data-test":o.name,"data-gid":"081V0101"},{default:a(()=>[d(r(o.name),1)]),_:2},1032,["command","data-test"]))),128))]),_:1})]),default:a(()=>[Q("span",T,[d(r(n.value)+" ",1),t(v,{class:"el-icon--right"},{default:a(()=>[t(_(S))]),_:1})])]),_:1})])}}});const W=H(J,[["__scopeId","data-v-9e0ad533"]]);export{W as I};
