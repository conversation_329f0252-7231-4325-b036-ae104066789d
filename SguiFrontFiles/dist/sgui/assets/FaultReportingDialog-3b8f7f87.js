import{L as we,M as ge,e8 as ke,ea as ce,q as ne,b6 as be,hp as Ne,hq as Oe,N as De,v as Le,hr as Ae,r as S,hs as Me,s as Ie,w as T,ac as ve,aG as Ce,o as Fe,x as L,y as ee,G as u,z as h,P as a,D,A as e,C as he,E as Pe,J as N,H as W,a1 as je,B as Y,ai as se,ht as Ye,hu as qe,hv as Be,hw as Ue,b9 as He,hx as Ge,hy as Xe,aj as Ee,a5 as $e,a6 as Ve,F as pe,T as Je,a7 as Ke,_ as Se,V as ae,u as le,gK as Qe,K as Te,eF as We,eE as Ze,eb as oe,Q as E,X as et,hz as tt,dP as at,gN as lt,p as ot,bU as ye,bV as re,ao as ze,ab as st,a9 as nt,aa as rt,aH as it,aY as fe,au as Re,cN as _e,R as xe,b5 as ut,ak as ie,ah as pt,al as ct,am as dt,an as ft}from"./index-9381ab2b.js";import{m as ue}from"./Tip-483abcda.js";import{E as mt}from"./index-1d08351c.js";import{E as gt,a as vt}from"./index-951011fc.js";import{t as me}from"./throttle-9e041729.js";import{i as ht}from"./position-c8cb347a.js";import{E as yt}from"./index-8850371a.js";import{E as bt}from"./index-7b8ec8cc.js";import{_ as Rt}from"./_plugin-vue_export-helper-c27b6911.js";const _t=we({urlList:{type:ge(Array),default:()=>ke([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7}}),xt={close:()=>!0,switch:s=>ce(s),rotate:s=>ce(s)},wt=["src"],kt=ne({name:"ElImageViewer"}),Dt=ne({...kt,props:_t,emits:xt,setup(s,{expose:y,emit:n}){const b=s,x={CONTAIN:{name:"contain",icon:be(Ne)},ORIGINAL:{name:"original",icon:be(Oe)}},{t:O}=De(),c=Le("image-viewer"),{nextZIndex:H}=Ae(),I=S(),o=S([]),A=Me(),g=S(!0),C=S(b.initialIndex),$=Ie(x.CONTAIN),R=S({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),X=T(()=>{const{urlList:t}=b;return t.length<=1}),q=T(()=>C.value===0),J=T(()=>C.value===b.urlList.length-1),Q=T(()=>b.urlList[C.value]),Z=T(()=>[c.e("btn"),c.e("prev"),c.is("disabled",!b.infinite&&q.value)]),G=T(()=>[c.e("btn"),c.e("next"),c.is("disabled",!b.infinite&&J.value)]),f=T(()=>{const{scale:t,deg:i,offsetX:_,offsetY:z,enableTransition:F}=R.value;let V=_/t,j=z/t;switch(i%360){case 90:case-270:[V,j]=[j,-V];break;case 180:case-180:[V,j]=[-V,-j];break;case 270:case-90:[V,j]=[-j,V];break}const te={transform:`scale(${t}) rotate(${i}deg) translate(${V}px, ${j}px)`,transition:F?"transform .3s":""};return $.value.name===x.CONTAIN.name&&(te.maxWidth=te.maxHeight="100%"),te}),p=T(()=>ce(b.zIndex)?b.zIndex:H());function B(){U(),n("close")}function M(){const t=me(_=>{switch(_.code){case ae.esc:b.closeOnPressEscape&&B();break;case ae.space:w();break;case ae.left:P();break;case ae.up:l("zoomIn");break;case ae.right:K();break;case ae.down:l("zoomOut");break}}),i=me(_=>{const z=_.deltaY||_.deltaX;l(z<0?"zoomIn":"zoomOut",{zoomRate:b.zoomRate,enableTransition:!1})});A.run(()=>{le(document,"keydown",t),le(document,"wheel",i)})}function U(){A.stop()}function r(){g.value=!1}function m(t){g.value=!1,t.target.alt=O("el.image.error")}function d(t){if(g.value||t.button!==0||!I.value)return;R.value.enableTransition=!1;const{offsetX:i,offsetY:_}=R.value,z=t.pageX,F=t.pageY,V=me(te=>{R.value={...R.value,offsetX:i+te.pageX-z,offsetY:_+te.pageY-F}}),j=le(document,"mousemove",V);le(document,"mouseup",()=>{j()}),t.preventDefault()}function v(){R.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function w(){if(g.value)return;const t=Qe(x),i=Object.values(x),_=$.value.name,F=(i.findIndex(V=>V.name===_)+1)%t.length;$.value=x[t[F]],v()}function k(t){const i=b.urlList.length;C.value=(t+i)%i}function P(){q.value&&!b.infinite||k(C.value-1)}function K(){J.value&&!b.infinite||k(C.value+1)}function l(t,i={}){if(g.value)return;const{minScale:_,maxScale:z}=b,{zoomRate:F,rotateDeg:V,enableTransition:j}={zoomRate:b.zoomRate,rotateDeg:90,enableTransition:!0,...i};switch(t){case"zoomOut":R.value.scale>_&&(R.value.scale=Number.parseFloat((R.value.scale/F).toFixed(3)));break;case"zoomIn":R.value.scale<z&&(R.value.scale=Number.parseFloat((R.value.scale*F).toFixed(3)));break;case"clockwise":R.value.deg+=V,n("rotate",R.value.deg);break;case"anticlockwise":R.value.deg-=V,n("rotate",R.value.deg);break}R.value.enableTransition=j}return ve(Q,()=>{Ce(()=>{const t=o.value[0];t!=null&&t.complete||(g.value=!0)})}),ve(C,t=>{v(),n("switch",t)}),Fe(()=>{var t,i;M(),(i=(t=I.value)==null?void 0:t.focus)==null||i.call(t)}),y({setActiveItem:k}),(t,i)=>(L(),ee(Ke,{to:"body",disabled:!t.teleported},[u(Je,{name:"viewer-fade",appear:""},{default:h(()=>[a("div",{ref_key:"wrapper",ref:I,tabindex:-1,class:D(e(c).e("wrapper")),style:he({zIndex:e(p)})},[a("div",{class:D(e(c).e("mask")),onClick:i[0]||(i[0]=Pe(_=>t.hideOnClickModal&&B(),["self"]))},null,2),N(" CLOSE "),a("span",{class:D([e(c).e("btn"),e(c).e("close")]),onClick:B},[u(e(W),null,{default:h(()=>[u(e(je))]),_:1})],2),N(" ARROW "),e(X)?N("v-if",!0):(L(),Y(se,{key:0},[a("span",{class:D(e(Z)),onClick:P},[u(e(W),null,{default:h(()=>[u(e(Ye))]),_:1})],2),a("span",{class:D(e(G)),onClick:K},[u(e(W),null,{default:h(()=>[u(e(qe))]),_:1})],2)],64)),N(" ACTIONS "),a("div",{class:D([e(c).e("btn"),e(c).e("actions")])},[a("div",{class:D(e(c).e("actions__inner"))},[u(e(W),{onClick:i[1]||(i[1]=_=>l("zoomOut"))},{default:h(()=>[u(e(Be))]),_:1}),u(e(W),{onClick:i[2]||(i[2]=_=>l("zoomIn"))},{default:h(()=>[u(e(Ue))]),_:1}),a("i",{class:D(e(c).e("actions__divider"))},null,2),u(e(W),{onClick:w},{default:h(()=>[(L(),ee(He(e($).icon)))]),_:1}),a("i",{class:D(e(c).e("actions__divider"))},null,2),u(e(W),{onClick:i[3]||(i[3]=_=>l("anticlockwise"))},{default:h(()=>[u(e(Ge))]),_:1}),u(e(W),{onClick:i[4]||(i[4]=_=>l("clockwise"))},{default:h(()=>[u(e(Xe))]),_:1})],2)],2),N(" CANVAS "),a("div",{class:D(e(c).e("canvas"))},[(L(!0),Y(se,null,Ee(t.urlList,(_,z)=>$e((L(),Y("img",{ref_for:!0,ref:F=>o.value[z]=F,key:_,src:_,style:he(e(f)),class:D(e(c).e("img")),onLoad:r,onError:m,onMousedown:d},null,46,wt)),[[Ve,z===C.value]])),128))],2),pe(t.$slots,"default")],6)]),_:3})],8,["disabled"]))}});var Lt=Se(Dt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image-viewer/src/image-viewer.vue"]]);const It=Te(Lt),Ct=we({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:ge([String,Object])},previewSrcList:{type:ge(Array),default:()=>ke([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7}}),Ft={load:s=>s instanceof Event,error:s=>s instanceof Event,switch:s=>ce(s),close:()=>!0,show:()=>!0},Et=["src","loading"],$t={key:0},Vt=ne({name:"ElImage",inheritAttrs:!1}),St=ne({...Vt,props:Ct,emits:Ft,setup(s,{emit:y}){const n=s;let b="";const{t:x}=De(),O=Le("image"),c=We(),H=Ze(),I=S(),o=S(!1),A=S(!0),g=S(!1),C=S(),$=S(),R=oe&&"loading"in HTMLImageElement.prototype;let X,q;const J=T(()=>[O.e("inner"),G.value&&O.e("preview"),A.value&&O.is("loading")]),Q=T(()=>c.style),Z=T(()=>{const{fit:l}=n;return oe&&l?{objectFit:l}:{}}),G=T(()=>{const{previewSrcList:l}=n;return Array.isArray(l)&&l.length>0}),f=T(()=>{const{previewSrcList:l,initialIndex:t}=n;let i=t;return t>l.length-1&&(i=0),i}),p=T(()=>n.loading==="eager"?!1:!R&&n.loading==="lazy"||n.lazy),B=()=>{oe&&(A.value=!0,o.value=!1,I.value=n.src)};function M(l){A.value=!1,o.value=!1,y("load",l)}function U(l){A.value=!1,o.value=!0,y("error",l)}function r(){ht(C.value,$.value)&&(B(),v())}const m=ot(r,200,!0);async function d(){var l;if(!oe)return;await Ce();const{scrollContainer:t}=n;tt(t)?$.value=t:at(t)&&t!==""?$.value=(l=document.querySelector(t))!=null?l:void 0:C.value&&($.value=lt(C.value)),$.value&&(X=le($,"scroll",m),setTimeout(()=>r(),100))}function v(){!oe||!$.value||!m||(X==null||X(),$.value=void 0)}function w(l){if(l.ctrlKey){if(l.deltaY<0)return l.preventDefault(),!1;if(l.deltaY>0)return l.preventDefault(),!1}}function k(){G.value&&(q=le("wheel",w,{passive:!1}),b=document.body.style.overflow,document.body.style.overflow="hidden",g.value=!0,y("show"))}function P(){q==null||q(),document.body.style.overflow=b,g.value=!1,y("close")}function K(l){y("switch",l)}return ve(()=>n.src,()=>{p.value?(A.value=!0,o.value=!1,v(),d()):B()}),Fe(()=>{p.value?d():B()}),(l,t)=>(L(),Y("div",{ref_key:"container",ref:C,class:D([e(O).b(),l.$attrs.class]),style:he(e(Q))},[o.value?pe(l.$slots,"error",{key:0},()=>[a("div",{class:D(e(O).e("error"))},E(e(x)("el.image.error")),3)]):(L(),Y(se,{key:1},[I.value!==void 0?(L(),Y("img",et({key:0},e(H),{src:I.value,loading:l.loading,style:e(Z),class:e(J),onClick:k,onLoad:M,onError:U}),null,16,Et)):N("v-if",!0),A.value?(L(),Y("div",{key:1,class:D(e(O).e("wrapper"))},[pe(l.$slots,"placeholder",{},()=>[a("div",{class:D(e(O).e("placeholder"))},null,2)])],2)):N("v-if",!0)],64)),e(G)?(L(),Y(se,{key:2},[g.value?(L(),ee(e(It),{key:0,"z-index":l.zIndex,"initial-index":e(f),infinite:l.infinite,"zoom-rate":l.zoomRate,"min-scale":l.minScale,"max-scale":l.maxScale,"url-list":l.previewSrcList,"hide-on-click-modal":l.hideOnClickModal,teleported:l.previewTeleported,"close-on-press-escape":l.closeOnPressEscape,onClose:P,onSwitch:K},{default:h(()=>[l.$slots.viewer?(L(),Y("div",$t,[pe(l.$slots,"viewer")])):N("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):N("v-if",!0)],64)):N("v-if",!0)],6))}});var Tt=Se(St,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image/src/image.vue"]]);const zt=Te(Tt),Nt=(s,y)=>ye.post(`${re}/tosp/faultreport/upload`,s,{headers:{"Content-Type":"multipart/form-data",gid:y}}),Ot=(s,y)=>ye.post(`${re}/tosp/faultreport/newFault`,s,{headers:{gid:y}}),Fa=(s,y)=>ze(`${re}/tosp/faultreport/queryFaultReport`,{headers:{gid:y}}).post(s).json(),Ea=(s,y)=>ze(`${re}/tosp/faultreport/updatestatus`,{headers:{gid:y}}).post(s).json(),$a=(s,y)=>ye.post(`${re}/tosp/faultreport/feedback`,s,{headers:{gid:y}}),At=(s,y)=>{var U;const{t:n}=st(),b=nt(),x=S(),O=S(),c=S(""),H=S(!1),I=rt("bugTipsObject",""),o=T(()=>b.state.user),A=()=>o.value.securityLevel===1?o.value.mobile:o.value.securityLevel===4?o.value.email:o.value.mobile,g=S({reportTelphone:((U=o.value)==null?void 0:U.mobile)??"",remark:"",imageListVo:[],faultTime:"",exception:"",description:"",transactionCode:"",reportReason:"",gid:""}),C=S({reportTelphone:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],remark:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:250,message:n("app.faultReport.faultReportingDialog.maximum250Characters"),trigger:"blur"}],imageListVo:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],faultTime:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],exception:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:n("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],description:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:n("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],transactionCode:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:n("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],reportReason:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}]}),$=Ie({render(){return it("em",{class:"iconfont icon-calendar"})}}),R=r=>{var k,P;let m=!1,d=!1;const v=["PNG","JPG","JPEG","BMP","IMAGE/JPEG","IMAGE/BMP"],w=((P=(k=r.raw)==null?void 0:k.type)==null?void 0:P.toUpperCase())??"";return v.forEach(K=>{w.includes(K)&&(m=!0)}),r!=null&&r.size&&r.size<=10*1024*1024&&r.size>0&&(d=!0),m&&d},X=(r,m)=>{g.value.imageListVo=m,R(r)?x.value.validateField("imageListVo"):(fe.error(n("app.faultReport.faultReportingDialog.uploadFailed")),q(r.uid))},q=r=>{var d,v;const m=(d=g.value.imageListVo)==null?void 0:d.findIndex(w=>w.uid===r);(v=g.value.imageListVo)==null||v.splice(m,1),x.value.validateField("imageListVo")},J=r=>r.filter(d=>d.success).map(d=>({imageName:d.fileName,imageNewName:d.fileUrl,imageSize:d.fileSize})),Q=()=>{x.value.validate(async r=>{if(!r)return;const m=new FormData;g.value.imageListVo.forEach(w=>{m.append("files",w.raw)});const d=Re("081L0132"),v=await Nt(m,d);if(v.code==="200")if(v.data.every(k=>k.success===!1))await _e("",n("app.faultReport.faultReportingDialog.ImageUploadFailed"),"",v.time);else{const k={...g.value,imageListVo:J(v.data),faultTimeCn:xe().format("YYYY-MM-DD HH:mm:ss"),macAddress:c.value??""},P=Re("081L0124");await Ot(k,P),x.value.resetFields(),x.value.faultTime="",x.value.imageListVo=[],x.value.remark="",x.value.transactionCode="",x.value.exception="",x.value.description="",y("updateFormFaultReportingForm",{...g.value,faultTime:""}),I.value="",y("update:modelValue",!1),y("handleQuery"),fe.success(n("app.faultReport.faultReportingDialog.reportedSuccessfully"))}else await _e("",n("app.faultReport.faultReportingDialog.ImageUploadFailed"),"",v.time)})},Z=r=>{var w;let m=!1;const d=["PNG","JPG","JPEG","BMP","IMAGE/JPEG","IMAGE/BMP"],v=((w=r.type)==null?void 0:w.toUpperCase())??"";return d.forEach(k=>{v.includes(k)&&(m=!0)}),m},G=r=>{if(!H.value)return;const d=r.clipboardData.items,v=d==null?void 0:d[0],w=v==null?void 0:v.getAsFile();if(w&&v.kind==="file"&&Z(v)&&g.value.imageListVo.length<3&&w.size<10*1024*1024){const k={raw:w,url:URL.createObjectURL(w)};g.value.imageListVo.push(k),x.value.validateField("imageListVo")}else fe.error(n("app.faultReport.faultReportingDialog.uploadFailed"))},f=()=>{H.value=!1,I.value="",y("update:modelValue",!1),y("updateFormFaultReportingForm",g.value)},p=async()=>{var r;if(c.value=(r=await M())==null?void 0:r.toUpperCase(),s.faultReportingDialogForm){const m={...s.faultReportingDialogForm,faultTime:s.faultReportingDialogForm.faultTime?s.faultReportingDialogForm.faultTime:xe().format("YYYY-MM-DD HH:mm:ss")};g.value=m}b.dispatch("setFullLoading",!1)},B=()=>{H.value=!0},M=async()=>{var d;const r=(d=navigator==null?void 0:navigator.userAgent)==null?void 0:d.toLowerCase();let m="";return r!=null&&r.includes("electron/")&&(m=await window.electronAPI.getAuthInfo()),m&&ut.decode(m)};return{faultReportingRef:x,isClickFaultReportingDom:H,uploadRef:O,FORM_RULES:C,userInfo:o,datePrefix:$,faultReportingForm:g,bugTipsObjectSession:I,macAddress:c,uploadChange:X,handDelImag:q,handleSubmit:Q,handlePaste:G,handleClose:f,handleFaultReporting:B,handleOpen:p,contactInformation:A}},Mt=At,de=s=>(dt("data-v-9f9cc65e"),s=s(),ft(),s),Pt={class:"justify-start text-gray-1 text-lg font-bold leading-normal"},jt={class:"w-full self-stretch px-2.5 py-1 bg-gray-8 rounded inline-flex flex-col justify-center items-start"},Yt={class:"self-stretch inline-flex justify-start items-center gap-2.5"},qt={class:"justify-center"},Bt={class:"text-gray-4 text-xs font-normal leading-tight"},Ut={class:"text-gray-1 text-xs font-normal leading-tight"},Ht={class:"justify-center"},Gt=de(()=>a("span",{class:"text-gray-4 text-xs font-normal leading-tight"},"OFFICE：",-1)),Xt={class:"text-gray-1 text-xs font-normal leading-tight"},Jt={class:"justify-center flex-wrap flex items-center"},Kt={class:"text-gray-4 text-xs font-normal leading-tight"},Qt={class:"text-gray-1 text-xs font-normal leading-tight truncate w-[78px]"},Wt={class:"justify-center flex-wrap flex items-center"},Zt={class:"text-gray-4 text-xs font-normal leading-tight"},ea={class:"w-[120px] text-gray-1 text-xs font-normal leading-tight max-w-[120px] inline-block align-middle truncate"},ta={class:"self-stretch inline-flex justify-start items-center gap-3.5"},aa={class:"justify-center flex-wrap flex items-center"},la={class:"text-gray-4 text-xs font-normal leading-tight"},oa={class:"text-gray-1 text-xs font-normal leading-tight w-[100px] inline-block align-middle truncate"},sa={class:"justify-center flex-wrap flex items-center"},na={class:"text-gray-4 text-xs font-normal leading-tight"},ra={class:"text-gray-1 text-xs font-normal leading-tight w-[370px] inline-block align-middle truncate"},ia={class:"form-inline"},ua={class:"upload-input-box"},pa={key:0,class:"self-stretch leading-4 justify-start text-gray-5 text-xs font-normal mb-[6px]"},ca=de(()=>a("br",null,null,-1)),da={class:"flex justify-start items-start flex-wrap"},fa=["onClick"],ma=de(()=>a("i",{class:"iconfont icon-delete text-brand-2"},null,-1)),ga=[ma],va=de(()=>a("div",null,[a("div",{class:"w-5 h-5 border-2 border-gray-6 border-solid rounded-sm text-gray-5 flex justify-center items-center"},[a("span",null,"+")])],-1)),ha={class:"w-64 ml-[68px] mb-[10px] justify-start text-red-1 text-xs font-normal leading-tight"},ya={class:"dialog-footer"},ba=ne({__name:"FaultReportingDialog",props:{faultReportingDialogForm:{}},emits:["update: modelValue","updateFormFaultReportingForm","handleQuery"],setup(s,{emit:y}){const n=s,b=y,{faultReportingRef:x,uploadRef:O,isClickFaultReportingDom:c,FORM_RULES:H,userInfo:I,faultReportingForm:o,datePrefix:A,bugTipsObjectSession:g,macAddress:C,uploadChange:$,handDelImag:R,handleSubmit:X,handlePaste:q,handleClose:J,handleFaultReporting:Q,handleOpen:Z,contactInformation:G}=Mt(n,b);return(f,p)=>{const B=mt,M=gt,U=pt,r=zt,m=yt,d=vt,v=ct,w=bt;return L(),ee(w,{width:"680px","custom-class":"fault-reporting-dialog","close-on-click-modal":!1,tabindex:"1",onClose:e(J),onOpen:e(Z),onPaste:e(q)},{header:h(()=>[a("div",Pt,E(f.$t("app.faultReport.faultReportingDialog.faultReporting")),1)]),footer:h(()=>[a("span",ya,[u(v,{type:"primary",class:"w-[80px]",onClick:e(X)},{default:h(()=>[ie(E(f.$t("app.faultReport.faultReportingDialog.submit")),1)]),_:1},8,["onClick"]),u(v,{class:"w-[80px]",onClick:e(J)},{default:h(()=>[ie(E(f.$t("app.personal.cancelConfigure")),1)]),_:1},8,["onClick"])])]),default:h(()=>{var k,P,K,l;return[a("div",null,[a("div",jt,[a("div",Yt,[a("div",qt,[a("span",Bt,E(f.$t("app.faultReport.faultReportingDialog.jobID"))+"：",1),a("span",Ut,E(((k=e(I))==null?void 0:k.agent)||"-"),1)]),a("div",Ht,[Gt,a("span",Xt,E(((P=e(I))==null?void 0:P.defaultOffice)||"-"),1)]),a("div",Jt,[a("span",Kt,E(f.$t("app.faultReport.faultReportingDialog.workContact"))+"：",1),a("div",Qt,[u(ue,{"show-val":e(G)()},{default:h(()=>[a("span",null,E(e(G)()||"-"),1)]),_:1},8,["show-val"])])]),a("div",Wt,[a("span",Zt,E(f.$t("app.faultReport.faultReportingDialog.MACAddress"))+"：",1),a("div",ea,[u(ue,{"show-val":e(C)},{default:h(()=>[a("span",null,E(e(C)||"-"),1)]),_:1},8,["show-val"])])])]),a("div",ta,[a("div",aa,[a("span",la,E(f.$t("app.faultReport.faultReportingDialog.nameReporter"))+"：",1),a("div",oa,[u(ue,{"show-val":(K=e(I))==null?void 0:K.employeeName},{default:h(()=>{var t;return[a("span",null,E(((t=e(I))==null?void 0:t.employeeName)||"-"),1)]}),_:1},8,["show-val"])])]),a("div",sa,[a("span",na,E(f.$t("app.faultReport.faultReportingDialog.reportingUnit"))+"：",1),a("div",ra,[u(ue,{"show-val":(l=e(I))==null?void 0:l.departmentName},{default:h(()=>{var t;return[a("span",null,E(((t=e(I))==null?void 0:t.departmentName)||"-"),1)]}),_:1},8,["show-val"])])])])]),u(d,{ref_key:"faultReportingRef",ref:x,model:e(o),"label-width":"70px","require-asterisk-position":"right",rules:e(H),inline:"",class:"fault-reporting-form crs-new-ui-init-cls"},{default:h(()=>[a("div",ia,[e(g)?N("",!0):(L(),ee(M,{key:0,label:f.$t("app.faultReport.faultReportingDialog.mtbf"),class:"fault-time",prop:"faultTime"},{default:h(()=>[u(B,{modelValue:e(o).faultTime,"onUpdate:modelValue":p[0]||(p[0]=t=>e(o).faultTime=t),type:"datetime","prefix-icon":e(A),clearable:!1,placeholder:f.$t("app.faultReport.faultReportingDialog.enterDate"),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onFocus:p[1]||(p[1]=t=>c.value=!1)},null,8,["modelValue","prefix-icon","placeholder"])]),_:1},8,["label"])),u(M,{label:f.$t("app.faultReport.faultReportingDialog.contactInformation"),prop:"reportTelphone",class:D([e(g)?"report-telphone":""])},{default:h(()=>[u(U,{modelValue:e(o).reportTelphone,"onUpdate:modelValue":p[2]||(p[2]=t=>e(o).reportTelphone=t),placeholder:f.$t("app.faultReport.faultReportingDialog.pleaseContact"),onFocus:p[3]||(p[3]=t=>c.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label","class"]),a("div",ua,[u(M,{label:f.$t("app.faultReport.faultReportingDialog.faultScreenshot"),class:"textarea-input upload-input h-[238px]",prop:"imageListVo"},{default:h(()=>{var t,i,_,z;return[a("div",{class:D(["image-box w-full self-stretch pl-[6px] pt-[6px] pb-0 bg-gray-0 rounded-sm outline outline-1 outline-offset-[-1px] outline-gray-6 gap-1.5 overflow-hidden",(t=e(o).imageListVo)!=null&&t.length?"":"pr-[6px]"]),onClick:p[5]||(p[5]=(...F)=>e(Q)&&e(Q)(...F))},[(i=e(o).imageListVo)!=null&&i.length?N("",!0):(L(),Y("div",pa,[ie(E(f.$t("app.faultReport.faultReportingDialog.faultScreenshottipOne")),1),ca,ie(E(f.$t("app.faultReport.faultReportingDialog.faultScreenshottipTwo")),1)])),a("div",da,[(L(!0),Y(se,null,Ee(e(o).imageListVo,(F,V)=>(L(),Y("div",{key:V,class:D(["border border-gray-6  border-solid mb-[6px] mr-[6px] rounded-sm w-[116px] h-[100px] bg-gray-0 relative image-item",V!==e(o).imageListVo.length-1?"mr-[6px]":""])},[u(r,{class:"w-full h-full",src:F.url,"zoom-rate":1.2,"max-scale":7,"min-scale":.2,"preview-src-list":[F.url],"initial-index":4,fit:"cover"},null,8,["src","preview-src-list"]),a("div",{class:"absolute bottom-0 bg-gray-8 w-full h-[20px] flex justify-center items-center cursor-pointer del-icon",onClick:j=>e(R)(F.uid)},ga,8,fa)],2))),128)),$e(u(m,{ref_key:"uploadRef",ref:O,"file-list":e(o).imageListVo,"onUpdate:fileList":p[4]||(p[4]=F=>e(o).imageListVo=F),class:D(["add-upload-btn"]),"auto-upload":!1,limit:5,"list-type":"picture-card","show-file-list":!1,"on-change":e($)},{trigger:h(()=>[va]),_:1},8,["file-list","on-change"]),[[Ve,((z=(_=e(o))==null?void 0:_.imageListVo)==null?void 0:z.length)<3]])])],2)]}),_:1},8,["label"]),a("div",ha,E(f.$t("app.faultReport.faultReportingDialog.screenshotsOnce")),1)]),a("div",null,[u(M,{label:f.$t("app.faultReport.faultReportingDialog.faultPhenomenon"),class:"textarea-input",prop:"remark"},{default:h(()=>[u(U,{modelValue:e(o).remark,"onUpdate:modelValue":p[6]||(p[6]=t=>e(o).remark=t),type:"textarea",placeholder:f.$t("app.faultReport.faultReportingDialog.faultPhenomenonTip"),onFocus:p[7]||(p[7]=t=>c.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),e(g)?N("",!0):(L(),ee(M,{key:1,label:f.$t("app.faultReport.faultReportingDialog.errorCode"),prop:"exception"},{default:h(()=>[u(U,{modelValue:e(o).exception,"onUpdate:modelValue":p[8]||(p[8]=t=>e(o).exception=t),placeholder:`${f.$t("app.faultReport.faultReportingDialog.example")}: OM-01-33R09`,onFocus:p[9]||(p[9]=t=>c.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])),e(g)?N("",!0):(L(),ee(M,{key:2,label:f.$t("app.faultReport.faultReportingDialog.transactionNumber"),prop:"transactionCode"},{default:h(()=>[u(U,{modelValue:e(o).transactionCode,"onUpdate:modelValue":p[10]||(p[10]=t=>e(o).transactionCode=t),placeholder:`${f.$t("app.faultReport.faultReportingDialog.example")}: XXXSAT41862025041319073900733488`,onFocus:p[11]||(p[11]=t=>c.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"]))])]),_:1},8,["model","rules"])])]}),_:1},8,["onClose","onOpen","onPaste"])}}});const Va=Rt(ba,[["__scopeId","data-v-9f9cc65e"]]);export{zt as E,Va as F,Fa as q,$a as s,Ea as u};
