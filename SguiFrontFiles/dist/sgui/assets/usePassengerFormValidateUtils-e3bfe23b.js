import{R as l,bO as o,c5 as c,fR as _,hf as R,hg as v,hh as x,bj as b,f_ as w,eJ as A,hi as O}from"./index-9381ab2b.js";import{a as S,h as M,q as C,r as L,t as V,v as Y,s as q,n as y}from"./pnrUtils-8a2ea82c.js";import{V as B,u as N}from"./config-b573cde3.js";import{N as T,I as E,T as D,ae as U,ad as H}from"./regular-crs-0d781ceb.js";const d=(i,t)=>{if(i){if(t==="ADT")return C;if(t==="CHD")return L}return t==="ADT"?V:t==="CHD"?Y:C},$=(i,t)=>{!(t!=null&&t.idCardNumber)&&!(t!=null&&t.documentType)&&(i==="D"?t.documentType="NI_I":t.documentType="PP_P")},ut=i=>({fullName:"",nameSuffix:"",document:{documentType:"",ssrType:"",idCardNumber:"",visaIssueCountry:"CN",passengerNationality:"CN",visaExpiryDate:"",birthday:"",gender:"",docsName:"",holder:""},docaInfoR:{country:"",state:"",city:"",address:"",zip:""},docaInfoD:{country:"",state:"",city:"",address:"",zip:""},osiCtcm:"",ssrCtcm:"",VIPCheck:!1,vipText:"",FOIDCheck:!1,passengerIndex:i+1,foidNICheck:!1,gmjcInfo:{rmk:"",ssrCkinDfmm:!1,ssrCkinDfpp:!1},FQTVText:"",FQTVCheck:!1,segments:[],niForDocs:"",ppForDocs:""}),z=(i,t)=>{const n=(t==null?void 0:t.airlineType)??"D",m=n!=="D";t.airlineType===n?i.document.documentType=t.document.documentType:i.document.documentType=d(m,t.passengerType)[0]},G=(i,t)=>{i.pasgType=t.passengerType},Q=(i,t)=>{i.fullName=(t==null?void 0:t.fullName)??"",i.nameSuffix=(t==null?void 0:t.nameSuffix)??""},J=i=>{var t,n;return(t=i==null?void 0:i.document)!=null&&t.documentType?q(((n=i==null?void 0:i.document)==null?void 0:n.documentType)??""):d(((i==null?void 0:i.airlineType)??"D")!=="D",i.passengerType)[0].value},X=(i,t,n)=>{var m,p,u,a,f,I,e,F,h;i.document={documentType:n||J(t),ssrType:((m=t==null?void 0:t.document)==null?void 0:m.ssrType)??"",idCardNumber:((p=t==null?void 0:t.document)==null?void 0:p.idCardNumber)??"",visaIssueCountry:((u=t==null?void 0:t.document)==null?void 0:u.visaIssueCountry)??"",passengerNationality:((a=t==null?void 0:t.document)==null?void 0:a.passengerNationality)??"",visaExpiryDate:((f=t==null?void 0:t.document)==null?void 0:f.visaExpiryDate)??"",birthday:((I=t==null?void 0:t.document)==null?void 0:I.birthday)??"",gender:((e=t==null?void 0:t.document)==null?void 0:e.gender)??"",holder:((F=t==null?void 0:t.document)==null?void 0:F.holder)??"",docsName:((h=t==null?void 0:t.document)==null?void 0:h.docsName)??""},$((t==null?void 0:t.airlineType)??"D",i.document)},Z=(i,t)=>{i.niForDocs=(t==null?void 0:t.niForDocs)??"",i.ppForDocs=(t==null?void 0:t.ppForDocs)??"",i.FOIDCheck=!!(t!=null&&t.ppForDocs),i.foidNICheck=!!(t!=null&&t.niForDocs)},K=(i,t)=>{t&&t.birthday&&(i.document.birthday=t.birthday)},W=(i,t)=>{var n,m,p,u,a;i.docaInfoR={country:((n=t==null?void 0:t.docaInfoR)==null?void 0:n.country)??"",state:((m=t==null?void 0:t.docaInfoR)==null?void 0:m.state)??"",city:((p=t==null?void 0:t.docaInfoR)==null?void 0:p.city)??"",address:((u=t==null?void 0:t.docaInfoR)==null?void 0:u.address)??"",zip:((a=t==null?void 0:t.docaInfoR)==null?void 0:a.zip)??""}},k=(i,t)=>{var n,m,p,u,a;i.docaInfoD={country:((n=t==null?void 0:t.docaInfoD)==null?void 0:n.country)??"",state:((m=t==null?void 0:t.docaInfoD)==null?void 0:m.state)??"",city:((p=t==null?void 0:t.docaInfoD)==null?void 0:p.city)??"",address:((u=t==null?void 0:t.docaInfoD)==null?void 0:u.address)??"",zip:((a=t==null?void 0:t.docaInfoD)==null?void 0:a.zip)??""}},lt=i=>{const t=i.replace(/，/g,",").split(/[,，]/);return[...new Set(t)].join(",")},j=(i,t)=>{var n;i.osiCtcm=(t==null?void 0:t.osiCtcm)??"",i.ssrCtcm=(t==null?void 0:t.ssrCtcm)??"",i.vipText=((n=t==null?void 0:t.vipTexts)==null?void 0:n.join("、"))??"",i.VIPCheck=!!(t!=null&&t.vipType),i.passengerEmail=((t==null?void 0:t.passengerEmail)??"").replace("//","@")??""},g=(i,t)=>{var m,p;i.gmjcInfo.rmk=(t==null?void 0:t.identityText)??"";const n=((p=(m=(t==null?void 0:t.supplementaryIdentityInfoList)??[])==null?void 0:m[0])==null?void 0:p.text)??"";i.gmjcInfo.ssrCkinDfmm=n==="DFMM",i.gmjcInfo.ssrCkinDfpp=n==="DFPP"},P=(i,t)=>{i.FQTVCheck=!!(t!=null&&t.frequentNumber),i.FQTVText=t==null?void 0:t.frequentNumber,i.segments=((t==null?void 0:t.segments)??[]).filter(n=>!n.openInd&&!n.arnkInd)},at=(i,t)=>{z(i,t),G(i,t),Q(i,t),X(i,t),Z(i,t),K(i,t),W(i,t),k(i,t),j(i,t),g(i,t),P(i,t)},ft=async(i,t)=>{const n=await S(t),m=(t??[]).filter(u=>!u.disabled&&u.segments[0].departureDate);if(!n||!i||!m.length)return!1;const p=l(m[0].segments[0].departureDate).add(6,"month");return l(i).isBefore(p)},ot=i=>{const t=l(l().format("YYYY-MM-DD"));return l(i).isBefore(t)},Nt=()=>(M()??[]).filter(i=>!i.openInd&&!i.arnkInd).map(i=>({...i,frequentNumber:""})),yt=i=>o(i==null?void 0:i.address)&&o(i==null?void 0:i.country)&&o(i==null?void 0:i.state)&&o(i==null?void 0:i.city)&&o(i==null?void 0:i.zip),Tt=i=>{const t=l(l().format("YYYY-MM-DD"));return l(i).isAfter(t)},It=i=>{const t=new Date,n=new Date(i);if(isNaN(n.getTime()))return Number.MAX_VALUE;let m=t.getFullYear()-n.getFullYear();const p=t.getMonth()-n.getMonth();return(p<0||p===0&&t.getDate()<n.getDate())&&m--,m},Et=(i,t,n)=>{(E.test(t)?/^[\p{Script=Han}\s?a-zA-Z0-9()·]*$/u:T).test(t)?n():n(new Error(`${c.global.t("app.intlPassengerForm.validate.englishOrChineseOrNumberNameError")}，${c.global.t("app.intlPassengerForm.validate.allEnglishNameError")}`))},s=(i,t)=>{const n={tip:"",min:1,max:0};if(i){const m=B.find(p=>t.split("_")[1]===p.type);m&&(n.min=m.min,n.max=m.max,new RegExp(`^[0-9A-Za-z(~!@#$%^&*]{${m.min},${m.max}}$`).test(i)||((i==null?void 0:i.length)<m.min||(i==null?void 0:i.length)>m.max?n.tip=m.errorTip:n.tip="app.intlPassengerForm.validate.certError"))}return n},r=i=>{let t="";return(i==null?void 0:i.length)===18&&(t=i.substring(6,14)),t},tt=(i,t)=>{if(!A.test(t.document.idCardNumber))return t.document.documentType==="NI_I"?"errBirthDate":"errNIDate";const n=r(t.document.idCardNumber);return i===y.CHD&&t.document.birthday!==""&&t.document.birthday&&n!==l(t.document.birthday).format("YYYYMMDD")?"niBirthDateDiff":""},it=(i,t)=>{if(t.document.documentType.includes("NI"))return c.global.t(N.get(tt(i,t))??"");if(t.document.documentType.includes("UU")){if(A.test(t.document.idCardNumber))return c.global.t(N.get("documentTypeTip")??"");if(!O.test(t.document.idCardNumber))return c.global.t(N.get("uuNumberTip")??"")}const n=s(t.document.idCardNumber,t.document.documentType);return n.tip?n.min>1?c.global.t(n.tip,{min:n.min,max:n.max}):c.global.t(n.tip,{max:n.max}):""},Dt=(i,t,n)=>{const m=i.form.value;if(m.document.documentType.includes("NI")&&t&&!_(t)){n(c.global.t(N.get(m.document.documentType==="NI_I"?"errBirthDate":"errHKCard")??""));return}const p=m.document.idCardNumber?it(i.pasgType,m):"";if(p.length>0){n(p);return}n()},et=(i,t,n)=>{const m=i.form.value,p=i.formRef;if(m.document.idCardNumber&&t===""&&m.document.documentType!=="PP_A"){n(new Error(c.global.t("app.intlPassengerForm.validate.required")));return}m.document.documentType.includes("NI")&&m.document.idCardNumber!==""&&p.value.validateField("passengeIdNo"),n()},Ft=(i,t,n)=>{if(i.form.value.VIPCheck&&t&&!R.test(t)){n(new Error(c.global.t("app.intlPassengerForm.validate.enterCorrectJob")));return}n()},ht=(i,t,n)=>{if(!i.form.value.foidNICheck){n();return}if(t&&!_(t)){n(c.global.t(N.get("errBirthDate")??""));return}n()},Ct=(i,t,n)=>{i.isDocsName&&((E.test(t)?D:T).test(t)||n(new Error(`${c.global.t("app.intlPassengerForm.validate.englishOrChineseOrNumberNameError")}，${c.global.t("app.intlPassengerForm.validate.allEnglishNameError")}`)),n()),T.test(t)?n():n(new Error(c.global.t("app.intlPassengerForm.validate.allEnglishNameError")))},_t=(i,t,n)=>{(i.infSuffix?U:E.test(t)?D:T).test(t)||n(new Error(c.global.t("app.intlPassengerForm.validate.cnFormatError"))),n()},At=(i,t,n)=>{i.fullNameEditDisable&&n(),i.isDocsName&&((E.test(t)?D:T).test(t)||n(new Error(`${c.global.t("app.intlPassengerForm.validate.englishOrChineseOrNumberNameError")}，${c.global.t("app.intlPassengerForm.validate.allEnglishNameError")}`)),n()),v.test(t)?n():n(new Error(c.global.t("app.intlPassengerForm.validate.allEnglishNameError")))},dt=(i,t,n)=>{const m=i.form.value,p=i.isInter,u=i.pasgType;!t&&u===y.INF&&!p&&!i.isTeamSchedule&&i.formRef.value.clearValidate(["document.visaIssueCountry","document.passengerNationality","document.visaExpiryDate"]);const a=["PP_P","PP_IP","PP_I","PP_A","PP_F","PP_IN"];u===y.INF&&a.includes(m.document.documentType)&&(t&&!H.test(t)&&n(new Error(c.global.t("app.intlPassengerForm.validate.certError"))),n()),i.isSpecialCardTypeF.value=m.document.documentType==="PP_F"&&x.test(t)&&!p;const f=s(t,m.document.documentType);if(f.tip){const I=f.min>1?c.global.t(f.tip,{min:f.min,max:f.max}):c.global.t(f.tip,{max:f.max});n(I)}n()},st=(i,t,n)=>{i.form.document.idCardNumber&&!t&&(i.form.document.documentType!=="PP_A"||i.pasgFormAdt)?n(new Error(c.global.t("app.intlPassengerForm.required"))):n()},Rt=(i,t,n)=>{(i.idNoType==="PP_P"||i.idNoType==="PP_IP")&&(t==null?void 0:t.length)>15?n(new Error(c.global.t("app.intlPassengerForm.validate.niNumberTip"))):n()},vt=(i,t,n)=>{i.isInter&&t?i.formRef.value.validateField(["issueAt","international","expiryDate"]):i.formRef.value.clearValidate(["issueAt","international","expiryDate"]),n()},xt=(i,t,n)=>{const m=i.form.value.document.documentType==="PP_A"&&i.pasgType===y.ADT&&i.visaExpiryDate;i.isSpecialCardTypeF&&n(),i.isInter?i.form.value.document.idCardNumber&&!t&&!m?n(new Error(c.global.t("app.intlPassengerForm.validate.required"))):n():i.pasgType===y.INF&&!i.isInter?i.form.value.document.idCardNumber&&!t?n(new Error(c.global.t("app.intlPassengerForm.validate.required"))):n():t?n():i.form.value.document.idCardNumber&&!t&&!m?n(new Error(c.global.t("app.intlPassengerForm.validate.required"))):n()},bt=(i,t,n)=>{if(t&&!(i.documentTypeList??[]).find(p=>p.value===t)){n(new Error(c.global.t("app.intlPassengerForm.validate.docTypeError")));return}n()},wt=(i,t,n)=>{if(i.form.value.FOIDCheck){if(t.trim()===""){n(new Error(c.global.t("app.intlPassengerForm.validate.required")));return}if(!b.test(t)){n(new Error(c.global.t("app.intlPassengerForm.validate.enterFOIDPPtips")));return}}n()},Ot=(i,t,n)=>{if(t&&!w.test(t)){n(new Error(c.global.t("app.intlPassengerForm.validate.allEnglishNameError")));return}n()},St=(i,t,n)=>{if(!t){n(new Error(c.global.t("app.intlPassengerForm.selectDateBirth")));return}if(i.currentData&&l(i.currentData).diff(l(t),"year")>=2&&t){n(new Error(c.global.t("app.intlPassengerForm.validate.babyBirthdayError")));return}n()};export{X as A,Z as B,K as C,W as D,k as E,j as F,g as G,P as H,s as I,ut as a,at as b,At as c,St as d,dt as e,vt as f,Rt as g,xt as h,Nt as i,ft as j,ot as k,It as l,yt as m,Et as n,bt as o,Dt as p,et as q,Ft as r,Ct as s,st as t,lt as u,_t as v,Ot as w,wt as x,ht as y,Tt as z};
