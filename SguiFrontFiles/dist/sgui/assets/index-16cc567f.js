import{L as m,dR as y,q as u,v as h,w as v,x as a,B as i,y as r,z as b,b9 as C,A as o,H as E,J as t,D as c,F as d,_,K as B}from"./index-9381ab2b.js";const g=m({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},icon:{type:y}}),w={click:l=>l instanceof MouseEvent},L=["href"],$=u({name:"ElLink"}),P=u({...$,props:g,emits:w,setup(l,{emit:p}){const s=l,n=h("link"),f=v(()=>[n.b(),n.m(s.type),n.is("disabled",s.disabled),n.is("underline",s.underline&&!s.disabled)]);function k(e){s.disabled||p("click",e)}return(e,z)=>(a(),i("a",{class:c(o(f)),href:e.disabled||!e.href?void 0:e.href,onClick:k},[e.icon?(a(),r(o(E),{key:0},{default:b(()=>[(a(),r(C(e.icon)))]),_:1})):t("v-if",!0),e.$slots.default?(a(),i("span",{key:1,class:c(o(n).e("inner"))},[d(e.$slots,"default")],2)):t("v-if",!0),e.$slots.icon?d(e.$slots,"icon",{key:2}):t("v-if",!0)],10,L))}});var S=_(P,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/link/src/link.vue"]]);const I=B(S);export{I as E};
