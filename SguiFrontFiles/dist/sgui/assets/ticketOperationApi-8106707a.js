import{ao as s,ap as r,iU as n}from"./index-9381ab2b.js";const i=(e,t)=>s(`${r}/crs/ticket/queryByPnr`,{headers:{gid:t}}).post({pnrNo:e}).json(),c=(e,t,a)=>s(`${r}/crs/ticket?ticketNo=${e}${a?`&type=${a}`:""}`,{headers:{gid:t}}).get().json(),u=(e,t)=>s(`${r}/crs/ticket/queryTicketDetail`,{headers:{gid:t}}).post(e).json(),p=(e,t)=>s(`${r}/crs/ticket/queryTicketDetail`,{headers:{gid:t}},{ignoreError:!0}).post(e).json(),d=(e,t)=>s(`${r}/crs/ticket/queryTicketDigestsByCert`,{headers:{gid:t}}).post(e).json(),k=(e,t)=>s(`${r}/crs/ticket/getTicketDigestsByName`,{headers:{gid:t}}).post(e).json(),y=(e,t)=>s(`${r}/crs/ticket/invalid`,{headers:{gid:t}}).post(e).json(),h=(e,t)=>s(`${r}/crs/passenger/queryAllPassengersByTktNumber`,{headers:{gid:t}}).post(e).json(),A=(e,t,a)=>s(`${r}/crs/office/queryOfficeInformation`,{headers:{gid:t}},{ignoreError:a??!1}).post(e).json(),g=(e,t)=>s(`${r}/crs/etTicket/getTol`,{headers:{gid:t}}).post(e).json(),l=(e,t)=>s(`${r}/crs/ticket/queryTicketByDetr`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).post(e).json(),T=(e,t)=>s(`${r}/crs/ticket/queryTicketByRtkt/${e}`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).get().json(),f=(e,t)=>s(`${r}/crs/ticket/queryTicketByRtkt/${e}`,{headers:{gid:t}}).get().json(),$=(e,t)=>s(`${r}/crs/ticket/tssChangeTicketStatus`,{headers:{gid:t}}).post(e).json(),j=(e,t)=>s(`${r}/crs/ticket/tssChangeTicketStatusByPnr`,{headers:{gid:t}}).post(e).json(),R=(e,t)=>s(`${r}/crs/ticket/etrfChangeTicketStatus`,{headers:{gid:t}}).post(e).json(),q=(e,t)=>s(`${r}/crs/bopRefund/dptr`,{headers:{gid:t}}).post(e).json(),B=(e,t)=>s(`${r}/crs/cccf/queryCRSCreditCardInfo`,{headers:{gid:t}}).post(e).json(),m=(e,t)=>s(`${r}/crs/ticket/findRefundTicketAndFee`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),C=(e,t)=>s(`${r}/apiRefundTicket/batchRefund`,{headers:{gid:t}}).post(e).json(),b=(e,t)=>s(`${r}/apiRefundTicket/batchAutoRefund`,{headers:{gid:t}}).post(e).json(),Q=(e,t)=>s(`${r}/apiRefundTicket/batchQueryTrfdZ`,{headers:{gid:t}}).post(e).json(),V=(e,t)=>s(`${r}/apiRefundTicket/deleteRefundForm`,{headers:{gid:t}}).post(e).json(),F=(e,t)=>s(`${r}/crs/ticket/queryRefundForm`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),P=(e,t)=>s(`${r}/apiRefundTicket/modifyRefundForm`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),E=(e,t)=>s(`${r}/apiRefundTicket/ticket/refund/supplementary`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),S=(e,t)=>s(`${r}/crs/ticketAuth/airTicketDisplayAuth`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),D=(e,t)=>s(`${r}/crs/ticketAuth/airTicketRemoveAuth`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),N=(e,t)=>s(`${r}/crs/ticketAuth/airTicketAddAuth`,{headers:{gid:t}},{originalValue:!0}).post(e).json(),v=(e,t)=>s(`${r}/crs/ticket/pullControl`,{headers:{gid:t}}).post(e).json(),I=(e,t)=>s(`${n}/crs/passenger/queryAllPassengersByPnrNo`,{headers:{gid:t}}).post(e).json(),_=(e,t)=>s(`${r}/apiRefundTicket/batchFindRefundFeeZ`,{headers:{gid:t}}).post(e).json(),M=(e,t)=>s(`${r}/crs/passenger/queryTicketByInvalid`,{headers:{gid:t}}).post(e).json(),O=(e,t)=>s(`${r}/crs/etTicket/getCRSStockTQTD`,{headers:{gid:t}}).post(e).json(),z=(e,t)=>s(`${r}/crs/ticketControl/getTicketPooll`,{headers:{gid:t}}).post(e).json(),U=(e,t)=>s(`${r}/crs/ticket/queryTicketManagementOrganization/${e}`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).get().json(),Z=(e,t)=>s(`${r}/crs/ticket/queryRtktByEtermMode/${e}`,{headers:{gid:t}},{ignoreError:!0,originalValue:!0}).get().json();export{B as A,l as B,Z as C,p as D,c as E,V as F,P as G,A as H,z as I,g as J,O as K,u as a,i as b,d as c,k as d,b as e,f,Q as g,h,M as i,T as j,S as k,D as l,N as m,$ as n,m as o,R as p,F as q,y as r,j as s,v as t,U as u,E as v,I as w,C as x,_ as y,q as z};
