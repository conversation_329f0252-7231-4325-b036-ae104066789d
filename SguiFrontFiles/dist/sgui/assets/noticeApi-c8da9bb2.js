import{q as P,w as I,R as H,x as t,B as n,P as k,Q as p,G as f,z as q,A as o,et as J,ak as j,cc as Q,H as z,r as x,ac as A,eu as U,J as $,ai as _,aj as m,y as F,ao as V,bV as Y}from"./index-9381ab2b.js";import{_ as G}from"./_plugin-vue_export-helper-c27b6911.js";const K={class:"topic-info"},W={class:"topic"},X={class:"author-info"},Z=P({__name:"noticeTitle",props:{title:{},time:{}},setup(c){const a=c,d=I(()=>a.time?H(a.time).format("YYYY-MM-DD hh:mm:ss"):"");return(u,C)=>{const l=z;return t(),n("div",K,[k("div",W,p(u.title??""),1),k("div",X,[k("span",null,[f(l,null,{default:q(()=>[f(o(J))]),_:1}),j(p(u.$t("app.systemNotice.publisher")),1)]),k("span",null,[f(l,null,{default:q(()=>[f(o(Q))]),_:1}),j(p(u.$t("app.systemNotice.date"))+" ",1),k("span",null,p(d.value??""),1)])])])}}});const D=G(Z,[["__scopeId","data-v-6ad65fd9"]]),tt=(c,a)=>{const d=x({}),u=x(c.currentIndex??-1),C=x(),l=x(!0),L=x(!1),h=x(!1),y=I(()=>{var i;const s=(i=d.value)==null?void 0:i.contentLinkText;return s&&s.length>0}),B=I(()=>{var i;const s=(i=d.value)==null?void 0:i.subTopics;return s&&s.length>0}),v=I(()=>{var i,b,T;const s=(T=(b=(i=d.value)==null?void 0:i.subTopics)==null?void 0:b[u.value])==null?void 0:T.contentLinkText;return s&&(s==null?void 0:s.length)>0}),w=I(()=>{const s=u.value===-1,i=y.value||B.value;return s&&y.value?{show:"text",showMainContent:!0}:i?{show:"subtext",showMainContent:!1}:{show:"notext",showMainContent:!0}}),N=s=>{u.value=s},S=s=>{a("focusTopicName",s)},M=()=>{a("back",u.value)};return A(()=>c.topicInfos,()=>{c.topicInfos&&(d.value=c.topicInfos??{})},{immediate:!0,deep:!0}),A(()=>c.currentIndex,()=>{c.currentIndex&&(u.value=c.currentIndex??-1)},{immediate:!0,deep:!0}),{currentTopicInfos:d,currentSubTopicIndex:u,noticeCarousel:C,changePage:N,isSpillout:L,showMainContent:l,isShowMore:h,focusTopicName:S,back:M,isShowContentOrSubInfo:w,hasContent:y,hasSubsContent:v}},et=tt,nt={class:"notice-detail-info"},ot={key:0},st={key:1},ct={key:2},at={key:0},it={key:1},rt={class:"messages"},ut={key:0},lt={key:0,class:"original-text-font text-gray-2"},dt=["onClick"],_t={key:0,class:"original-text-font text-gray-2"},pt=["onClick"],ft=P({__name:"NoticeDetailInfo",props:{topicInfos:{},currentIndex:{},showBack:{type:Boolean}},emits:["focusTopicName","back"],setup(c,{expose:a,emit:d}){const u=c,C=d,{currentTopicInfos:l,noticeCarousel:L,isShowContentOrSubInfo:h,currentSubTopicIndex:y,hasSubsContent:B,focusTopicName:v,back:w}=et(u,C);return a({focusTopicName:v}),(N,S)=>{var s,i,b,T,g,E,O;const M=z;return t(),n("div",nt,[N.showBack&&o(h).show==="subtext"?(t(),n("div",{key:0,ref:"backRef",class:"mb-2.5 text-brand-2 text-xs cursor-pointer",onClick:S[0]||(S[0]=(...e)=>o(w)&&o(w)(...e))},[f(M,{class:"text-brand-2"},{default:q(()=>[f(o(U))]),_:1}),j(p(N.$t("app.systemNotice.back")),1)],512)):$("",!0),k("div",null,[o(h).show==="text"?(t(),n("div",ot,[(t(!0),n(_,null,m((s=o(l))==null?void 0:s.contentLinkText,(e,r)=>(t(),n(_,{key:r},[r===0?(t(),F(D,{key:r,title:e==null?void 0:e.title,time:e==null?void 0:e.time},null,8,["title","time"])):$("",!0)],64))),128))])):o(h).show==="notext"?(t(),n("div",st,[f(D,{title:(i=o(l))==null?void 0:i.topicName,time:(b=o(l))==null?void 0:b.updateTime},null,8,["title","time"])])):o(h).show==="subtext"?(t(),n("div",ct,[o(B)?(t(),n("div",at,[(t(!0),n(_,null,m((g=(T=o(l))==null?void 0:T.subTopics[o(y)])==null?void 0:g.contentLinkText,(e,r)=>(t(),n(_,{key:r},[r===0?(t(),F(D,{key:0,title:e==null?void 0:e.title,time:e==null?void 0:e.time},null,8,["title","time"])):$("",!0)],64))),128))])):(t(),n("div",it,[f(D,{title:"",time:""})]))])):$("",!0)]),k("div",rt,[o(h).showMainContent?(t(),n("div",ut,[(t(!0),n(_,null,m((E=o(l))==null?void 0:E.contentLinkText,(e,r)=>(t(),n(_,{key:r},[e.type&&e.type==="text"?(t(),n("span",lt,p(e.text),1)):(t(),n("span",{key:1,class:"cursor-pointer text-brand-2 original-text-font",onClick:R=>o(v)(e.text)},p(e.text),9,dt))],64))),128))])):(t(),n("div",{key:1,ref_key:"noticeCarousel",ref:L},[(t(!0),n(_,null,m((O=o(l))==null?void 0:O.subTopics,e=>(t(),n("div",{key:e.subTopicIndex},[(t(!0),n(_,null,m(e.contentLinkText,(r,R)=>(t(),n(_,{key:R},[r.type==="text"?(t(),n("span",_t,p(r.text),1)):(t(),n("span",{key:1,class:"cursor-pointer text-brand-2 original-text-font",onClick:ht=>o(v)(r.text)},p(r.text),9,pt))],64))),128))]))),128))],512))])])}}});const yt=G(ft,[["__scopeId","data-v-bd3f2025"]]),vt=(c,a)=>V(`${Y}/announcement/queryTopicList`,{headers:{gid:a}}).post(c).json(),bt=(c,a)=>V(`${Y}/announcement/queryTopicDetail`,{headers:{gid:a}}).post(c).json(),Tt=(c,a)=>V(`${Y}/announcement/querySubTopicDetail`,{headers:{gid:a}}).post(c).json();export{yt as N,Tt as a,vt as g,bt as q};
