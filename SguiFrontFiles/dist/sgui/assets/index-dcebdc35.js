import{L as ue,ee as se,ea as d,eq as Q,eD as k,ej as y,ep as V,q as X,N as oe,v as ie,r as ce,b0 as de,ei as me,w as h,ef as E,em as pe,eG as be,ac as fe,o as ve,dQ as Ne,x as b,B as z,a5 as H,A as t,D as K,b1 as _,G as M,z as R,y as S,h3 as he,h4 as Ve,H as W,J as Y,ek as ye,h5 as Ie,E as P,ah as ge,_ as we,m as Ee,dP as _e,en as j,K as Se}from"./index-9381ab2b.js";import{v as J}from"./index-06c3636a.js";const Pe=ue({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:Boolean,disabled:Boolean,size:se,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:l=>l===null||d(l)||["min","max"].includes(l),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:l=>l>=0&&l===Number.parseInt(`${l}`,10)},validateEvent:{type:Boolean,default:!0}}),ke={[Q]:(l,A)=>A!==l,blur:l=>l instanceof FocusEvent,focus:l=>l instanceof FocusEvent,[k]:l=>d(l)||y(l),[V]:l=>d(l)||y(l)},Ae=["aria-label","onKeydown"],Fe=["aria-label","onKeydown"],xe=X({name:"ElInputNumber"}),Be=X({...xe,props:Pe,emits:ke,setup(l,{expose:A,emit:c}){const a=l,{t:O}=oe(),m=ie("input-number"),v=ce(),u=de({currentValue:a.modelValue,userInput:null}),{formItem:f}=me(),G=h(()=>d(a.modelValue)&&a.modelValue<=a.min),U=h(()=>d(a.modelValue)&&a.modelValue>=a.max),Z=h(()=>{const e=$(a.step);return E(a.precision)?Math.max($(a.modelValue),e):(e>a.precision,a.precision)}),F=h(()=>a.controls&&a.controlsPosition==="right"),L=pe(),N=be(),x=h(()=>{if(u.userInput!==null)return u.userInput;let e=u.currentValue;if(y(e))return"";if(d(e)){if(Number.isNaN(e))return"";E(a.precision)||(e=e.toFixed(a.precision))}return e}),B=(e,n)=>{if(E(n)&&(n=Z.value),n===0)return Math.round(e);let r=String(e);const s=r.indexOf(".");if(s===-1||!r.replace(".","").split("")[s+n])return e;const g=r.length;return r.charAt(g-1)==="5"&&(r=`${r.slice(0,Math.max(0,g-1))}6`),Number.parseFloat(Number(r).toFixed(n))},$=e=>{if(y(e))return 0;const n=e.toString(),r=n.indexOf(".");let s=0;return r!==-1&&(s=n.length-r-1),s},q=(e,n=1)=>d(e)?B(e+a.step*n):u.currentValue,C=()=>{if(a.readonly||N.value||U.value)return;const e=Number(x.value)||0,n=q(e);I(n),c(k,u.currentValue)},D=()=>{if(a.readonly||N.value||G.value)return;const e=Number(x.value)||0,n=q(e,-1);I(n),c(k,u.currentValue)},T=(e,n)=>{const{max:r,min:s,step:o,precision:p,stepStrictly:g,valueOnClear:w}=a;r<s&&Ee("InputNumber","min should not be greater than max.");let i=Number(e);if(y(e)||Number.isNaN(i))return null;if(e===""){if(w===null)return null;i=_e(w)?{min:s,max:r}[w]:w}return g&&(i=B(Math.round(i/o)*o,p)),E(p)||(i=B(i,p)),(i>r||i<s)&&(i=i>r?r:s,n&&c(V,i)),i},I=(e,n=!0)=>{var r;const s=u.currentValue,o=T(e);if(!n){c(V,o);return}s!==o&&(u.userInput=null,c(V,o),c(Q,o,s),a.validateEvent&&((r=f==null?void 0:f.validate)==null||r.call(f,"change").catch(p=>j())),u.currentValue=o)},ee=e=>{u.userInput=e;const n=e===""?null:Number(e);c(k,n),I(n,!1)},ne=e=>{const n=e!==""?Number(e):"";(d(n)&&!Number.isNaN(n)||e==="")&&I(n),u.userInput=null},te=()=>{var e,n;(n=(e=v.value)==null?void 0:e.focus)==null||n.call(e)},re=()=>{var e,n;(n=(e=v.value)==null?void 0:e.blur)==null||n.call(e)},ae=e=>{c("focus",e)},le=e=>{var n;c("blur",e),a.validateEvent&&((n=f==null?void 0:f.validate)==null||n.call(f,"blur").catch(r=>j()))};return fe(()=>a.modelValue,e=>{const n=T(u.userInput),r=T(e,!0);!d(n)&&(!n||n!==r)&&(u.currentValue=r,u.userInput=null)},{immediate:!0}),ve(()=>{var e;const{min:n,max:r,modelValue:s}=a,o=(e=v.value)==null?void 0:e.input;if(o.setAttribute("role","spinbutton"),Number.isFinite(r)?o.setAttribute("aria-valuemax",String(r)):o.removeAttribute("aria-valuemax"),Number.isFinite(n)?o.setAttribute("aria-valuemin",String(n)):o.removeAttribute("aria-valuemin"),o.setAttribute("aria-valuenow",u.currentValue||u.currentValue===0?String(u.currentValue):""),o.setAttribute("aria-disabled",String(N.value)),!d(s)&&s!=null){let p=Number(s);Number.isNaN(p)&&(p=null),c(V,p)}}),Ne(()=>{var e,n;const r=(e=v.value)==null?void 0:e.input;r==null||r.setAttribute("aria-valuenow",`${(n=u.currentValue)!=null?n:""}`)}),A({focus:te,blur:re}),(e,n)=>(b(),z("div",{class:K([t(m).b(),t(m).m(t(L)),t(m).is("disabled",t(N)),t(m).is("without-controls",!e.controls),t(m).is("controls-right",t(F))]),onDragstart:n[1]||(n[1]=P(()=>{},["prevent"]))},[e.controls?H((b(),z("span",{key:0,role:"button","aria-label":t(O)("el.inputNumber.decrease"),class:K([t(m).e("decrease"),t(m).is("disabled",t(G))]),onKeydown:_(D,["enter"])},[M(t(W),null,{default:R(()=>[t(F)?(b(),S(t(he),{key:0})):(b(),S(t(Ve),{key:1}))]),_:1})],42,Ae)),[[t(J),D]]):Y("v-if",!0),e.controls?H((b(),z("span",{key:1,role:"button","aria-label":t(O)("el.inputNumber.increase"),class:K([t(m).e("increase"),t(m).is("disabled",t(U))]),onKeydown:_(C,["enter"])},[M(t(W),null,{default:R(()=>[t(F)?(b(),S(t(ye),{key:0})):(b(),S(t(Ie),{key:1}))]),_:1})],42,Fe)),[[t(J),C]]):Y("v-if",!0),M(t(ge),{id:e.id,ref_key:"input",ref:v,type:"number",step:e.step,"model-value":t(x),placeholder:e.placeholder,readonly:e.readonly,disabled:t(N),size:t(L),max:e.max,min:e.min,name:e.name,label:e.label,"validate-event":!1,onWheel:n[0]||(n[0]=P(()=>{},["prevent"])),onKeydown:[_(P(C,["prevent"]),["up"]),_(P(D,["prevent"]),["down"])],onBlur:le,onFocus:ae,onInput:ee,onChange:ne},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","label","onKeydown"])],34))}});var Ce=we(Be,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/input-number/src/input-number.vue"]]);const ze=Se(Ce);export{ze as E};
