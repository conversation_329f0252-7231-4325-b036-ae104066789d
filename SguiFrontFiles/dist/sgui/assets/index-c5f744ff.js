import{L as x,M as V,e8 as W,ep as $,eq as A,ea as q,r as f,ac as z,W as G,v as B,w as r,q as b,x as D,B as H,F as K,D as m,A as e,_ as M,O as L,P as k,ak as j,Q,G as y,z as I,hu as U,H as Z,b1 as J,E as R,a5 as X,a6 as Y,K as ee,Z as se}from"./index-9381ab2b.js";import{c as N}from"./castArray-25c7c99e.js";import{_ as ae}from"./index-e8380056.js";const S=()=>Math.floor(Math.random()*1e4),g=s=>typeof q(s),te=x({accordion:Boolean,modelValue:{type:V([Array,String,Number]),default:()=>W([])}}),le={[$]:g,[A]:g},T=Symbol("collapseContextKey"),oe=(s,o)=>{const a=f(N(s.modelValue)),n=l=>{a.value=l;const i=s.accordion?a.value[0]:a.value;o($,i),o(A,i)},t=l=>{if(s.accordion)n([a.value[0]===l?"":l]);else{const i=[...a.value],c=i.indexOf(l);c>-1?i.splice(c,1):i.push(l),n(i)}};return z(()=>s.modelValue,()=>a.value=N(s.modelValue),{deep:!0}),G(T,{activeNames:a,handleItemClick:t}),{activeNames:a,setActiveNames:n}},ne=()=>{const s=B("collapse");return{rootKls:r(()=>s.b())}},ie=b({name:"ElCollapse"}),ce=b({...ie,props:te,emits:le,setup(s,{expose:o,emit:a}){const n=s,{activeNames:t,setActiveNames:l}=oe(n,a),{rootKls:i}=ne();return o({activeNames:t,setActiveNames:l}),(c,u)=>(D(),H("div",{class:m(e(i))},[K(c.$slots,"default")],2))}});var re=M(ce,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse/src/collapse.vue"]]);const de=x({title:{type:String,default:""},name:{type:V([String,Number]),default:()=>S()},disabled:Boolean}),ue=s=>{const o=L(T),a=f(!1),n=f(!1),t=f(S()),l=r(()=>o==null?void 0:o.activeNames.value.includes(s.name));return{focusing:a,id:t,isActive:l,handleFocus:()=>{setTimeout(()=>{n.value?n.value=!1:a.value=!0},50)},handleHeaderClick:()=>{s.disabled||(o==null||o.handleItemClick(s.name),a.value=!1,n.value=!0)},handleEnterClick:()=>{o==null||o.handleItemClick(s.name)}}},pe=(s,{focusing:o,isActive:a,id:n})=>{const t=B("collapse"),l=r(()=>[t.b("item"),t.is("active",e(a)),t.is("disabled",s.disabled)]),i=r(()=>[t.be("item","header"),t.is("active",e(a)),{focusing:e(o)&&!s.disabled}]),c=r(()=>[t.be("item","arrow"),t.is("active",e(a))]),u=r(()=>t.be("item","wrap")),C=r(()=>t.be("item","content")),h=r(()=>t.b(`content-${e(n)}`)),_=r(()=>t.b(`head-${e(n)}`));return{arrowKls:c,headKls:i,rootKls:l,itemWrapperKls:u,itemContentKls:C,scopedContentId:h,scopedHeadId:_}},me=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],ve=["id","aria-hidden","aria-labelledby"],fe=b({name:"ElCollapseItem"}),be=b({...fe,props:de,setup(s,{expose:o}){const a=s,{focusing:n,id:t,isActive:l,handleFocus:i,handleHeaderClick:c,handleEnterClick:u}=ue(a),{arrowKls:C,headKls:h,rootKls:_,itemWrapperKls:P,itemContentKls:O,scopedContentId:E,scopedHeadId:w}=pe(a,{focusing:n,isActive:l,id:t});return o({isActive:l}),(v,d)=>(D(),H("div",{class:m(e(_))},[k("button",{id:e(w),class:m(e(h)),"aria-expanded":e(l),"aria-controls":e(E),"aria-describedby":e(E),tabindex:v.disabled?-1:0,type:"button",onClick:d[0]||(d[0]=(...p)=>e(c)&&e(c)(...p)),onKeydown:d[1]||(d[1]=J(R((...p)=>e(u)&&e(u)(...p),["stop","prevent"]),["space","enter"])),onFocus:d[2]||(d[2]=(...p)=>e(i)&&e(i)(...p)),onBlur:d[3]||(d[3]=p=>n.value=!1)},[K(v.$slots,"title",{},()=>[j(Q(v.title),1)]),y(e(Z),{class:m(e(C))},{default:I(()=>[y(e(U))]),_:1},8,["class"])],42,me),y(e(ae),null,{default:I(()=>[X(k("div",{id:e(E),role:"region",class:m(e(P)),"aria-hidden":!e(l),"aria-labelledby":e(w)},[k("div",{class:m(e(O))},[K(v.$slots,"default")],2)],10,ve),[[Y,e(l)]])]),_:3})],2))}});var F=M(be,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse/src/collapse-item.vue"]]);const Ee=ee(re,{CollapseItem:F}),ke=se(F);export{ke as E,Ee as a,S as g};
