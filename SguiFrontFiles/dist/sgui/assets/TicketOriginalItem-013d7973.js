import{ao as q,gP as U,gX as ye,gW as A,R as ae,gR as Ot,q as ie,aC as Ft,aD as Lt,w as oe,r as P,o as ge,x as n,y as ce,z as L,G as I,P as t,Q as s,B as l,ai as M,aj as V,ab as jt,b0 as St,b4 as Pt,ar as Bt,a5 as ue,A as g,D as z,b3 as Tt,a6 as ke,J as j,F as me,ak as pe,E as Mt,ah as qt,al as Ut,aZ as wt,am as Vt,an as Qt,s as Nt,aS as At,eM as be,av as Rt,iV as ve,gf as fe,au as le,c2 as Ht,ag as $t,aV as Dt,af as Yt,H as Xt}from"./index-9381ab2b.js";import{E as Gt,a as zt}from"./index-c5f744ff.js";import{_ as he}from"./_plugin-vue_export-helper-c27b6911.js";import{E as Wt}from"./index-e22833ad.js";import{E as It}from"./index-34c19038.js";import{E as Jt,a as Kt}from"./index-951011fc.js";import{E as Zt}from"./index-c19c3f80.js";import{E as ea}from"./index-7b8ec8cc.js";import{B as _e,C as ta,j as aa}from"./ticketOperationApi-8106707a.js";import{a as sa}from"./time-c3069dc1.js";import{ac as ra}from"./regular-crs-0d781ceb.js";import{a as oa}from"./config-b573cde3.js";import{E as ia}from"./index-847d31f7.js";const ni=(r,d)=>q(`${U}/apiAvSearch/computeInterAirPrice`,{headers:{gid:d}}).post(r).json(),li=(r,d)=>q(`${U}/fare/domestic`,{headers:{gid:d}}).post(r).json(),ci=(r,d)=>q(`${U}/fare/queryInterReprice`,{headers:{gid:d}}).post(r).json(),di=(r,d)=>q(`${U}/fare/queryDomesticReprice`,{headers:{gid:d}}).post(r).json(),ui=(r,d)=>q(`${U}/apiAvSearch/batchInterAirPriceFormat`,{headers:{gid:d}}).post(r).json(),pi=(r,d)=>q(`${U}/fare/route/query`,{headers:{gid:d}}).post(r).json(),gi=(r,d)=>q(`${U}/apiAvSearch/fareRuleBaggage`,{headers:{gid:d}}).post(r).json(),fi=(r,d)=>q(`${U}/apiAvSearch/domesticFreeBaggage`,{headers:{gid:d}}).post(r).json(),yi=(r,d)=>q(`${U}/apiAvSearch/historyAndNewPriceCompute`,{headers:{gid:d}}).post(r).json(),hi=(r,d)=>q(`${U}/fare/domestic`,{headers:{gid:d}}).post(r).json(),vi=(r,d)=>q(`${U}/apiAvSearch/fareIntoAndEtdz`,{headers:{gid:d}}).post(r).json(),na=(r,d)=>q(`${U}/apiAvSearch/queryRuleForInterAirPrice`,{headers:{gid:d}}).post(r).json(),_i=(r,d)=>q(`${U}/fare/queryRTKTDetail`,{headers:{gid:d}},{originalValue:!0,ignoreError:!0}).post(r).json(),ki=(r,d)=>q(`${U}/fare/cancelCreditCardAuth`,{headers:{gid:d}}).post(r).json(),mi=(r,d)=>q(`${U}/cpay/crsCdsPay`,{headers:{gid:d}}).post(r).json(),bi=(r,d)=>q(`${U}/bopPay/crsBopPay`,{headers:{gid:d}}).post(r).json(),xi=(r,d)=>q(`${U}/bopPay/cancelPayment`,{headers:{gid:d}}).post(r).json(),Ti=(r,d)=>q(`${U}/fare/queryTicketsDetail`,{headers:{gid:d}},{ignoreError:!0}).post(r).json(),la=[{code:"<",offset:"-1"},{code:">",offset:"+1"},{code:"\\",offset:"+2"},{code:"+",offset:"+2"}],Ct=(r,d)=>!r||!d||!Ot.test(d)?"":`${r}T${d.slice(0,2)}:${d.slice(2)}`,ca=r=>r.charAt(0).toUpperCase()+r.slice(1).toLowerCase(),da=(r,d,k,m)=>{if(ra.test(r)){const D=ae(new Date).format("YYYY").slice(0,2),[f,b,C]=r.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),w=`${D}${C}`,x=ae(`${w}-${ca(b)}-${f}`,"YYYY-MMM-DD");if(!x.isValid())throw new Error;return x.format("YYYY-MM-DD")}let o=k,c=m;(o??"")===""&&(o=ae(new Date).format("YYYY-MM-DD"),c=ae(new Date).format("HHmm"));const e=o.substring(0,4);let y,h;const $=r?sa(r,e):`${e}-${o.substring(5)}`;return c!==""&&d!==""?(y=ae(`${e}-${o.substring(5)}:${c}`),h=ae(`${$}:${d}`)):(y=ae(`${e}-${o.substring(5)}`),h=ae(`${$}`)),h.isBefore(y)?h.add(1,"year").format("YYYY-MM-DD"):h.format("YYYY-MM-DD")},ua=r=>({[A.FLIGHT_STATUS]:{value:"",required:!0},[A.COMPANY_CODE]:{value:"",required:!0},[A.FLIGHT_NUMBER]:{value:"",required:r},[A.CABIN_CODE]:{value:"",required:!0},[A.DEPARTURE_DATE]:{value:"",required:r},[A.DEPARTURE_AIRPORT]:{value:"",required:!0},[A.DATE_TIME_RANGE]:{value:"",required:r},[A.ARRIVAL_AIRPORT]:{value:"",required:!0},[A.STOP_QUANTITY]:{value:"",required:!0},[A.STOP_FLAG]:{value:"",required:!0,unorderedCode:[A.GLOBAL_INDICATOR]},[A.AIR_EQUIPMENT_TYPE]:{value:""},[A.OC_AIRLINE]:{value:""},[A.OPEN_AND_CLOSE_CABINS]:{value:""}}),pa=()=>({[A.GLOBAL_INDICATOR]:{value:""}}),ga=r=>{const d=r[0]!=="O";let k=r.replaceAll(/ */gi,"");const m=pa(),o=ua(d);try{if(Object.keys(o).forEach(e=>{var $;const y=ye.get(e),h=o[e];if(y){const D=y.exec(k);if(!D||D.index){if(h!=null&&h.required)throw new Error;return}h.value=D[0],k=k.slice(D[0].length),($=h.unorderedCode)!=null&&$.length&&h.unorderedCode.forEach(f=>{const b=m[f],C=new RegExp(ye.get(f),"g"),w=k.match(C);if(w!=null&&w.length){if(w.length>1)throw new Error;b.value=w[0],k=k.replace(C,"")}})}}),k)throw new Error;return{valid:!0,flightInfoForm:{...o,...m}}}catch{return{valid:!1,flightInfoForm:{...o,...m}}}},fa=(r,d,k,m)=>{var b;if(!r&&!d)return{departureDateTime:"",arrivalDateTime:""};const o=d,c=o.slice(0,4),e=da(r,c,k,m),y=((b=la.find(C=>o.includes(C.code)))==null?void 0:b.offset)??0,h=ae(e).add(Number(y),"day").format("YYYY-MM-DD"),$=y?o.slice(5):o.slice(4),D=Ct(e,c||"0000"),f=Ct(h,$||"0000");return{departureDateTime:D,arrivalDateTime:f}},ya=r=>r.slice(2),ha=r=>{if(!r)return{openCabins:[],closeCabins:[]};let d=r,k=[],m=[];const o=ye.get(A.OPEN_CABINS),c=o.exec(d);if(o&&c){const h=c[0];if(k=h.replace("#D","").split("")??[],c.index)return{openCabins:k,closeCabins:[]};d=d.slice(h.length)}return ye.get(A.CLOSE_CABINS).exec(d)&&(m=d.replace("#C","").split("")??[]),{openCabins:k,closeCabins:m}},va=(r,d,k)=>{const m=r[A.FLIGHT_STATUS].value==="O",{departureDateTime:o,arrivalDateTime:c}=fa(r[A.DEPARTURE_DATE].value,r[A.DATE_TIME_RANGE].value,d,k),{openCabins:e,closeCabins:y}=ha(r[A.OPEN_AND_CLOSE_CABINS].value),h=ya(r[A.OC_AIRLINE].value),$=r[A.GLOBAL_INDICATOR].value.slice(2);return{openFlag:m,globalIndicator:$,flightStatus:r[A.FLIGHT_STATUS].value.replace("*",""),companyCode:r[A.COMPANY_CODE].value,flightNumber:r[A.FLIGHT_NUMBER].value,cabinCode:r[A.CABIN_CODE].value,departureAirport:r[A.DEPARTURE_AIRPORT].value,departureDateTime:o,arrivalDateTime:c,arrivalAirport:r[A.ARRIVAL_AIRPORT].value,stopQuantity:r[A.STOP_QUANTITY].value,stopFlag:r[A.STOP_FLAG].value,airEquipmentType:r[A.AIR_EQUIPMENT_TYPE].value,operatingAirline:h,openCabins:e,closeCabins:y,flightType:""}},$i=(r,d,k)=>{const m=[];return r.some(e=>{const{valid:y,flightInfoForm:h}=ga(e);return m.push(h),!y})?[]:m.map(e=>[va(e,d??"",k??"")])??[]},_a=r=>{const d=[],k=[];return r.forEach(m=>{oa.some(o=>o.value.toUpperCase()===m.toUpperCase())?k.push({passengerType:m==="CNN"?"CHD":m,selectedNumber:1}):d.push({passengerType:m,selectedNumber:1})}),{selectedPassengers:k,groupPassengers:d}},Di=r=>{switch(r){case"RR":case"HK":return"S";case"HL":case"HN":return"L";default:return"U"}},Ci=r=>r?` #O${r}`:"",ka={class:"item-name"},ma={class:"item-state"},ba={class:"item-container"},xa={class:"item"},Ta={class:"item-text"},$a={class:"item-name"},Da={class:"item-state"},Ca={class:"item-container"},wa={class:"item"},Na={class:"item-text"},Aa={class:"item-name"},Ra={class:"item-state"},Ia={class:"item-container"},Ea={class:"item"},Oa={class:"item-text"},Fa={class:"item-name"},La={class:"item-state"},ja={class:"item-container"},Sa={class:"item"},Pa={class:"item-text"},Ba=ie({__name:"CollapseInfo",props:{rule:{}},setup(r){const d=Ft(),{orderInfo:k,activeTag:m}=Lt(d),o=oe(()=>d.getFareType(m.value)),c=oe(()=>k.value.get(m.value).type==="2"),e=oe(()=>k.value.get(m.value).international),y=P("retract"),h=P("retract"),$=P(["change","refund"]),D=f=>{(f??[]).includes("change")?h.value="retract":h.value="unfold",(f??[]).includes("refund")?y.value="retract":y.value="unfold"};return ge(()=>{!e.value&&c.value&&o.value==="normalFare"&&(y.value="retract",h.value="retract")}),(f,b)=>{const C=Gt,w=zt;return!e.value&&c.value&&o.value==="normalFare"?(n(),ce(w,{key:0,modelValue:$.value,"onUpdate:modelValue":b[0]||(b[0]=x=>$.value=x),onChange:D},{default:L(()=>[I(C,{name:"change"},{title:L(()=>[t("div",ka,[t("span",null,s(f.$t("app.fareQuery.freightate.revalidationRule")),1),t("span",ma,s(f.$t(`app.fareQuery.freightate.${h.value??"unfold"}`)),1)])]),default:L(()=>[t("div",ba,[t("div",xa,[(n(!0),l(M,null,V(f.rule.ruleInfos.change,(x,S)=>(n(),l("div",{key:S},[t("div",Ta,s(x.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),I(C,{name:"refund"},{title:L(()=>[t("div",$a,[t("span",null,s(f.$t("app.fareQuery.freightate.refund")),1),t("span",Da,s(f.$t(`app.fareQuery.freightate.${y.value??"unfold"}`)),1)])]),default:L(()=>[t("div",Ca,[t("div",wa,[(n(!0),l(M,null,V(f.rule.ruleInfos.refund,(x,S)=>(n(),l("div",{key:S},[t("span",Na,s(x.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"])):(n(),ce(w,{key:1,modelValue:$.value,"onUpdate:modelValue":b[1]||(b[1]=x=>$.value=x),onChange:D},{default:L(()=>[I(C,{name:"change"},{title:L(()=>[t("div",Aa,[t("span",null,s(f.$t("app.fareQuery.freightate.revalidationRule")),1),t("span",Ra,s(f.$t(`app.fareQuery.freightate.${h.value??"unfold"}`)),1)])]),default:L(()=>[t("div",Ia,[t("div",Ea,[(n(!0),l(M,null,V(f.rule.ruleInfos.change,(x,S)=>(n(),l("div",{key:S},[t("div",Oa,s(x.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),I(C,{name:"refund"},{title:L(()=>[t("div",Fa,[t("span",null,s(f.$t("app.fareQuery.freightate.refund")),1),t("span",La,s(f.$t(`app.fareQuery.freightate.${y.value??"unfold"}`)),1)])]),default:L(()=>[t("div",ja,[t("div",Sa,[(n(!0),l(M,null,V(f.rule.ruleInfos.refund,(x,S)=>(n(),l("div",{key:S},[t("span",Pa,s(x.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"]))}}});const Ma=he(Ba,[["__scopeId","data-v-360264be"]]),qa=r=>{var X;const{t:d}=jt(),k=P([]),m=P(!1),o=P([]),c=P([]),e=P(),y=P(0),h=P(!1),$=(X=navigator==null?void 0:navigator.userAgent)==null?void 0:X.toLowerCase(),D=oe(()=>$==null?void 0:$.includes("electron/")),f=oe(()=>r.isCnLang),b=P(),C=P(),w=P({serialNumber:"16"}),x=P(!1),S=oe(()=>r.rulesRes.translatedRuleInfo??""),T=St({proChange:[],maxStay:"",minStay:""}),E={serialNumber:[{pattern:Pt,message:d("app.fareQuery.freightate.inputSplitNum")}]},N=O=>{k.value=O.map(F=>({...F,contents:F.contents.map(v=>({strList:v.split("<br>")}))}))},B=()=>{var O;m.value=!1,(O=b.value)==null||O.validate(F=>{if(F)if(w.value.serialNumber){const v=w.value.serialNumber.split("/"),a=[...new Set(v)].map(Number),i=[];a.forEach(p=>{i.push(...r.rulesRes.fareRuleInfos.filter(_=>p===Number(_.number)))}),N(i)}else N(r.rulesRes.fareRuleInfos)})},J=()=>{var v,Q,a,i;m.value=!0,w.value.serialNumber="";const O=oe(()=>r.rulesRes.fareRuleInfos.map(p=>({number:p.number,title:p.title}))),F=Math.ceil(((v=O.value)==null?void 0:v.length)/2);o.value=(Q=O.value)==null?void 0:Q.slice(0,F),c.value=(i=O.value)==null?void 0:i.slice(F,(a=O.value)==null?void 0:a.length)},W=O=>{w.value.serialNumber=O,B()},K=O=>{switch(O){case"1":return"per one way";case"2":return"per round way";case"3":return"per ticket";case"4":return"per coupon";case"5":return"per direction";default:return"-"}},Z=O=>{switch(O){case"H":return"(取高)";case"L":return"(取低)";case"A":return"(叠加)";default:return""}},Y=()=>{var O;y.value=((O=e.value)==null?void 0:O.offsetWidth)??0,h.value=!(y.value<726)};return ge(async()=>{B(),Y(),window.addEventListener("resize",Y)}),Bt(()=>{k.value=[],window.removeEventListener("resize",Y)}),{interRuleContent:k,priceRules:T,queryRuleInfo:B,QUIT_OR_UPDATE_RULE:E,quitOrUpdateRuleForm:w,quitOrUpdateRuleRef:b,loading:x,scrollbar:C,getHightLow:Z,getChargePer:K,queryList:J,isList:m,leftColumn:o,rightColumn:c,queryRuleByNumber:W,isChineseShow:f,isCnRulesText:S,isClient:D,infoRuleRef:e,innerWidth:y,checkLang:h}},Ua=qa,Va=r=>(Vt("data-v-18280113"),r=r(),Qt(),r),Qa={class:"u-cat"},Ha={class:"u-cat-tit"},Ya={class:"u-cat-cont"},Xa={key:0,class:"text-gray-2 text-xs"},Ga={key:1,class:"loading-text"},za={class:"pasg-name-tag"},Wa=Va(()=>t("span",{class:"iconfont icon-info-circle-line"},null,-1)),Ja={key:0},Ka={key:1,class:"loading-text"},Za={key:0},es=["onClick"],ts=["onClick"],as={key:1,class:"loading-text"},ss=ie({__name:"InterRuleInfo",props:{rulesRes:{},detailInfo:{},ruleFullHeight:{type:Boolean},isCnLang:{type:Boolean},openDialog:{type:Boolean},fsn:{type:Boolean},issueDate:{}},setup(r){const d=r,{interRuleContent:k,queryRuleInfo:m,quitOrUpdateRuleRef:o,quitOrUpdateRuleForm:c,QUIT_OR_UPDATE_RULE:e,loading:y,scrollbar:h,queryList:$,isList:D,queryRuleByNumber:f,leftColumn:b,rightColumn:C,isCnRulesText:w,isChineseShow:x,infoRuleRef:S,innerWidth:T,checkLang:E}=Ua(d);return(N,B)=>{const J=Wt,W=It,K=Jt,Z=Zt,Y=qt,X=Ut,O=Kt,F=wt;return ue((n(),l("div",{ref_key:"infoRuleRef",ref:S,class:z(["rule-table mt-1",N.ruleFullHeight?"h-[668px]":"h-[368px]",g(T)<726&&!N.ruleFullHeight?"block px-1 max-w-[526px] border-none bg-gray-0":"flex bg-gray-7"])},[g(T)>726||g(E)&&g(T)<726||N.ruleFullHeight?(n(),l("div",{key:0,class:z(["u-content bg-gray-7",g(T)<726&&!N.ruleFullHeight?"h-[368px]":""])},[ue(t("p",null,[I(J,{modelValue:g(E),"onUpdate:modelValue":B[0]||(B[0]=v=>Tt(E)?E.value=v:null),"inline-prompt":"","inactive-text":N.$t("app.fareQuery.freightate.en"),"active-text":N.$t("app.fareQuery.freightate.cn")},null,8,["modelValue","inactive-text","active-text"])],512),[[ke,g(E)&&g(T)<726&&!N.ruleFullHeight]]),I(W,{class:z([N.ruleFullHeight?"h-[668px]":"h-[368px]"])},{default:L(()=>[t("div",Qa,[t("div",Ha,[t("span",{class:z([N.fsn?"w-[345px]":""])},null,2)]),t("div",Ya,[g(w)?(n(),l("div",Xa,[t("pre",null,s(g(w)),1)])):(n(),l("div",Ga,s(N.$t("app.fareQuery.freightate.noDataAvailable")),1))])])]),_:1},8,["class"])],2)):j("",!0),g(T)>726||!g(E)&&g(T)<726||N.ruleFullHeight?(n(),l("div",{key:1,class:z(["w-[526px] flex flex-col crs-new-ui-init-cls border-box en-box bg-gray-7",g(T)<726&&!N.ruleFullHeight?"px-1 h-[368px]":""])},[I(O,{ref_key:"quitOrUpdateRuleRef",ref:o,class:"relative",inline:"",model:g(c),rules:g(e),onSubmit:B[3]||(B[3]=Mt(()=>{},["prevent"]))},{default:L(()=>[me(N.$slots,"default",{},void 0,!0),ue(I(K,null,{default:L(()=>[I(J,{modelValue:g(E),"onUpdate:modelValue":B[1]||(B[1]=v=>Tt(E)?E.value=v:null),"inline-prompt":"","inactive-text":N.$t("app.fareQuery.freightate.en"),"active-text":N.$t("app.fareQuery.freightate.cn")},null,8,["modelValue","inactive-text","active-text"])]),_:1},512),[[ke,!g(E)&&g(T)<726&&!N.ruleFullHeight]]),I(K,{label:N.$t("app.fareQuery.freightate.ruleNumber"),class:"rule-number-item",prop:"serialNumber"},{default:L(()=>[t("div",za,[I(Z,{placement:"top"},{content:L(()=>[t("p",null,s(N.$t("app.fareQuery.freightate.inputSplitNumTip")),1)]),default:L(()=>[Wa]),_:1})]),I(Y,{modelValue:g(c).serialNumber,"onUpdate:modelValue":B[2]||(B[2]=v=>g(c).serialNumber=v)},null,8,["modelValue"])]),_:1},8,["label"]),I(X,{type:"primary",size:"small",onClick:g(m)},{default:L(()=>[pe(s(N.$t("app.fareQuery.freightate.search")),1)]),_:1},8,["onClick"]),I(X,{type:"primary",link:"",size:"small",class:"ml-1",onClick:g($)},{default:L(()=>[pe(s(N.$t("app.fareQuery.freightate.list")),1)]),_:1},8,["onClick"])]),_:3},8,["model","rules"]),I(W,{ref_key:"scrollbar",ref:h,height:N.ruleFullHeight?"610":"318",class:"mt-2.5 text-xs text-gray-2 grow"},{default:L(()=>[g(D)?j("",!0):(n(!0),l(M,{key:0},V(g(k),(v,Q)=>(n(),l("div",{key:Q},[t("pre",null,s(`${v.number}.${v.title}`),1),v.contents.length?(n(),l("div",Ja,[(n(!0),l(M,null,V(v.contents,(a,i)=>(n(),l("div",{key:i},[(n(!0),l(M,null,V(a.strList,p=>(n(),l("pre",{key:p,class:"whitespace-pre-wrap"},s(p),1))),128))]))),128))])):(n(),l("div",Ka,s(N.$t("app.fareQuery.freightate.noDataAvailable")),1))]))),128)),g(D)&&!g(x)?(n(),l(M,{key:1},[g(b).length>0||g(C).length>0?(n(),l("div",Za,[(n(!0),l(M,null,V(g(b),(v,Q)=>(n(),l("div",{key:Q},[g(b).length>0?(n(),l("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:a=>g(f)(`${v==null?void 0:v.number}`)},[t("span",null,s((v==null?void 0:v.number)<10?`0${v==null?void 0:v.number}`:`${v==null?void 0:v.number}`)+" "+s(v==null?void 0:v.title),1)],8,es)):j("",!0)]))),128)),(n(!0),l(M,null,V(g(C),(v,Q)=>(n(),l("div",{key:Q},[g(C).length>0?(n(),l("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:a=>g(f)(`${v==null?void 0:v.number}`)},[t("span",null,s((v==null?void 0:v.number)<10?`0${v==null?void 0:v.number}`:`${v==null?void 0:v.number}`)+" "+s(v==null?void 0:v.title),1)],8,ts)):j("",!0)]))),128))])):(n(),l("div",as,s(N.$t("app.fareQuery.freightate.noDataAvailable")),1))],64)):j("",!0)]),_:1},8,["height"])],2)):j("",!0)],2)),[[F,g(y)]])}}});const Et=he(ss,[["__scopeId","data-v-18280113"]]),rs={key:0,class:"w-[1012px]"},os={class:"title flex gap-1"},is={key:0,class:"bg-gray-7 text-gray-3 text-[12px] font-[400] py-[4px] px-[5px] rounded-[2px]"},ns={key:1},ls={key:2},cs={class:"content"},ds={key:1,class:"empty-info"},us=ie({__name:"TicketRuleDialog",props:{detailInfo:{},isCnLang:{type:Boolean},issueDate:{},fsnTicketNos:{}},emits:["update:modelValue"],setup(r,{emit:d}){const k=d,m=()=>{k("update:modelValue",!1)},o=Nt([]),c=e=>{const y=o.value.find(h=>h.airportCode===e.toUpperCase());return y?y.airportEnName:e};return ge(async()=>{const e=await At("searchLocalData");o.value=e?JSON.parse(e==null?void 0:e.localData):[]}),(e,y)=>{const h=It,$=ea;return n(),ce($,{title:e.$t("app.fareQuery.freightate.tktRule"),class:"tkt-rules-dialog","align-center":!0,modal:"","close-on-click-modal":!1,width:"1040",onClose:m},{default:L(()=>[I(h,{ref:"scrollbar",height:"688"},{default:L(()=>{var D,f,b;return[((f=(D=e.detailInfo.ticketRegulation)==null?void 0:D.flightRules[0])==null?void 0:f.rules.length)!==0?(n(),l("div",rs,[(n(!0),l(M,null,V(((b=e.detailInfo.ticketRegulation)==null?void 0:b.flightRules)??[],(C,w)=>(n(),l("div",{key:w,class:"rule-item overflow-y-hidden"},[(n(!0),l(M,null,V(C.rules,(x,S)=>{var T,E;return n(),l("div",{key:S,class:"min-w-full"},[t("div",os,[(T=e.fsnTicketNos)!=null&&T[S]?(n(),l("span",is,s(g(be)((E=e.fsnTicketNos)==null?void 0:E[S])),1)):j("",!0),g(Rt)()==="en"?(n(),l("span",ns,s(`${c(x.departureAirport)}(${x.departureAirport}) - ${c(x.arrivalAirport)}(${x.arrivalAirport})`),1)):(n(),l("span",ls,s(`${(x==null?void 0:x.departureAirportCityCh)??""}(${x.departureAirport}) - ${(x==null?void 0:x.arrivalAirportCityCh)??""}(${x.arrivalAirport})`),1))]),t("div",cs,[I(Et,{"is-cn-lang":e.isCnLang,"rules-res":x,"detail-info":e.detailInfo,"rule-full-height":"","issue-date":e.issueDate},{default:L(()=>[me(e.$slots,"default")]),_:2},1032,["is-cn-lang","rules-res","detail-info","issue-date"])])])}),128))]))),128))])):(n(),l("div",ds,[t("div",null,s(e.$t("app.fareQuery.freightate.noTktData")),1)]))]}),_:3},512)]),_:3},8,["title"])}}});const ps={class:"min-h-[50px] mt-[10px]"},gs={key:0,class:"close-box"},fs={class:"flex gap-1 items-center"},ys={class:"title flex gap-1"},hs={key:0,class:"bg-gray-7 text-gray-3 text-[12px] font-[400] py-[4px] px-[5px] rounded-[2px]"},vs={key:1},_s={key:2},ks={class:"content"},ms=ie({__name:"TicketRule",props:{international:{type:Boolean},ruleInfoData:{},detailInfo:{},needCloseIcon:{type:Boolean},fullHeight:{type:Boolean},fsn:{type:Boolean},issueDate:{},fsnTicketNos:{},isShowWindow:{type:Boolean,default:!1}},setup(r){const d=P(!1),k=P(!1),m=()=>{d.value=!0},o=Nt([]),c=e=>{const y=o.value.find(h=>h.airportCode===e.toUpperCase());return y?y.airportEnName:e};return ge(async()=>{const e=await At("searchLocalData");o.value=e?JSON.parse(e==null?void 0:e.localData):[]}),(e,y)=>{var h;return n(),l("div",ps,[t("div",{class:z(["rule-info",[e.fullHeight||e.fsn?"":" border-t border-gray-7 border-solid",e.fsn?"":"mt-2.5 mr-2.5"]])},[e.needCloseIcon?(n(),l("div",gs,[t("div",fs,[e.needCloseIcon?me(e.$slots,"default",{key:0},void 0,!0):j("",!0),e.international?(n(),l("span",{key:1,class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer",onClick:m},s(e.$t("app.fareQuery.freightate.fullView")),1)):j("",!0)])])):j("",!0),(n(!0),l(M,null,V(((h=e.ruleInfoData)==null?void 0:h.flightRules)??[],($,D)=>(n(),l("div",{key:D,class:"rule-item overflow-y-hidden"},[(n(!0),l(M,null,V($.rules,(f,b)=>{var C,w;return n(),l("div",{key:b,class:"min-w-full"},[t("div",ys,[(C=e.fsnTicketNos)!=null&&C[b]?(n(),l("span",hs,s(g(be)((w=e.fsnTicketNos)==null?void 0:w[b])),1)):j("",!0),g(Rt)()==="en"?(n(),l("span",vs,s(`${c(f.departureAirport)}(${f.departureAirport}) - ${c(f.arrivalAirport)}(${f.arrivalAirport})`),1)):(n(),l("span",_s,s(`${(f==null?void 0:f.departureAirportCityCh)??""}(${f.departureAirport}) - ${(f==null?void 0:f.arrivalAirportCityCh)??""}(${f.arrivalAirport})`),1))]),t("div",ks,[e.international?(n(),ce(Et,{key:0,"is-cn-lang":k.value,"onUpdate:isCnLang":y[0]||(y[0]=x=>k.value=x),"rules-res":f,"detail-info":e.detailInfo,"rule-full-height":e.fullHeight,fsn:e.fsn,"issue-date":e.issueDate},null,8,["is-cn-lang","rules-res","detail-info","rule-full-height","fsn","issue-date"])):(n(),ce(Ma,{key:1,rule:f},null,8,["rule"]))])])}),128))]))),128))],2),I(us,{modelValue:d.value,"onUpdate:modelValue":y[1]||(y[1]=$=>d.value=$),"is-cn-lang":k.value,"onUpdate:isCnLang":y[2]||(y[2]=$=>k.value=$),"detail-info":e.detailInfo,"issue-date":e.issueDate,"fsn-ticket-nos":e.fsnTicketNos},null,8,["modelValue","is-cn-lang","detail-info","issue-date","fsn-ticket-nos"])])}}});const bs=he(ms,[["__scopeId","data-v-d480855a"]]),xs={class:"flex-grow overflow-y-auto border border-gray-6 mt-[5px] bg-gray-8 rounded-sm break-words"},Ts={class:"h-[20px] text-gray-1 text-[12px] font-bold ml-[5px] mt-[5px] mb-[-2px]"},$s={key:0,class:"h-full overflow-auto m-1 font-mono bg-gray-8 rounded-sm"},Ds={class:"grid grid-cols-[70%_0%_0%_30%] auto-rows-[minmax(25px,auto)]"},Cs={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},ws={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Ns={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},As={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Rs={class:"col-span-2 px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Is={class:"mb-1"},Es={class:"grid grid-cols-[60%_23%_17%_0%]"},Os={class:"ml-1 text-gray-1 text-[12px] font-normal leading-[16px] mr-2"},Fs={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Ls={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},js={class:"flex justify-end"},Ss={class:"mr-[2px] text-gray-1 text-[12px] font-normal leading-[16px]"},Ps={class:"flex justify-end"},Bs={class:"ml-1 text-gray-1 text-[12px] font-normal leading-[16px]"},Ms={key:0,class:"inline-block text-red-1 mr-1"},qs={class:"w-[102px] inline-block"},Us={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Vs={class:"text-gray-1 text-[12px] font-normal leading-[16px] whitespace-pre-wrap"},Qs=t("br",null,null,-1),Hs={class:"grid auto-rows-[minmax(25px,auto)]"},Ys={class:"col-span-8 px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Xs={class:"grid grid-cols-[23%_10%_7%_3%_9%_9%_22%_17%] auto-rows-[minmax(20px,auto)] grid-row"},Gs={class:"text-gray-1 text-[12px] font-normal leading-[16px] ml-[10px] mr-[5px] grid-col"},zs={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},Ws={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},Js={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},Ks={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},Zs={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},er={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col mr-[5px]"},tr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},ar={key:0,class:"grid grid-cols-[23%_10%_7%_3%_9%_9%_17%_22%] auto-rows-[minmax(20x,auto)] grid-row"},sr={class:"text-gray-1 text-[12px] font-normal leading-[16px] ml-[10px] mr-[5px] grid-col"},rr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},or={class:"grid grid-cols-[23%_0%_0%_77%] auto-rows-[minmax(25px,auto)]"},ir={class:"col-span-2 flex flex-col justify-start px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},nr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},lr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},cr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},dr={class:"text-gray-1 text-[12px] font-normal leading-[16px] w-[395px]"},ur={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},pr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},gr=t("div",{class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},[t("div",{class:"text-gray-1 text-[12px] font-normal leading-[16px]"})],-1),fr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},yr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},hr=t("div",{class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},[t("div",{class:"text-gray-1 text-[12px] font-normal leading-[16px]"})],-1),vr={class:"grid grid-cols-[23%_52%_25%_0%] auto-rows-[minmax(25px,auto)]"},_r={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},kr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},mr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},br={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},xr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Tr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},$r={class:"grid grid-cols-[23%_27%_25%_25%] auto-rows-[minmax(25px,auto)]"},Dr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Cr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},wr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Nr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Ar={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-r-0 border-gray-6"},Rr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Ir={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-r-0 border-gray-6"},Er={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Or={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-gray-6 border-r-0 border-b-0"},Fr={class:"text-gray-1 text-[12px] font-normal leading-[16px] whitespace-pre-wrap"},Lr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},jr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Sr={class:"col-span-4 px-[6px] py-[4px] bg-gray-8 border border-b-1 border-gray-6 flex justify-end items-center"},Pr={class:"text-gray-1 text-[12px] font-normal leading-[16px] mr-[5px]"},Br={key:0,class:"col-span-4 px-[6px] py-[4px] bg-gray-8 border border-t-0 border-gray-6 flex justify-end items-center"},Mr={class:"text-gray-1 text-[12px] font-normal leading-[16px] mr-[5px]"},qr={key:1,class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] border-t-0 bg-gray-8 border border-gray-6"},Ur={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Vr={key:1,class:"mb-[15px] text-center w-full bg-gray-8"},Qr={class:"mt-[15px] text-gray-2 font-normal leading-tight text-xs"},Hr=ie({__name:"RtktDetail",props:{rtktDetail:{}},setup(r){const d=o=>{const c=[];return o.forEach(e=>{c.push(`${e.taxCode}: ${e.currencyCode} ${e.taxAmount}`)}),c.length?c.join(" / "):""},k=(o,c)=>{if(!o.length)return"";const e=o.filter(y=>!y.extendedTaxInd);return e[c]&&e[c].newOldRefundTax==="O"?Number(e[c].taxAmount)>0?`PD ${e[c].taxAmount}${e[c].taxCode}`:"PD EXEMPT":e[c]&&e[c].newOldRefundTax==="T"?Number(e[c].taxAmount)>0?`${e[c].taxCode} ${e[c].taxAmount}`:`${e[c].taxCode} EXEMPT`:""},m=o=>{if(!o.length)return"";const c=o.filter(y=>y.taxCode==="XT"&&!y.extendedTaxInd);return o.filter(y=>!y.extendedTaxInd).length===3?k(o,2):c[0]&&c[0].newOldRefundTax==="O"?Number(c[0].taxAmount)>0?`PD ${c[0].taxAmount}${c[0].taxCode}`:"PD EXEMPT":c[0]&&c[0].newOldRefundTax==="T"?Number(c[0].taxAmount)>0?`${c[0].taxCode} ${c[0].taxAmount}`:`${c[0].taxCode} EXEMPT`:""};return(o,c)=>{var e,y,h,$,D,f,b,C,w,x,S,T,E,N,B,J,W,K,Z,Y,X,O,F,v,Q,a,i,p,_,u,R,H,te,ee,se,re,de,ne,xe,Te,$e,De,Ce,we,Ne,Ae,Re,Ie,Ee,Oe,Fe,Le,je,Se,Pe,Be,Me,qe,Ue,Ve,Qe,He,Ye,Xe,Ge,ze,We,Je,Ke,Ze,et,tt,at,st,rt,ot,it,nt,lt,ct,dt,ut,pt,gt,ft,yt,ht,vt,_t,kt;return n(),l("div",xs,[t("div",Ts,s(o.$t("app.queryRtkt.originalTextOfRTKT")),1),o.rtktDetail?(n(),l("div",$s,[t("div",Ds,[t("div",Cs,[t("div",ws,s(((y=(e=o.rtktDetail)==null?void 0:e.ticket)==null?void 0:y.issueAirline)??""),1)]),t("div",Ns,[t("div",As,s((($=(h=o.rtktDetail)==null?void 0:h.ticket)==null?void 0:$.iataCode)??""),1)]),t("div",Rs,[t("div",Is,[t("div",Es,[t("div",Os,s(((f=(D=o.rtktDetail)==null?void 0:D.ticket)==null?void 0:f.ei)??""),1),t("div",Fs,s(((C=(b=o.rtktDetail)==null?void 0:b.ticket)==null?void 0:C.recordCreateDateTime)??""),1),t("div",Ls,s(((x=(w=o.rtktDetail)==null?void 0:w.ticket)==null?void 0:x.origin)??"")+s(((T=(S=o.rtktDetail)==null?void 0:S.ticket)==null?void 0:T.destination)??""),1)])]),t("div",js,[t("div",Ss,s(((N=(E=o.rtktDetail)==null?void 0:E.ticket)==null?void 0:N.code)??"")+"/"+s(((J=(B=o.rtktDetail)==null?void 0:B.ticket)==null?void 0:J.channelCode)??""),1)]),t("div",Ps,[t("div",Bs,[(K=(W=o.rtktDetail)==null?void 0:W.ticket)!=null&&K.refundVoidTag?(n(),l("div",Ms,s(((Y=(Z=o.rtktDetail)==null?void 0:Z.ticket)==null?void 0:Y.refundVoidTag)??""),1)):j("",!0),t("div",qs,s(((O=(X=o.rtktDetail)==null?void 0:X.ticket)==null?void 0:O.originalTicket)??""),1)])])]),t("div",Us,[t("div",Vs,[pe(s(((v=(F=o.rtktDetail)==null?void 0:F.ticket)==null?void 0:v.describe)??""),1),Qs,pe(s(((a=(Q=o.rtktDetail)==null?void 0:Q.ticket)==null?void 0:a.accountNumber)??"")+" "+s(((p=(i=o.rtktDetail)==null?void 0:i.ticket)==null?void 0:p.cipher)??"")+" "+s(((u=(_=o.rtktDetail)==null?void 0:_.ticket)==null?void 0:u.hourIndicator)??""),1)])])]),t("div",Hs,[t("div",Ys,[(n(!0),l(M,null,V((H=(R=o.rtktDetail)==null?void 0:R.passenger)==null?void 0:H.segments,(G,mt)=>{var bt,xt;return n(),l("div",{key:mt},[t("div",Xs,[t("div",Gs,s(G.departureCityName),1),t("div",zs,s(G.departureCity)+s(G.airline),1),t("div",Ws,s(G.flightNoWithAirline),1),t("div",Js,s(G.cabin),1),t("div",Ks,s(G.departureDate),1),t("div",Zs,s(G.departureTime),1),t("div",er,s(G.reservationStatusCode)+s(G.fareBasis),1),t("div",tr,s(G.notValidBefore)+s(G.notValidAfter)+s(G.baggage),1)]),mt+1===((xt=(bt=o.rtktDetail)==null?void 0:bt.passenger)==null?void 0:xt.segments.length)?(n(),l("div",ar,[t("div",sr,s(G.arrivalCityName),1),t("div",rr,s(G.arrivalCity),1)])):j("",!0)])}),128))])]),t("div",or,[t("div",ir,[t("div",nr,s((ee=(te=o.rtktDetail)==null?void 0:te.price)==null?void 0:ee.ticketAmountFOrRCode)+" "+s((re=(se=o.rtktDetail)==null?void 0:se.price)==null?void 0:re.ticketAmountFOrRAmount),1),t("div",lr,s((ne=(de=o.rtktDetail)==null?void 0:de.price)==null?void 0:ne.ticketAmountECode)+" "+s((Te=(xe=o.rtktDetail)==null?void 0:xe.price)==null?void 0:Te.ticketAmountE),1)]),t("div",cr,[t("div",dr,s((De=($e=o.rtktDetail)==null?void 0:$e.price)==null?void 0:De.autoFareType)+s((we=(Ce=o.rtktDetail)==null?void 0:Ce.price)==null?void 0:we.fc),1)]),t("div",ur,[t("div",pr,s(k(((Ae=(Ne=o.rtktDetail)==null?void 0:Ne.price)==null?void 0:Ae.taxes)??[],0)),1)]),gr,t("div",fr,[t("div",yr,s(k(((Ie=(Re=o.rtktDetail)==null?void 0:Re.price)==null?void 0:Ie.taxes)??[],1)),1)]),hr]),t("div",vr,[t("div",_r,[t("div",kr,s(m(((Oe=(Ee=o.rtktDetail)==null?void 0:Ee.price)==null?void 0:Oe.taxes)??[])),1)]),t("div",mr,[t("div",br,s((Le=(Fe=o.rtktDetail)==null?void 0:Fe.price)==null?void 0:Le.formOfPaymentText),1)]),t("div",xr,[t("div",Tr,s((Se=(je=o.rtktDetail)==null?void 0:je.price)==null?void 0:Se.tc),1)])]),t("div",$r,[t("div",Dr,[t("div",Cr,s(((Be=(Pe=o.rtktDetail)==null?void 0:Pe.price)==null?void 0:Be.fareAmount)===""||((qe=(Me=o.rtktDetail)==null?void 0:Me.price)==null?void 0:qe.fareAmount)==="NO ADC"?"":(Ve=(Ue=o.rtktDetail)==null?void 0:Ue.price)==null?void 0:Ve.currency)+" "+s((He=(Qe=o.rtktDetail)==null?void 0:Qe.price)==null?void 0:He.fareAmount),1)]),t("div",wr,[t("div",Nr,s((Xe=(Ye=o.rtktDetail)==null?void 0:Ye.ticket)==null?void 0:Xe.originalTicketInfo.ticketNumber)+s((ze=(Ge=o.rtktDetail)==null?void 0:Ge.ticket)==null?void 0:ze.originalTicketInfo.cityCode)+s((Je=(We=o.rtktDetail)==null?void 0:We.ticket)==null?void 0:Je.originalTicketInfo.issueDate)+" "+s((Ze=(Ke=o.rtktDetail)==null?void 0:Ke.ticket)==null?void 0:Ze.originalTicketInfo.iataNumber),1)]),t("div",Ar,[t("div",Rr,s((tt=(et=o.rtktDetail)==null?void 0:et.ticket)==null?void 0:tt.ticketNumber),1)]),t("div",Ir,[t("div",Er,s((st=(at=o.rtktDetail)==null?void 0:at.price)==null?void 0:st.scny),1)]),t("div",Or,[t("div",Fr,s((ot=(rt=o.rtktDetail)==null?void 0:rt.price)==null?void 0:ot.commissionFareOrRate),1)]),t("div",Lr,[t("div",jr,s((nt=(it=o.rtktDetail)==null?void 0:it.price)==null?void 0:nt.newTaxTotalAmount),1)]),t("div",Sr,[t("div",Pr,s((ct=(lt=o.rtktDetail)==null?void 0:lt.ticket)==null?void 0:ct.ticketType)+" "+s((ut=(dt=o.rtktDetail)==null?void 0:dt.ticket)==null?void 0:ut.ticketManagementOrganizationCode),1)]),((gt=(pt=o.rtktDetail)==null?void 0:pt.ticket)==null?void 0:gt.refundVoidTag)==="RFND"?(n(),l("div",Br,[t("div",Mr,"RFDEV-"+s((yt=(ft=o.rtktDetail)==null?void 0:ft.ticket)==null?void 0:yt.printNumber),1)])):j("",!0),(vt=(ht=o.rtktDetail)==null?void 0:ht.price)!=null&&vt.refundTaxes.length?(n(),l("div",qr,[t("div",Ur,s(o.$t("app.queryRtkt.detailsOfTaxRefund"))+s(d(((kt=(_t=o.rtktDetail)==null?void 0:_t.price)==null?void 0:kt.refundTaxes)??[])),1)])):j("",!0)])])):(n(),l("div",Vr,[t("p",Qr,s(o.$t("app.querySearch.airCityCommon.noneData")),1)]))])}}}),Yr={key:0,class:"w-[538px] p-1.5 bg-gray-7 rounded rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},Xr={class:"text-gray-1 text-xs font-bold leading-tight mb-1"},Gr={key:0,class:"text-[12px] justify-start text-gray-1 font-normal leading-4"},zr={key:0},Wr={class:"font-mono inline-block whitespace-pre-wrap break-words max-w-full"},Jr={key:1,class:"font-mono whitespace-pre-wrap break-words max-w-full"},Kr={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},Zr={key:1,class:"w-[538px] p-1.5 bg-gray-8 rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},eo={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},to={key:0,class:"w-[520px] font-mono text-[12px] justify-start text-gray-1 font-normal leading-4"},ao={class:"whitespace-pre-wrap break-words max-w-full"},so={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},ro={key:2,class:"w-[538px] p-1.5 bg-gray-8 rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},oo={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},io={key:0,class:"w-[520px] font-mono text-[12px] justify-start text-gray-1 font-normal leading-4"},no={class:"whitespace-pre-wrap break-words max-w-full"},lo={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},co={key:3,class:"w-[538px] p-1.5 bg-gray-8 rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},uo={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},po={key:0,class:"w-[520px] font-mono text-[12px] justify-start text-gray-1 font-normal leading-4"},go={class:"whitespace-pre-wrap break-words max-w-full"},fo={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},yo=ie({__name:"TicketOriginalContent",props:{ticketOriginalItem:{}},emits:["clickOriginalTicketNo"],setup(r,{emit:d}){const k=d,m=c=>{const e=c.match(ve);return be((e==null?void 0:e[1])??"")??""},o=c=>{k("clickOriginalTicketNo",c)};return(c,e)=>{var y,h,$,D;return n(),l("div",null,[(y=c.ticketOriginalItem.checkedTabList)!=null&&y.includes("D")?(n(),l("div",Yr,[t("div",Xr,s(c.$t("app.ticketOriginal.originalTextTitle")),1),c.ticketOriginalItem.originalTextList.length>1?(n(),l("div",Gr,[(n(!0),l(M,null,V(c.ticketOriginalItem.originalTextList,(f,b)=>(n(),l(M,{key:b},[g(ve).test(f)?(n(),l("span",zr,[pe(" EXCH:   "),t("span",{class:"text-brand-2 font-bold cursor-pointer mr-5 underline",onClick:e[0]||(e[0]=C=>o(c.ticketOriginalItem))},s(m(f)),1),t("pre",Wr,s(f.replace(g(ve),"")),1)])):(n(),l("pre",Jr,s(f),1))],64))),128))])):(n(),l("div",Kr,s(c.$t("app.ticketOriginal.noData")),1))])):j("",!0),(h=c.ticketOriginalItem.checkedTabList)!=null&&h.includes("H")?(n(),l("div",Zr,[t("div",eo,s(c.$t("app.ticketOriginal.historyTitle")),1),c.ticketOriginalItem.historyText?(n(),l("div",to,[t("pre",ao,s(c.ticketOriginalItem.historyText),1)])):(n(),l("div",so,s(c.$t("app.ticketOriginal.noData")),1))])):j("",!0),($=c.ticketOriginalItem.checkedTabList)!=null&&$.includes("F")?(n(),l("div",ro,[t("div",oo,s(c.$t("app.ticketOriginal.certificateTitle")),1),c.ticketOriginalItem.certificateText?(n(),l("div",io,[t("pre",no,s(c.ticketOriginalItem.certificateText),1)])):(n(),l("div",lo,s(c.$t("app.ticketOriginal.noData")),1))])):j("",!0),(D=c.ticketOriginalItem.checkedTabList)!=null&&D.includes("X")?(n(),l("div",co,[t("div",uo,s(c.$t("app.ticketOriginal.taxDetailTitle")),1),c.ticketOriginalItem.taxTextList.length>0?(n(),l("div",po,[(n(!0),l(M,null,V(c.ticketOriginalItem.taxTextList,(f,b)=>(n(),l("div",{key:b},[t("pre",go,s(f),1)]))),128))])):(n(),l("div",fo,s(c.$t("app.ticketOriginal.noData")),1))])):j("",!0)])}}}),ho=r=>{const d=P(!1),k=P(!1),m=P(!1),o=P(!1),c=P(""),e=P([]),y=async a=>{var i;try{d.value=!0;const p=D("D"),_=f(a,"D"),u=(i=(await _e(_,p)).data.value)==null?void 0:i.data;e.value[a].checkedTabList.includes("D")||e.value[a].checkedTabList.push("D"),e.value[a].isShowXsFsnTab=(u==null?void 0:u.international)??!1,e.value[a].secondFactor=(u==null?void 0:u.secondFactor)??{},e.value[a].originalTicketNos=(u==null?void 0:u.originalTicketNos)??[],e.value[a].conjunctionTicketNos=(u==null?void 0:u.conjunctionTicketNos)??[],e.value[a].originalTextList=fe((u==null?void 0:u.openSourceText)??"").split(`\r
`)}finally{d.value=!1}},h=async a=>{for(let i=0;i<a.originalTicketNos.length;i++){const p=e.value.findIndex(_=>_.ticketNo===a.originalTicketNos[i]);p===-1?(await e.value.push(F(a.originalTicketNos[i],a.secondFactor)),await y(e.value.length-1)):e.value.splice(p,1)}},$=async()=>{m.value||e.value[0].originalTextList.length!==0||await Q(0),m.value=!m.value},D=a=>{switch(a){case"D":return le("091M0102");case"H":return le("091M0103");case"F":return le("091M0104");case"X":return le("091M0105");case"R":return le("091M0106");case"N":return le("091M0107");default:return""}},f=(a,i)=>({detrType:i,ticketNo:e.value[a].ticketNo,intl:Ht()==="en",secondFactor:e.value[a].secondFactor}),b=a=>{if(!a)return[];const i=[],p=(a??"").split(`
`);i.push(p[0]);let _="",u=0;for(const R of p[1].trim())R==="|"&&u++,_=`${_}${R}`,u===5&&(i.push(_),_="",u=0);return _&&i.push(_),i},C=async(a,i)=>{var R,H;const p=D(i),_=f(a,i),u=(R=(await _e(_,p)).data.value)==null?void 0:R.data;switch(i){case"D":e.value[a].isShowXsFsnTab=(u==null?void 0:u.international)??!1,e.value[a].secondFactor=(u==null?void 0:u.secondFactor)??{},e.value[a].originalTicketNos=(u==null?void 0:u.originalTicketNos)??[],e.value[a].conjunctionTicketNos=(u==null?void 0:u.conjunctionTicketNos)??[],e.value[a].originalTextList=fe((u==null?void 0:u.openSourceText)??"").split(`\r
`);break;case"F":e.value[a].certificateText=fe(((H=u==null?void 0:u.credential)==null?void 0:H.certificatesText)??"");break;case"H":e.value[a].historyText=(u==null?void 0:u.tktHistoryText)??"";break;case"X":e.value[a].taxTextList=b((u==null?void 0:u.ticketFareInfoText)??"");break}},w=a=>a.gpSign?a.gpSign.trim():a.negotiatedFareCode,x=a=>((a==null?void 0:a.filter(p=>p.flightNo))??[]).map(p=>{var _,u,R;return(_=p.fareBasis)!=null&&_.includes("/")?(R=p.fareBasis)==null?void 0:R.slice(0,(u=p.fareBasis)==null?void 0:u.indexOf("/")):p.fareBasis}).join("/"),S=(a,i)=>{const p=i==null?void 0:i.filter(_=>_.flightNo);e.value[a].fsnTicketNos=(p??[]).map(_=>_.ticketNo)},T=a=>{const i=a.replace(/\s+/g,"");return i&&i!=="null"?a.replace(/\s+/g,"T"):""},E=a=>((a==null?void 0:a.filter(p=>p.flightNo))??[]).map(p=>{var _,u,R,H;return[{openFlag:((_=p.flightNo)==null?void 0:_.includes("OPEN"))||((u=p.flightNo)==null?void 0:u.length)<=2,companyCode:((R=p.flightNo)==null?void 0:R.slice(0,2))??"",flightNumber:((H=p.flightNo)==null?void 0:H.slice(2))??"",cabinCode:p.cabin??"",departureDateTime:T(p.departureDateTime??""),departureAirport:p.departureCity??"",arrivalDateTime:T(p.arrivalDateTime??""),arrivalAirport:p.arrivalCity??"",flightType:"",stopQuantity:"",stopFlag:""}]}),N=(a,i)=>{var H,te,ee,se,re,de;S(a,i.passenger.conjunctionSegments);const p=[i.passenger.passengerType??""],{selectedPassengers:_,groupPassengers:u}=_a(p);return e.value[a].fsnIssueDate=`${((H=i.ticket)==null?void 0:H.issueDate)??""}${((te=i.ticket)==null?void 0:te.issueTime)??""}`,{creditCard:w(i.price)==="GP",fareType:"",farebasic:x(i.passenger.conjunctionSegments),currencyCode:((ee=i.price.fareAmount)==null?void 0:ee.slice(0,3))??"",selectedPassengers:_,company:i.ticket.issueAirline,farePricingSale:!0,negotiatedFareCode:w(i.price),queryExclusiveNegotiatedFare:i.price.queryExclusiveNegotiatedFare??!1,allb:"0",pricingSource:"Both",reprice:"0",voyages:E(i.passenger.conjunctionSegments),brand:"",baggage:"",altTicketingDateTime:`${((se=i.ticket)==null?void 0:se.issueDate.slice(0,-3))??""}${((re=i.ticket)==null?void 0:re.issueTime)??""}`,airportCode:"",groupPassengerType:u.filter(ne=>ne).map(ne=>ne.passengerType).join(",")??"",priceVerification:{ticketAmount:i.price.fsnAmount??"",taxAmount:i.price.totalTaxAmount??"",totalAmount:((de=i.price.fareAmount)==null?void 0:de.slice(3))??""}}},B=async(a,i)=>{var _,u;const p=((_=(await aa(e.value[i].ticketNo,a)).data.value)==null?void 0:_.data)??{};return e.value[i].isAutoFare=((u=p.price)==null?void 0:u.autoFareType)??!1,JSON.stringify(p)!=="{}"?N(i,p):{}},J=async(a,i)=>{const p=D(i);await B(p,a).then(async _=>{var u,R;return JSON.stringify(_)!=="{}"&&e.value[a].isAutoFare?((R=(u=await na(_,p))==null?void 0:u.data)==null?void 0:R.value)??{}:{}}).then(_=>{var u,R;if(_){let H=[];const te=(u=_.internationalPriceComputeResponse)==null?void 0:u.fareItems;Object.keys(te).forEach(ee=>{if(te[ee].length){const se=te[ee].map(re=>({...re,passengerType:ee}));H=H.concat(se)}}),e.value[a].tktRulesData.fareData=(R=H.map(ee=>({...ee,componentCode:"tktRule"}))??[])==null?void 0:R[0],e.value[a].tktRulesData.fareData.ticketRegulation=_.rulesResponse,e.value[a].tktRulesData.rulesResponse=_.rulesResponse}}).catch(_=>{throw _})},W=async(a,i)=>{var R;const p=D(i),_=e.value[a].ticketNo,u=await ta(_,p);e.value[a].rtktDetail=(R=u.data.value)==null?void 0:R.data},K=async(a,i)=>{try{d.value=!0,i==="R"?await W(a,i):i==="N"?await J(a,i):await C(a,i)}finally{d.value=!1}},Z=(a,i)=>{var p;(p=e.value[a].checkedTabList)!=null&&p.includes(i)?e.value[a].checkedTabList=e.value[a].checkedTabList.filter(_=>_!==i):(e.value[a].checkedTabList.push(i),K(a,i))},Y=async a=>{if(e.value.forEach(i=>i.checkedTabList=["D"]),e.value[a].originalTextList.length===0&&await v(a),e.value[a].originalTicketNos.length>0)for(let i=0;i<e.value[a].originalTicketNos.length;i++){const p=e.value.every(u=>u.ticketNo!==e.value[a].originalTicketNos[i]||u.originalTextList.length===0);e.value.every(u=>u.ticketNo!==e.value[a].originalTicketNos[i])&&await e.value.push(F(e.value[a].originalTicketNos[i],e.value[a].secondFactor)),p&&await Y(e.value.length-1)}},X=async a=>{var i;try{if(d.value=!0,e.value.forEach(p=>p.checkedTabList=["R"]),Object.keys(((i=e.value[a])==null?void 0:i.rtktDetail)??{}).length===0&&await W(a,"R"),e.value[a].originalTicketNos.length>0)for(let p=0;p<e.value[a].originalTicketNos.length;p++){const _=e.value.every(R=>R.ticketNo!==e.value[a].originalTicketNos[p]||Object.keys((R==null?void 0:R.rtktDetail)??{}).length===0);e.value.every(R=>R.ticketNo!==e.value[a].originalTicketNos[p])&&await e.value.push(F(e.value[a].originalTicketNos[p],e.value[a].secondFactor)),_&&await X(e.value.length-1)}}finally{d.value=!1}},O=async a=>{k.value=!k.value,c.value=a;for(let i=0;i<e.value.length;i++)a==="D"?await Y(i):await X(i)},F=(a,i)=>({ticketNo:a,originalTicketNos:[],conjunctionTicketNos:[],secondFactor:i,originalTextList:[],historyText:"",certificateText:"",taxTextList:[],rtktDetail:{},isShowXsFsnTab:!1,checkedTabList:[],isAutoFare:!1,tktRulesData:{},fsnIssueDate:"",fsnTicketNos:[]}),v=async a=>{var i;try{d.value=!0;const p=D("D"),_=f(a,"D"),u=(i=(await _e(_,p)).data.value)==null?void 0:i.data;a===0&&((u==null?void 0:u.originalTicketNos)??[]).length>0&&(o.value=!0),e.value[a].checkedTabList.includes("D")||e.value[a].checkedTabList.push("D"),e.value[a].isShowXsFsnTab=(u==null?void 0:u.international)??!1,e.value[a].secondFactor=(u==null?void 0:u.secondFactor)??{},e.value[a].originalTicketNos=(u==null?void 0:u.originalTicketNos)??[],e.value[a].conjunctionTicketNos=(u==null?void 0:u.conjunctionTicketNos)??[],e.value[a].originalTextList=fe((u==null?void 0:u.openSourceText)??"").split(`\r
`)}finally{d.value=!1}},Q=async a=>{await v(a);const i=e.value[a].conjunctionTicketNos.filter(p=>p!==e.value[a].ticketNo);if(i.length>0)for(let p=0;p<i.length;p++)await e.value.push(F(i[p],e.value[a].secondFactor)),await v(p+1)};return ge(async()=>{if(e.value){const a=r.ticketInfo.ticketNo,i=a[3]!=="-"?`${a.slice(0,3)}-${a.slice(3)}`:a;await e.value.push(F(i,r.ticketInfo.secondFactor))}r.isQuery&&(m.value=!0,Q(0))}),{showLoading:d,isExpandList:k,isExpandTicket:m,isShowTicketContrast:o,contrastType:c,ticketOriginalList:e,handleClickTab:Z,handleExpandTicket:$,handleTicketContrast:O,clickOriginalTicketNo:h}},vo=ho,_o={key:0,class:"w-[550px] h-8 px-2.5 py-1.5 bg-brand-4 rounded-sm border border-brand-3 justify-between items-center inline-flex"},ko={class:"justify-start items-center flex"},mo={class:"text-gray-1 text-xs font-bold leading-tight mr-2.5"},bo={key:0,class:"text-brand-2 text-xs font-normal leading-tight mr-1"},xo={key:1,class:"relative"},To={class:"absolute right-[-30px] top-[12px] mt-2 w-[80px] bg-white rounded shadow-lg py-1 z-50 border border-gray-200"},$o={class:"flex items-center"},Do={key:0,class:"w-[17px] text-brand-2"},Co={key:1,class:"w-[15px]"},wo={class:"flex items-center"},No={key:0,class:"w-[17px] text-brand-2"},Ao={key:1,class:"w-[15px]"},Ro={key:0},Io={key:1},Eo={key:1,class:"mx-1.5"},Oo={class:"w-[530px] text-gray-1 text-xs font-bold leading-tight"},Fo={key:2,class:"w-[550px] h-full p-1.5 flex-col justify-start items-start inline-flex bg-gray-8 rounded-b-sm"},Lo={class:"text-xs h-6 justify-start items-start inline-flex cursor-pointer"},jo=["onClick"],So=["onClick"],Po=["onClick"],Bo=["onClick"],Mo=["onClick"],qo=["onClick"],Uo={class:"w-full"},Vo={key:1,class:"w-[538px] p-1.5 bg-gray-8 rounded rounded rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},Qo={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},Ho={key:0,class:"max-w-[520px]"},Yo={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},Xo=ie({__name:"TicketOriginalItem",props:{ticketInfo:{},isQuery:{type:Boolean}},setup(r){const d=r,{showLoading:k,isExpandList:m,isExpandTicket:o,isShowTicketContrast:c,contrastType:e,ticketOriginalList:y,handleClickTab:h,handleExpandTicket:$,handleTicketContrast:D,clickOriginalTicketNo:f}=vo(d);return(b,C)=>{const w=Xt,x=ia,S=wt;return ue((n(),l("div",null,[(n(!0),l(M,null,V(g(y),(T,E)=>{var N,B,J,W,K,Z,Y,X,O;return n(),l("div",{key:E+T.ticketNo,class:"w-[550px] bg-gray-8"},[E===0?(n(),l("div",_o,[t("div",ko,[t("div",mo,s(b.ticketInfo.ticketNo),1),g(c)?(n(),l("div",bo,s(b.$t("app.ticketOriginal.ticketContrast")),1)):j("",!0),g(c)?(n(),l("span",xo,[t("span",{class:"text-[16px] text-brand-2 cursor-pointer",onClick:C[0]||(C[0]=F=>m.value=!g(m))},[I(w,null,{default:L(()=>[I(g($t))]),_:1})]),ue(t("div",To,[t("div",{class:"px-4 py-2 hover:bg-blue-50 cursor-pointer text-xs",onClick:C[1]||(C[1]=F=>g(D)("D"))},[t("div",$o,[g(e)==="D"?(n(),l("div",Do,[I(w,null,{default:L(()=>[I(g(Dt))]),_:1})])):(n(),l("div",Co)),t("span",{class:z({"text-brand-2 font-medium":g(e)==="D"})},s(b.$t("app.ticketOriginal.originalText")),3)])]),t("div",{class:"px-4 py-2 hover:bg-blue-50 cursor-pointer text-xs",onClick:C[2]||(C[2]=F=>g(D)("R"))},[t("div",wo,[g(e)==="R"?(n(),l("div",No,[I(w,null,{default:L(()=>[I(g(Dt))]),_:1})])):(n(),l("div",Ao)),t("span",{class:z({"text-brand-2 font-medium":g(e)==="R"})},s(b.$t("app.ticketOriginal.rtkt")),3)])])],512),[[ke,g(m)]])])):j("",!0)]),t("div",{class:"text-[20px] text-brand-2 cursor-pointer",onClick:C[3]||(C[3]=F=>g($)())},[g(o)?(n(),l("span",Ro,[I(w,null,{default:L(()=>[I(g(Yt))]),_:1})])):(n(),l("span",Io,[I(w,null,{default:L(()=>[I(g($t))]),_:1})]))])])):j("",!0),E!==0&&g(o)?(n(),l("div",Eo,[I(x,{"border-style":"dashed"}),t("div",Oo,s(T.ticketNo),1)])):j("",!0),g(o)?(n(),l("div",Fo,[t("div",Lo,[t("div",{class:z(["w-11 px-2 py-0.5 rounded-tl-sm border",[(N=T.checkedTabList)!=null&&N.includes("D")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:F=>g(h)(E,"D")},s(b.$t("app.ticketOriginal.originalText")),11,jo),t("div",{class:z(["w-[99px] px-2 py-0.5 border",[(B=T.checkedTabList)!=null&&B.includes("H")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:F=>g(h)(E,"H")},s(b.$t("app.ticketOriginal.history")),11,So),t("div",{class:z(["w-[132px] px-2 py-0.5 border",[(J=T.checkedTabList)!=null&&J.includes("F")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:F=>g(h)(E,"F")},s(b.$t("app.ticketOriginal.certificate")),11,Po),t("div",{class:z(["w-[109px] px-2 py-0.5 border",[(W=T.checkedTabList)!=null&&W.includes("X")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:F=>g(h)(E,"X")},s(b.$t("app.ticketOriginal.taxDetail")),11,Bo),t("div",{class:z(["w-[50px] px-2 py-0.5 rounded-tl-sm rounded-sm border",[(K=T.checkedTabList)!=null&&K.includes("R")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:F=>g(h)(E,"R")},s(b.$t("app.ticketOriginal.rtkt")),11,Mo),T.isShowXsFsnTab?(n(),l("div",{key:0,class:z(["w-[97px] px-2 py-0.5 rounded-tl-sm rounded-sm border",[T.checkedTabList.includes("N")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:F=>g(h)(E,"N")},s(b.$t("app.ticketOriginal.ticketRule")),11,qo)):j("",!0)]),t("div",Uo,[I(yo,{"ticket-original-item":T,onClickOriginalTicketNo:g(f)},null,8,["ticket-original-item","onClickOriginalTicketNo"]),(Z=T.checkedTabList)!=null&&Z.includes("R")?(n(),ce(Hr,{key:0,"rtkt-detail":T.rtktDetail},null,8,["rtkt-detail"])):j("",!0),(Y=T.checkedTabList)!=null&&Y.includes("N")?(n(),l("div",Vo,[t("div",Qo,s(b.$t("app.ticketOriginal.ticketRuleTitle")),1),JSON.stringify(T.tktRulesData)!=="{}"&&T.isAutoFare?(n(),l("div",Ho,[I(bs,{international:"","rule-info-data":(X=T.tktRulesData)==null?void 0:X.rulesResponse,"detail-info":(O=T.tktRulesData)==null?void 0:O.fareData,fsn:"","need-close-icon":"","issue-date":T.fsnIssueDate,"fsn-ticket-nos":T.fsnTicketNos,"is-show-window":!1},null,8,["rule-info-data","detail-info","issue-date","fsn-ticket-nos"])])):(n(),l("div",Yo,s(b.$t("app.ticketOriginal.noFareTip")),1))])):j("",!0)])])):j("",!0)])}),128))])),[[S,g(k)]])}}});const wi=he(Xo,[["__scopeId","data-v-8a1ccb75"]]);export{bs as T,yo as _,Di as a,$i as b,ni as c,pi as d,ki as e,xi as f,Ci as g,_a as h,di as i,ci as j,na as k,Hr as l,Ti as m,yi as n,_i as o,fi as p,gi as q,li as r,vi as s,ui as t,hi as u,ga as v,bi as w,mi as x,wi as y};
