import{e as K,h as Q,iM as x,iv as J,iN as M,iO as D,i as S,iP as G,iy as $,iF as E,iz as X,a as B,gF as Y,gG as Z,iQ as W,iu as m,iC as b}from"./index-9381ab2b.js";function z(n,e){for(var f=-1,a=n==null?0:n.length;++f<a;)if(e(n[f],f,n))return!0;return!1}var V=1,c=2;function q(n,e,f,a,i,r){var s=f&V,g=n.length,l=e.length;if(g!=l&&!(s&&l>g))return!1;var v=r.get(n),d=r.get(e);if(v&&d)return v==e&&d==n;var A=-1,u=!0,O=f&c?new K:void 0;for(r.set(n,e),r.set(e,n);++A<g;){var t=n[A],T=e[A];if(a)var P=s?a(T,t,A,e,n,r):a(t,T,A,n,e,r);if(P!==void 0){if(P)continue;u=!1;break}if(O){if(!z(e,function(w,L){if(!Q(O,L)&&(t===w||i(t,w,f,a,r)))return O.push(L)})){u=!1;break}}else if(!(t===T||i(t,T,f,a,r))){u=!1;break}}return r.delete(n),r.delete(e),u}function h(n){var e=-1,f=Array(n.size);return n.forEach(function(a,i){f[++e]=[i,a]}),f}function j(n){var e=-1,f=Array(n.size);return n.forEach(function(a){f[++e]=a}),f}var o=1,k=2,nn="[object Boolean]",en="[object Date]",rn="[object Error]",an="[object Map]",fn="[object Number]",sn="[object RegExp]",un="[object Set]",ln="[object String]",gn="[object Symbol]",vn="[object ArrayBuffer]",An="[object DataView]",N=x?x.prototype:void 0,R=N?N.valueOf:void 0;function tn(n,e,f,a,i,r,s){switch(f){case An:if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case vn:return!(n.byteLength!=e.byteLength||!r(new M(n),new M(e)));case nn:case en:case fn:return J(+n,+e);case rn:return n.name==e.name&&n.message==e.message;case sn:case ln:return n==e+"";case an:var g=h;case un:var l=a&o;if(g||(g=j),n.size!=e.size&&!l)return!1;var v=s.get(n);if(v)return v==e;a|=k,s.set(n,e);var d=q(g(n),g(e),a,i,r,s);return s.delete(n),d;case gn:if(R)return R.call(n)==R.call(e)}return!1}var Tn=1,dn=Object.prototype,On=dn.hasOwnProperty;function Pn(n,e,f,a,i,r){var s=f&Tn,g=D(n),l=g.length,v=D(e),d=v.length;if(l!=d&&!s)return!1;for(var A=l;A--;){var u=g[A];if(!(s?u in e:On.call(e,u)))return!1}var O=r.get(n),t=r.get(e);if(O&&t)return O==e&&t==n;var T=!0;r.set(n,e),r.set(e,n);for(var P=s;++A<l;){u=g[A];var w=n[u],L=e[u];if(a)var I=s?a(L,w,u,e,n,r):a(w,L,u,n,e,r);if(!(I===void 0?w===L||i(w,L,f,a,r):I)){T=!1;break}P||(P=u=="constructor")}if(T&&!P){var _=n.constructor,p=e.constructor;_!=p&&"constructor"in n&&"constructor"in e&&!(typeof _=="function"&&_ instanceof _&&typeof p=="function"&&p instanceof p)&&(T=!1)}return r.delete(n),r.delete(e),T}var wn=1,C="[object Arguments]",F="[object Array]",y="[object Object]",Ln=Object.prototype,U=Ln.hasOwnProperty;function _n(n,e,f,a,i,r){var s=S(n),g=S(e),l=s?F:G(n),v=g?F:G(e);l=l==C?y:l,v=v==C?y:v;var d=l==y,A=v==y,u=l==v;if(u&&$(n)){if(!$(e))return!1;s=!0,d=!1}if(u&&!d)return r||(r=new E),s||X(n)?q(n,e,f,a,i,r):tn(n,e,l,f,a,i,r);if(!(f&wn)){var O=d&&U.call(n,"__wrapped__"),t=A&&U.call(e,"__wrapped__");if(O||t){var T=O?n.value():n,P=t?e.value():e;return r||(r=new E),i(T,P,f,a,r)}}return u?(r||(r=new E),Pn(n,e,f,a,i,r)):!1}function H(n,e,f,a,i){return n===e?!0:n==null||e==null||!B(n)&&!B(e)?n!==n&&e!==e:_n(n,e,f,a,H,i)}function pn(n,e){return n!=null&&e in Object(n)}function yn(n,e,f){e=Y(e,n);for(var a=-1,i=e.length,r=!1;++a<i;){var s=Z(e[a]);if(!(r=n!=null&&f(n,s)))break;n=n[s]}return r||++a!=i?r:(i=n==null?0:n.length,!!i&&W(i)&&m(s,i)&&(S(n)||b(n)))}function Rn(n,e){return n!=null&&yn(n,e,pn)}function Sn(n,e){return H(n,e)}export{H as b,Rn as h,Sn as i,j as s};
