import{iF as O,g0 as P,e_ as _,iH as E,gG as m,ec as L,dZ as v,d_ as F,i as I,eb as x,hz as D}from"./index-9381ab2b.js";import{b as C,h as H}from"./isEqual-a619023a.js";var w=1,G=2;function T(n,e,t,r){var s=t.length,o=s,u=!r;if(n==null)return!o;for(n=Object(n);s--;){var i=t[s];if(u&&i[2]?i[1]!==n[i[0]]:!(i[0]in n))return!1}for(;++s<o;){i=t[s];var a=i[0],c=n[a],d=i[1];if(u&&i[2]){if(c===void 0&&!(a in n))return!1}else{var l=new O;if(r)var p=r(c,d,a,n,e,l);if(!(p===void 0?C(d,c,w|G,r,l):p))return!1}}return!0}function R(n){return n===n&&!P(n)}function S(n){for(var e=_(n),t=e.length;t--;){var r=e[t],s=n[r];e[t]=[r,s,R(s)]}return e}function y(n,e){return function(t){return t==null?!1:t[n]===e&&(e!==void 0||n in Object(t))}}function B(n){var e=S(n);return e.length==1&&e[0][2]?y(e[0][0],e[0][1]):function(t){return t===n||T(t,n,e)}}var U=1,K=2;function N(n,e){return E(n)&&R(e)?y(m(n),e):function(t){var r=L(t,n);return r===void 0&&r===e?H(t,n):C(e,r,U|K)}}function $(n){return function(e){return e==null?void 0:e[n]}}function q(n){return function(e){return v(e,n)}}function Z(n){return E(n)?$(m(n)):q(n)}function W(n){return typeof n=="function"?n:n==null?F:typeof n=="object"?I(n)?N(n[0],n[1]):B(n):Z(n)}const f=new Map;let g;x&&(document.addEventListener("mousedown",n=>g=n),document.addEventListener("mouseup",n=>{for(const e of f.values())for(const{documentHandler:t}of e)t(n,g)}));function A(n,e){let t=[];return Array.isArray(e.arg)?t=e.arg:D(e.arg)&&t.push(e.arg),function(r,s){const o=e.instance.popperRef,u=r.target,i=s==null?void 0:s.target,a=!e||!e.instance,c=!u||!i,d=n.contains(u)||n.contains(i),l=n===u,p=t.length&&t.some(h=>h==null?void 0:h.contains(u))||t.length&&t.includes(i),M=o&&(o.contains(u)||o.contains(i));a||c||d||l||p||M||e.value(r,s)}}const X={beforeMount(n,e){f.has(n)||f.set(n,[]),f.get(n).push({documentHandler:A(n,e),bindingFn:e.value})},updated(n,e){f.has(n)||f.set(n,[]);const t=f.get(n),r=t.findIndex(o=>o.bindingFn===e.oldValue),s={documentHandler:A(n,e),bindingFn:e.value};r>=0?t.splice(r,1,s):t.push(s)},unmounted(n){f.delete(n)}};export{X as C,W as b};
