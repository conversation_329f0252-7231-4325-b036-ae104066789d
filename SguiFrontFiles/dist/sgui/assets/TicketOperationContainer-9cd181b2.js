import{L as ds,M as ga,eC as ps,ep as vn,dP as Vn,eD as Ln,eq as Ca,dI as fs,q as Le,eE as ms,eF as gs,eG as ks,v as ys,r as I,w as je,d4 as vs,eH as hs,o as mt,x as s,y as re,z as c,P as t,D as Pe,A as e,C as ka,G as r,B as g,H as nt,dL as _s,ai as _e,aj as Re,F as Yt,ak as J,Q as n,ah as vt,X as bs,b1 as fn,E as jn,dD as xs,_ as Ts,U as Mn,m as Ns,K as $s,ab as Ze,a9 as Rt,b0 as it,bt as Tn,b3 as Me,aV as Yn,J as ee,al as et,ax as Nn,bg as gn,eI as ya,ac as Ct,bs as Mt,eJ as va,au as we,b5 as Ft,ae as $n,a5 as Ue,am as wt,an as St,s as De,aT as yt,at as Je,aH as Ce,ev as wa,aY as lt,b6 as Jt,aG as cn,eK as Kn,ex as Rs,ad as yn,aF as Sa,eL as Nt,aZ as ht,eM as Gt,eN as Bn,a6 as kn,R as ot,ar as Cs,eO as ws,c5 as Pa,c3 as Wn,eP as Xt,eQ as Ss,bu as hn,eR as Xn,ew as on,eS as Ps,eT as As,da as Ds,eU as Es,aX as Aa,cL as Os,aC as Fs,aD as Vs,aS as Da,eV as Ms,eW as ha,eX as In,a8 as Ls,c2 as js,bO as Bs,eY as Is,av as Us,eZ as Qs,aI as qs,b9 as zs}from"./index-9381ab2b.js";import{P as Kt}from"./PrintNoSelect-c0b0bc1c.js";import{D as Ht,R as Gs,P as Jn,b as gt,c as Ea,d as Oa,e as Hs,f as _a,g as _n,h as Zn,i as ea,j as Fa,k as Ys,l as ba,m as Ks,n as Ws,o as Xs,p as Va,q as Js,r as Zs,s as eo,t as to,S as no,u as ao,E as so}from"./regular-crs-0d781ceb.js";import{E as Dt,a as Et}from"./index-b4196982.js";import{E as Zt,a as en}from"./index-094198d8.js";import{E as ct,a as ut}from"./index-951011fc.js";import{u as oo,E as _t}from"./index-c19c3f80.js";import{E as Rn}from"./index-e22833ad.js";import{E as rt}from"./index-7b8ec8cc.js";import{C as io,S as mn,P as xa,m as lo,E as Ma,r as ro,a as co}from"./jspdf.es.min-0a3c1a09.js";import{a as La,b as uo,c as po,d as fo,e as Ta,g as mo,q as ln,h as go,r as ko,i as yo,j as ja,k as Ba,l as Un,m as Qn,s as vo,n as ho,o as _o,p as bo,t as xo,u as To,v as No,w as Na,x as $o,y as Ro,z as Co,f as wo,A as So}from"./ticketOperationApi-8106707a.js";import{E as Cn}from"./index-9683911d.js";import{_ as dt}from"./_plugin-vue_export-helper-c27b6911.js";import{u as Ia,_ as un}from"./TicketOriginalPopover.vue_vue_type_script_setup_true_lang-bce4521f.js";import{s as ta}from"./index-1e574ce4.js";import{o as qn,a as Po,b as Ao,p as bn,q as Ua,c as Do,d as Eo,e as Oo,m as Fo,f as Qa,x as Vo,g as Mo,_ as Lo,R as jo,h as Bo}from"./RtktWindoing.vue_vue_type_script_setup_true_lang-98e71f4c.js";import{D as qa,a as na,p as wn,h as za,f as zn,g as Ga,b as Gn}from"./refundUtil-234919d2.js";import{a as Qe,D as dn}from"./decimal-56a2735b.js";import{d as Lt,m as jt,f as rn,T as aa,R as Io,P as Uo}from"./TicketRefundForm-c10fc473.js";import{s as At}from"./subtract-e226dc5c.js";import{E as tn,a as sa}from"./index-a7943392.js";import{E as Ha,a as Ya}from"./index-57a4abc9.js";import{C as Ka,E as Bt,k as Qo}from"./config-b573cde3.js";import{g as $t,a as $a}from"./pnrUtils-8a2ea82c.js";import{E as oa}from"./index-847d31f7.js";import{E as ia}from"./index-34c19038.js";import{g as qo}from"./index-c5f744ff.js";import{u as la}from"./usePersonalization-2a6caa7c.js";import{_ as ra}from"./theme-light_empty-0081a108.js";import{E as zo}from"./index-1d08351c.js";import{E as Go}from"./exceljs.min-5aff0268.js";import{E as Ho}from"./index-8850371a.js";import"./index-1dbceb27.js";import"./strings-820bea19.js";import"./isEqual-a619023a.js";import"./index-a4ffe93f.js";import"./castArray-25c7c99e.js";import"./isUndefined-aa0326a0.js";import"./refs-d6b4edba.js";import"./browser-6cfa1fde.js";import"./dropdown-67e5d658.js";import"./TicketOriginalItem-013d7973.js";import"./time-c3069dc1.js";import"./_createMathOperation-15113527.js";import"./throttle-9e041729.js";import"./flatten-85480810.js";import"./index-729d485f.js";import"./passengerSpecialType-e43b2056.js";import"./dictApi-2d93d62f.js";import"./index-e8380056.js";import"./index-06c3636a.js";const Yo=ds({valueKey:{type:String,default:"value"},modelValue:{type:[String,Number],default:""},debounce:{type:Number,default:300},placement:{type:ga(String),values:["top","top-start","top-end","bottom","bottom-start","bottom-end"],default:"bottom-start"},fetchSuggestions:{type:ga([Function,Array]),default:ps},popperClass:{type:String,default:""},triggerOnFocus:{type:Boolean,default:!0},selectWhenUnmatched:{type:Boolean,default:!1},hideLoading:{type:Boolean,default:!1},label:{type:String},teleported:oo.teleported,highlightFirstItem:{type:Boolean,default:!1},fitInputWidth:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},name:String}),Ko={[vn]:a=>Vn(a),[Ln]:a=>Vn(a),[Ca]:a=>Vn(a),focus:a=>a instanceof FocusEvent,blur:a=>a instanceof FocusEvent,clear:()=>!0,select:a=>fs(a)},Wo=["aria-expanded","aria-owns"],Xo={key:0},Jo=["id","aria-selected","onClick"],Wa="ElAutocomplete",Zo=Le({name:Wa,inheritAttrs:!1}),ei=Le({...Zo,props:Yo,emits:Ko,setup(a,{expose:i,emit:u}){const p=a,f=ms(),d=gs(),o=ks(),_=ys("autocomplete"),h=I(),y=I(),T=I(),b=I();let S=!1,k=!1;const $=I([]),m=I(-1),A=I(""),l=I(!1),P=I(!1),j=I(!1),X=je(()=>_.b(String(qo()))),ce=je(()=>d.style),M=je(()=>($.value.length>0||j.value)&&l.value),w=je(()=>!p.hideLoading&&j.value),W=je(()=>h.value?Array.from(h.value.$el.querySelectorAll("input")):[]),C=()=>{M.value&&(A.value=`${h.value.$el.offsetWidth}px`)},R=()=>{m.value=-1},K=vs(async B=>{if(P.value)return;const v=G=>{j.value=!1,!P.value&&(Mn(G)?($.value=G,m.value=p.highlightFirstItem?0:-1):Ns(Wa,"autocomplete suggestions must be an array"))};if(j.value=!0,Mn(p.fetchSuggestions))v(p.fetchSuggestions);else{const G=await p.fetchSuggestions(B,v);Mn(G)&&v(G)}},p.debounce),oe=B=>{const v=!!B;if(u(Ln,B),u(vn,B),P.value=!1,l.value||(l.value=v),!p.triggerOnFocus&&!B){P.value=!0,$.value=[];return}K(B)},te=B=>{var v;o.value||(((v=B.target)==null?void 0:v.tagName)!=="INPUT"||W.value.includes(document.activeElement))&&(l.value=!0)},D=B=>{u(Ca,B)},E=B=>{k?k=!1:(l.value=!0,u("focus",B),p.triggerOnFocus&&!S&&K(String(p.modelValue)))},pe=B=>{setTimeout(()=>{var v;if((v=T.value)!=null&&v.isFocusInsideContent()){k=!0;return}l.value&&de(),u("blur",B)})},ue=()=>{l.value=!1,u(vn,""),u("clear")},ge=async()=>{M.value&&m.value>=0&&m.value<$.value.length?q($.value[m.value]):p.selectWhenUnmatched&&(u("select",{value:p.modelValue}),$.value=[],m.value=-1)},se=B=>{M.value&&(B.preventDefault(),B.stopPropagation(),de())},de=()=>{l.value=!1},H=()=>{var B;(B=h.value)==null||B.focus()},Q=()=>{var B;(B=h.value)==null||B.blur()},q=async B=>{u(Ln,B[p.valueKey]),u(vn,B[p.valueKey]),u("select",B),$.value=[],m.value=-1},V=B=>{if(!M.value||j.value)return;if(B<0){m.value=-1;return}B>=$.value.length&&(B=$.value.length-1);const v=y.value.querySelector(`.${_.be("suggestion","wrap")}`),L=v.querySelectorAll(`.${_.be("suggestion","list")} li`)[B],ae=v.scrollTop,{offsetTop:ie,scrollHeight:Ne}=L;ie+Ne>ae+v.clientHeight&&(v.scrollTop+=Ne),ie<ae&&(v.scrollTop-=Ne),m.value=B,h.value.ref.setAttribute("aria-activedescendant",`${X.value}-item-${m.value}`)};return hs(b,()=>{M.value&&de()}),mt(()=>{h.value.ref.setAttribute("role","textbox"),h.value.ref.setAttribute("aria-autocomplete","list"),h.value.ref.setAttribute("aria-controls","id"),h.value.ref.setAttribute("aria-activedescendant",`${X.value}-item-${m.value}`),S=h.value.ref.hasAttribute("readonly")}),i({highlightedIndex:m,activated:l,loading:j,inputRef:h,popperRef:T,suggestions:$,handleSelect:q,handleKeyEnter:ge,focus:H,blur:Q,close:de,highlight:V}),(B,v)=>(s(),re(e(_t),{ref_key:"popperRef",ref:T,visible:e(M),placement:B.placement,"fallback-placements":["bottom-start","top-start"],"popper-class":[e(_).e("popper"),B.popperClass],teleported:B.teleported,"gpu-acceleration":!1,pure:"","manual-mode":"",effect:"light",trigger:"click",transition:`${e(_).namespace.value}-zoom-in-top`,persistent:"",role:"listbox",onBeforeShow:C,onHide:R},{content:c(()=>[t("div",{ref_key:"regionRef",ref:y,class:Pe([e(_).b("suggestion"),e(_).is("loading",e(w))]),style:ka({[B.fitInputWidth?"width":"minWidth"]:A.value,outline:"none"}),role:"region"},[r(e(ia),{id:e(X),tag:"ul","wrap-class":e(_).be("suggestion","wrap"),"view-class":e(_).be("suggestion","list"),role:"listbox"},{default:c(()=>[e(w)?(s(),g("li",Xo,[r(e(nt),{class:Pe(e(_).is("loading"))},{default:c(()=>[r(e(_s))]),_:1},8,["class"])])):(s(!0),g(_e,{key:1},Re($.value,(G,L)=>(s(),g("li",{id:`${e(X)}-item-${L}`,key:L,class:Pe({highlighted:m.value===L}),role:"option","aria-selected":m.value===L,onClick:ae=>q(G)},[Yt(B.$slots,"default",{item:G},()=>[J(n(G[B.valueKey]),1)])],10,Jo))),128))]),_:3},8,["id","wrap-class","view-class"])],6)]),default:c(()=>[t("div",{ref_key:"listboxRef",ref:b,class:Pe([e(_).b(),B.$attrs.class]),style:ka(e(ce)),role:"combobox","aria-haspopup":"listbox","aria-expanded":e(M),"aria-owns":e(X)},[r(e(vt),bs({ref_key:"inputRef",ref:h},e(f),{clearable:B.clearable,disabled:e(o),name:B.name,"model-value":B.modelValue,onInput:oe,onChange:D,onFocus:E,onBlur:pe,onClear:ue,onKeydown:[v[0]||(v[0]=fn(jn(G=>V(m.value-1),["prevent"]),["up"])),v[1]||(v[1]=fn(jn(G=>V(m.value+1),["prevent"]),["down"])),fn(ge,["enter"]),fn(de,["tab"]),fn(se,["esc"])],onMousedown:te}),xs({_:2},[B.$slots.prepend?{name:"prepend",fn:c(()=>[Yt(B.$slots,"prepend")])}:void 0,B.$slots.append?{name:"append",fn:c(()=>[Yt(B.$slots,"append")])}:void 0,B.$slots.prefix?{name:"prefix",fn:c(()=>[Yt(B.$slots,"prefix")])}:void 0,B.$slots.suffix?{name:"suffix",fn:c(()=>[Yt(B.$slots,"suffix")])}:void 0]),1040,["clearable","disabled","name","model-value","onKeydown"])],14,Wo)]),_:3},8,["visible","placement","popper-class","teleported","transition"]))}});var ti=Ts(ei,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/autocomplete/src/autocomplete.vue"]]);const Xa=$s(ti),ni=a=>{const{t:i}=Ze(),u=Rt(),p=je(()=>{var A;return(A=u.state.user)==null?void 0:A.entityType}),f=I(),d=I("1"),o=it({printerNo:"",ticketNo:"",refundNo:"",domestic:!0,ticketOrganization:""}),_={ticketOrganization:[{required:!0,message:i("app.ticketStatus.deviceNumNull"),trigger:"blur"}],printerNo:[{required:!0,message:i("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Ht,trigger:"blur",message:i("app.ticketStatus.deviceError")}],ticketNo:[{required:!0,message:i("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:Tn,message:i("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}],domestic:[{required:!0,message:i("app.agentTicketQuery.validate.required"),trigger:"blur"}],refundNo:[{required:!0,message:i("app.agentTicketQuery.validate.required"),trigger:"blur"},{pattern:Gs,message:i("app.agentTicketQuery.validate.refundNoError"),trigger:"blur"}]},h=I([]),y={BSP:{label:i("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:i("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:i("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:i("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:i("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:i("app.agentTicketQuery.OWNTicket"),value:"ARL"}},T=je(()=>!["CDS","GPCDS"].includes(o.ticketOrganization)),b=A=>{o.domestic=A==="D"||!A},S=()=>{f.value.validate(A=>{A&&a("openRefundDialog",o.ticketNo,o.refundNo,o.domestic,o.printerNo,o.ticketOrganization)})},k=()=>{a("update:modelValue",!1)},$=()=>{d.value==="1"&&(o.refundNo=""),d.value==="2"&&(o.ticketNo="")},m=()=>{var A,l,P,j,X,ce,M,w,W,C,R,N;((A=p.value)!=null&&A.includes("$$$")||(l=p.value)!=null&&l.includes("BSP"))&&(h.value.push(y.BSP),h.value.push(y.GPBSP)),!((P=p.value)!=null&&P.includes("BSP"))&&((j=p.value)!=null&&j.includes("GP"))&&h.value.push(y.GPBSP),((X=p.value)!=null&&X.includes("$$$")||(ce=p.value)!=null&&ce.includes("BOP"))&&h.value.push(y.BOPBSP),((M=p.value)!=null&&M.includes("$$$")||(w=p.value)!=null&&w.includes("CDS"))&&(h.value.push(y.CDS),h.value.push(y.GPCDS)),((W=p.value)!=null&&W.includes("$$$")||(C=p.value)!=null&&C.includes("本票"))&&h.value.push(y.ARL),o.ticketOrganization=((N=(R=h.value)==null?void 0:R[0])==null?void 0:N.value)??""};return mt(()=>{m()}),{refundType:d,formDate:f,printNoFrom:o,PRINTER_NO_RULES:_,ticketOrganizationList:h,isShowPrintNo:T,confirmPrinterNo:S,closeDialog:k,deliverPrintType:b,changeRefundType:$}},ai=ni,si=t("i",{class:"iconfont icon-close"},null,-1),oi=[si],ii={class:"carType-option-panel"},li={key:0,class:"flex"},ri=t("br",null,null,-1),ci=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),ui={key:1,class:"flex"},di=t("br",null,null,-1),pi=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),fi=Le({__name:"RefundParameterDialog",emits:["update:modelValue","openRefundDialog"],setup(a,{emit:i}){const u=i,{refundType:p,formDate:f,printNoFrom:d,PRINTER_NO_RULES:o,ticketOrganizationList:_,isShowPrintNo:h,confirmPrinterNo:y,closeDialog:T,deliverPrintType:b,changeRefundType:S}=ai(u);return(k,$)=>{const m=Dt,A=Et,l=nt,P=Zt,j=en,X=ct,ce=vt,M=_t,w=Rn,W=ut,C=et,R=rt;return s(),re(R,{title:k.$t("app.agentTicketQuery.queryRefundTitle"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(T)},{footer:c(()=>[t("div",null,[r(C,{type:"primary","data-gid":"091T0107",onClick:$[8]||($[8]=N=>e(y)())},{default:c(()=>[J(n(k.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),r(C,{onClick:e(T)},{default:c(()=>[J(n(k.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:$[0]||($[0]=(...N)=>e(T)&&e(T)(...N))},oi),r(W,{ref_key:"formDate",ref:f,model:e(d),rules:e(o),"label-position":"left","require-asterisk-position":"right"},{default:c(()=>[r(A,{modelValue:e(p),"onUpdate:modelValue":$[1]||($[1]=N=>Me(p)?p.value=N:null),onChange:e(S)},{default:c(()=>[r(m,{label:"1"},{default:c(()=>[J(n(k.$t("app.agentTicketQuery.useTicketNo")),1)]),_:1}),r(m,{label:"2"},{default:c(()=>[J(n(k.$t("app.agentTicketQuery.useRefundNo")),1)]),_:1})]),_:1},8,["modelValue","onChange"]),r(X,{prop:"ticketOrganization",label:k.$t("app.agentTicketQuery.ticketOrganization")},{default:c(()=>[r(j,{modelValue:e(d).ticketOrganization,"onUpdate:modelValue":$[2]||($[2]=N=>e(d).ticketOrganization=N),class:"ticket-management-organization",disabled:e(d).ticketOrganization==="",placeholder:e(d).ticketOrganization===""?k.$t("app.agentTicketQuery.noData"):""},{default:c(()=>[(s(!0),g(_e,null,Re(e(_),N=>(s(),re(P,{key:N.value,label:N.label,value:N.value},{default:c(()=>[t("div",ii,[t("div",{class:Pe(e(d).ticketOrganization===N.value?"show-select":"hidden-select")},[r(l,null,{default:c(()=>[r(e(Yn))]),_:1})],2),t("span",null,n(N.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),e(p)==="1"?(s(),g("div",li,[r(X,{prop:"ticketNo",label:k.$t("app.agentTicketQuery.ticketNo")},{default:c(()=>[r(ce,{modelValue:e(d).ticketNo,"onUpdate:modelValue":$[3]||($[3]=N=>e(d).ticketNo=N),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),r(M,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:c(()=>[J(n(k.$t("app.agentTicketQuery.ticketNumberTips")),1),ri,J(" "+n(k.$t("app.agentTicketQuery.forExample"))+"：999-1234567890"+n(k.$t("app.agentTicketQuery.or"))+"9991234567890 ",1)]),default:c(()=>[ci]),_:1})])):(s(),g("div",ui,[r(X,{prop:"refundNo",label:k.$t("app.agentTicketQuery.rtNum")},{default:c(()=>[r(ce,{modelValue:e(d).refundNo,"onUpdate:modelValue":$[4]||($[4]=N=>e(d).refundNo=N),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),r(M,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:c(()=>[J(n(k.$t("app.agentTicketQuery.refundNoError")),1),di,J(" "+n(k.$t("app.agentTicketQuery.forExample"))+"：999-123456789"+n(k.$t("app.agentTicketQuery.or"))+"999123456789"+n(k.$t("app.agentTicketQuery.or"))+"123456789 ",1)]),default:c(()=>[pi]),_:1})])),e(h)?(s(),re(X,{key:2,prop:"printerNo",label:k.$t("app.ticketStatus.deviceNum")},{default:c(()=>[r(Kt,{modelValue:e(d).printerNo,"onUpdate:modelValue":[$[5]||($[5]=N=>e(d).printerNo=N),$[6]||($[6]=N=>e(f).validateField("printerNo"))],"select-class":"w-[340px]",onDeliverPrintType:e(b)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):ee("",!0),r(X,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"domestic",label:k.$t("app.agentTicketQuery.printType")},{default:c(()=>[r(w,{modelValue:e(d).domestic,"onUpdate:modelValue":$[7]||($[7]=N=>e(d).domestic=N),"inline-prompt":"","active-text":k.$t("app.issue.dom"),"inactive-text":k.$t("app.issue.intr")},null,8,["modelValue","active-text","inactive-text"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const mi=a=>{const{t:i}=Ze(),u=I();let p=null;const f=I(!1),d=Nn(),o=it({optionType:"1",selectType:"NI",tktNo:"",pnrNo:"",ticketHistory:!1,passengerName:"",certificateNo:"",secondFactorType:"certificate",secondFactorCode:"NI",secondFactorValue:""}),_={3:[{label:`NI ${i("app.agentTicketQuery.certs.by_IDAndResidence")}`,value:"NI"},{label:`PP ${i("app.agentTicketQuery.certs.by_PassportAndOther")}`,value:"PP"},{label:`UU ${i("app.agentTicketQuery.certs.by_Unable")}`,value:"UU"}]},h=(R,N,K)=>{if(!(o.optionType==="3"&&o.selectType==="NI")){K();return}va.test(N)?K():K(i("app.agentTicketQuery.validate.enterCorrectIDTips"))},y=(R,N,K)=>{N?o.secondFactorType==="PNR"&&!gn.test(N)?K(i("app.agentTicketQuery.validate.enterCorrectPNRnumber")):o.secondFactorType==="name"&&!ya.test(N)?K(i("app.agentTicketQuery.validate.passengerNameError")):o.secondFactorType==="certificate"&&o.secondFactorCode==="NI"&&!va.test(N)&&K(i("app.agentTicketQuery.validate.enterCorrectIDTips")):o.secondFactorType==="PNR"?K(i("app.agentTicketQuery.validate.inputPNRNo")):o.secondFactorType==="name"?K(i("app.agentTicketQuery.validate.inputPassengerName")):K(i("app.agentTicketQuery.validate.inputCertificateNo")),K()},T={optionType:[{required:!0,message:i("app.agentTicketQuery.validate.required"),trigger:"change"}],selectType:[{required:!0,message:i("app.agentTicketQuery.validate.required"),trigger:"change"}],tktNo:[{required:!0,message:i("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:Tn,message:i("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}],pnrNo:[{required:!0,message:i("app.agentTicketQuery.validate.inputPNRNo"),trigger:"blur"},{pattern:gn,message:i("app.agentTicketQuery.validate.enterCorrectPNRnumber"),trigger:"blur"}],certificateNo:[{required:!0,message:i("app.agentTicketQuery.validate.inputCertificateNo"),trigger:"blur"},{validator:h,trigger:"blur"}],passengerName:[{required:!0,message:i("app.agentTicketQuery.validate.inputPassengerName"),trigger:"blur"},{pattern:ya,message:i("app.agentTicketQuery.validate.passengerNameError"),trigger:"blur"}],secondFactorValue:[{validator:y,trigger:"blur"}]},b=R=>{o.secondFactorType=R.secondFactorType,o.secondFactorCode=R.secondFactorCode,o.secondFactorValue=R.secondFactorValue},S=()=>{const{tktNo:R,secondFactorCode:N,secondFactorValue:K}=o;return{ticketNo:R,detrType:"CONJUNCTIVE_TICKET",secondFactor:{secondFactorCode:N,secondFactorValue:K}}},k=async()=>{try{const R=we("091M0101"),N=(await La(S(),R)).data.value;a("handleQueryTicket",N,o.optionType)}catch{X()}},$=async()=>{try{const R=we("091M0108"),N=(await po({certNo:o.certificateNo,certCode:o.selectType},R)).data.value;a("handleQueryTicket",N,o.optionType)}catch{X()}},m=async()=>{try{const R=we("091M0110"),N=(await uo(o.pnrNo,R)).data.value;a("handleQueryTicket",N,o.optionType)}catch{X()}},A=async()=>{try{const R=we("091M0109"),N=(await fo({certCode:"NM",certNo:Ft.encode(o.passengerName.trim())},R)).data.value;a("handleQueryTicket",N,o.optionType)}catch{X()}},l=async()=>{var R;(R=u.value)==null||R.validate(async N=>{if(N){switch(p=Mt.service({fullscreen:!0}),o.optionType){case"1":k();break;case"2":m();break;case"3":$();break;case"4":A();break}d.replace("/v2/crs/ticketOperation")}})},P=()=>{var R,N;o.selectType=((N=(R=_[o.optionType])==null?void 0:R[0])==null?void 0:N.value)??"TICKET",o.pnrNo="",o.tktNo="",o.ticketHistory=!1,o.certificateNo="",o.passengerName=""},j=()=>{var R;(R=u.value)==null||R.clearValidate("certificateNo")},X=()=>{p&&p.close()},ce=(R,N)=>{N==="1"?(o.optionType=N,o.selectType="TICKET",o.tktNo=R):N==="2"?(o.optionType=N,o.pnrNo=R):N==="3"&&(o.optionType=N,o.selectType="NI",o.certificateNo=R),l(),d.replace("/v2/crs/ticketOperation")},M=R=>{R.get("secondFactorCode")&&R.get("secondFactorValue")?(o.secondFactorType="PNR",o.secondFactorCode=R.get("secondFactorCode")??"CN",o.secondFactorValue=R.get("secondFactorValue")??""):(o.secondFactorType="certificate",o.secondFactorCode="NI",o.secondFactorValue="")},w=()=>{f.value=!0},W=()=>{f.value=!1},C=(R,N,K,oe,te)=>{a("openRefundDialog",R,N,K,oe,te)};return Ct(()=>d.currentRoute.value.fullPath,R=>{const N=new URLSearchParams(window.location.search);R.includes("ticketOperation")&&(N.get("pnrNo")&&(o.pnrNo=N.get("pnrNo")??"",o.optionType="2",p=Mt.service({fullscreen:!0}),m()),N.get("ticketNumber")&&(o.tktNo=N.get("ticketNumber")??"",o.optionType="1",M(N),o.secondFactorCode&&o.secondFactorValue&&(p=Mt.service({fullscreen:!0}),k())),d.replace("/v2/crs/ticketOperation"))},{immediate:!0,deep:!0}),{formRef:u,queryForm:o,queryTkt:l,SELECT_OPTION:_,FORM_RULES:T,updateSecondFactorFormData:b,clearOtherInput:P,clearCertValid:j,queryTktByRoute:ce,closeLoading:X,refundView:w,showPrintNoDialog:f,queryRefundMessage:C,closeRefundView:W}},gi=mi,nn=a=>(wt("data-v-d672a212"),a=a(),St(),a),ki={class:"query-condition-container bg-gray-0 p-[10px] rounded-t-lg"},yi={class:"inline-block mr-[10px] mb-[10px]"},vi={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[36px]"},hi=nn(()=>t("span",{class:"text-red-1 text-xs font-normal"},"*",-1)),_i=nn(()=>t("br",null,null,-1)),bi=nn(()=>t("i",{class:"iconfont icon-info-circle-line ticket-icon mr-[10px]"},null,-1)),xi=nn(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight mr-[3px]"},"*",-1)),Ti={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[47px]"},Ni=nn(()=>t("span",{class:"text-red-1 text-xs font-normal"},"*",-1)),$i=nn(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight"},"*",-1)),Ri={key:0,class:"iconfont icon-right-line text-[12px]"},Ci={key:1,class:"inline-block w-[12px] mr-[4px]"},wi={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[35px] -mt-[2px]"},Si=nn(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight"},"*",-1)),Pi=Le({__name:"TicketQueryCondition",emits:["handleQueryTicket","addNewTab","openAuthOffice","openBatchRefund","openBopRefund","openManualRefund","openRefundDialog","openRtkt","openCccf"],setup(a,{expose:i,emit:u}){const p=u,{formRef:f,queryForm:d,queryTkt:o,SELECT_OPTION:_,FORM_RULES:h,updateSecondFactorFormData:y,clearOtherInput:T,clearCertValid:b,queryTktByRoute:S,closeLoading:k,refundView:$,showPrintNoDialog:m,queryRefundMessage:A,closeRefundView:l}=gi(p);return i({queryTkt:o,queryTktByRoute:S,closeLoading:k,closeRefundView:l}),(P,j)=>{const X=Dt,ce=Et,M=ct,w=vt,W=_t,C=Zt,R=en,N=et,K=Cn,oe=ut,te=$n("permission");return s(),g(_e,null,[t("div",ki,[r(oe,{ref_key:"formRef",ref:f,inline:!0,model:e(d),rules:e(h),class:"query-condition-form"},{default:c(()=>[r(M,{prop:"optionType",class:"query-left mb-[10px]"},{default:c(()=>[r(ce,{modelValue:e(d).optionType,"onUpdate:modelValue":j[0]||(j[0]=D=>e(d).optionType=D),class:"ml-4",onChange:e(T)},{default:c(()=>[r(X,{label:"1",size:"large"},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.ticketNumber")),1)]),_:1}),r(X,{label:"3",size:"large"},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.idNumber")),1)]),_:1}),r(X,{label:"4",size:"large"},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.passengerName")),1)]),_:1})]),_:1},8,["modelValue","onChange"])]),_:1}),t("div",yi,[e(d).optionType==="1"?(s(),g(_e,{key:0},[t("div",vi,[J(n(P.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+" ",1),hi]),r(M,{prop:"tktNo"},{default:c(()=>[r(w,{modelValue:e(d).tktNo,"onUpdate:modelValue":j[1]||(j[1]=D=>e(d).tktNo=D),modelModifiers:{trim:!0},placeholder:P.$t("app.agentTicketQuery.inputEmdNo"),class:"ticket-input",clearable:"",onInput:j[2]||(j[2]=D=>{var E;return e(d).tktNo=((E=e(d).tktNo)==null?void 0:E.toUpperCase())??""})},null,8,["modelValue","placeholder"])]),_:1}),r(W,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:c(()=>[J(n(P.$t("app.agentTicketQuery.ticketNumberTips")),1),_i,J(" "+n(P.$t("app.agentTicketQuery.suchAs"))+"：999-1234567890 ",1)]),default:c(()=>[bi]),_:1}),xi,r(io,{"item-props":"secondFactorValue","second-factor-type":e(d).secondFactorType,"second-factor-code":e(d).secondFactorCode,"second-factor-value":e(d).secondFactorValue,onUpdateFormData:e(y),onValidateProp:j[3]||(j[3]=D=>{var E;return(E=e(f))==null?void 0:E.validateField("secondFactorValue")}),onClearPropValidate:j[4]||(j[4]=D=>{var E;return(E=e(f))==null?void 0:E.clearValidate("secondFactorValue")})},null,8,["second-factor-type","second-factor-code","second-factor-value","onUpdateFormData"])],64)):ee("",!0),e(d).optionType==="2"?(s(),g(_e,{key:1},[t("div",Ti,[J(n(P.$t("app.agentTicketQuery.pnrNo"))+" ",1),Ni]),r(M,{prop:"pnrNo"},{default:c(()=>[r(w,{modelValue:e(d).pnrNo,"onUpdate:modelValue":j[5]||(j[5]=D=>e(d).pnrNo=D),modelModifiers:{trim:!0},placeholder:P.$t("app.agentTicketQuery.inputPNRNo"),class:"pnr-input",clearable:"",onInput:j[6]||(j[6]=D=>{var E;return e(d).pnrNo=((E=e(d).pnrNo)==null?void 0:E.toUpperCase())??""})},null,8,["modelValue","placeholder"])]),_:1})],64)):ee("",!0),e(d).optionType==="3"?(s(),g(_e,{key:2},[r(M,{prop:"selectType"},{default:c(()=>[$i,r(R,{modelValue:e(d).selectType,"onUpdate:modelValue":j[7]||(j[7]=D=>e(d).selectType=D),"popper-class":"doctype-selector-popper",class:"doctype-select",teleported:!1,onChange:e(b)},{default:c(()=>[(s(!0),g(_e,null,Re(e(_)[e(d).optionType],D=>(s(),re(C,{key:D.value,label:D.label,value:D.value},{default:c(()=>[t("span",null,[e(d).selectType===D.value?(s(),g("i",Ri)):(s(),g("span",Ci)),t("span",null,n(D.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),r(M,{prop:"certificateNo"},{default:c(()=>[r(w,{modelValue:e(d).certificateNo,"onUpdate:modelValue":j[8]||(j[8]=D=>e(d).certificateNo=D),modelModifiers:{trim:!0},placeholder:P.$t("app.agentTicketQuery.inputCertificateNo"),class:"cert-input",clearable:"",onInput:j[9]||(j[9]=D=>{var E;return e(d).certificateNo=((E=e(d).certificateNo)==null?void 0:E.toUpperCase())??""})},null,8,["modelValue","placeholder"])]),_:1})],64)):ee("",!0),e(d).optionType==="4"?(s(),g(_e,{key:3},[t("div",wi,[J(n(P.$t("app.agentTicketQuery.name"))+" ",1),Si]),r(M,{prop:"passengerName"},{default:c(()=>[r(w,{modelValue:e(d).passengerName,"onUpdate:modelValue":j[10]||(j[10]=D=>e(d).passengerName=D),class:"name-input",clearable:"",onInput:j[11]||(j[11]=D=>{var E;return e(d).passengerName=((E=e(d).passengerName)==null?void 0:E.toUpperCase())??""})},null,8,["modelValue"])]),_:1})],64)):ee("",!0)]),r(M,null,{default:c(()=>[r(N,{type:"primary","data-gid":"091M0101",onClick:e(o)},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.queryBtn")),1)]),_:1},8,["onClick"]),Ue((s(),re(N,{onClick:j[12]||(j[12]=D=>p("openAuthOffice"))},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.ticketAuthBtn")),1)]),_:1})),[[te,"crs-tc-ticketOperation-ticketQuery-ticketAuth-button"]]),Ue((s(),re(N,{onClick:j[13]||(j[13]=D=>p("openBatchRefund"))},{default:c(()=>[J(n(P.$t("app.batchRefund.batchRefund")),1)]),_:1})),[[te,"crs-tc-ticketOperation-ticketQuery-refundTicket-button"]]),Ue((s(),re(N,{onClick:j[14]||(j[14]=D=>p("openBopRefund"))},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.bopRefund")),1)]),_:1})),[[te,"crs-ticket-manage-ticket-query-page-bop-refund-button"]]),Ue((s(),re(N,{onClick:j[15]||(j[15]=D=>p("openManualRefund"))},{default:c(()=>[J(n(P.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1})),[[te,"crs-ticket-manage-ticket-query-page-manual-refund-button"]]),r(N,{onClick:e($)},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.queryRefundBtn")),1)]),_:1},8,["onClick"]),r(N,{onClick:j[16]||(j[16]=D=>p("openRtkt"))},{default:c(()=>[J("RTKT")]),_:1}),r(K,{"popper-class":"credit-card-receipt-print-popover",placement:"top",effect:"dark",width:"auto",content:P.$t("app.cccf.creditCardReceiptPrint")},{reference:c(()=>[r(N,{onClick:j[17]||(j[17]=D=>p("openCccf"))},{default:c(()=>[J("CCCF")]),_:1})]),_:1},8,["content"])]),_:1})]),_:1},8,["model","rules"])]),e(m)?(s(),re(fi,{key:0,modelValue:e(m),"onUpdate:modelValue":j[18]||(j[18]=D=>Me(m)?m.value=D:null),onOpenRefundDialog:e(A)},null,8,["modelValue","onOpenRefundDialog"])):ee("",!0)],64)}}});const Ai=dt(Pi,[["__scopeId","data-v-d672a212"]]),Di=(a,i)=>{var ma;const u=I(),p=Rt(),f=Ia(),{t:d}=Ze(),o=I(!1),_=I(!0),h=I(!1),y=I([]),T=I([]),b=De(!1),S=I({}),k=I([]),$=I(!1),m=I(!1),A=I(!1),l=it({isAlreadyClick:!1,isAlreadySuccessSearch:!1}),P=je(()=>{const U=y.value.filter(ne=>ne.infants).length;return Qe(y.value.length,U)===T.value.length}),j=De(""),X=De(""),ce=De(""),M=I(""),w=De(a.tktNo),W=I(!1),C=I([]),R=I(!1),N=I("auto"),K=De(!0),oe=De(""),te=I(),D=I([]),E=it({pnrNo:""}),pe=I(!1),ue={pnrNo:[{required:!0,message:d("app.agentTicketQuery.validate.inputPNRNo"),trigger:"blur"},{pattern:gn,message:d("app.agentTicketQuery.validate.enterCorrectPNRnumber"),trigger:"blur"}]},ge=je(()=>T.value.length>0?T.value.every(U=>{var ne;return(ne=U.ticket.segment)==null?void 0:ne.some(me=>["OPEN FOR USE","AIRPORT CNTL"].includes(me.ticketStatus??""))}):!1),se=U=>({passengerNameSuffix:U.passengerNameSuffix??"",passengerName:U.name,specialPassengerType:U.specialPassengerType,passengerType:U.specialPassengerType??"",index:"0",secondFactor:U.secondFactor,ticketNos:U.ticketNo.includes("-")?[U.ticketNo.split("-")[0],U.ticketNo.split("-")[1]]:[U.ticketNo],isChecked:!0}),de=I({}),H=I({}),Q=I(""),{defaultOffice:q,office:V,defaultRoleWithPid:B}=p.state.user,G=(B?q:((ma=V==null?void 0:V.split(";"))==null?void 0:ma[0])??"")===qa,L=(U,ne)=>{const me=new Map;return U.forEach(he=>{var He;const Ve=(He=he[ne])==null?void 0:He.replace(/-/,"");me.set(Ve,me.get(Ve)||[]),me.get(Ve).push(he)}),me},ae=async(U,ne,me,he)=>{const Ve=T.value.some(He=>He.ticket.ticketNo.includes(U));if(me&&Ve)return"";if(me)try{o.value=!0;const ze=(await qn({tktNo:U,secondFactor:ne,refundQuery:!0},he)).data.value;return ze!=null&&ze.data?(ze.data.ticket.ticketSegment=L(ze.data.ticket.segment??[],"tktTag"),H.value=ze==null?void 0:ze.data,T.value.push(ze.data),_.value&&y.value.push(se(ze.data.ticket)),ze.data.ticket.crsPnrNo??""):""}finally{o.value=!1}else return T.value=T.value.filter(He=>!He.ticket.ticketNo.includes(U)),T.value.length===1&&(H.value=T.value[0]),""},ie=U=>U==null?void 0:U.replace("-",""),Ne=U=>{U.ticketNos=U.ticketNos.map(ie),U.infants&&(U.infants.ticketNos=U.infants.ticketNos.map(ie))},Ae=U=>{U.forEach(Ne)},z=U=>{var me,he;const ne=(me=a.tktNo)==null?void 0:me.replace("-","");return(he=U.ticketNos)==null?void 0:he.includes(ne)},ke=(U,ne)=>(K.value=U.some(z),K.value?ce.value=ne:oe.value=(U==null?void 0:U.length)===0?d("app.agentTicketRefund.queryPnrFailed"):d("app.agentTicketRefund.currentTicketNotMatchPnr"),K.value),x=U=>{var me,he,Ve;y.value.splice(0,1);const ne=y.value.findIndex(He=>He.index===(U==null?void 0:U.index));y.value.unshift(y.value.splice(ne,1)[0]),(Ve=(he=(me=y.value[0])==null?void 0:me.infants)==null?void 0:he.ticketNos)!=null&&Ve.length&&(y.value[0].infants.isChecked=!0)},O=async(U,ne)=>{var me,he,Ve;if(y.value[0].infants=U==null?void 0:U.infants,y.value[0].specialPassengerType=U==null?void 0:U.specialPassengerType,y.value[0].index=(U==null?void 0:U.index)??"",y.value[0].secondFactor=U==null?void 0:U.secondFactor,(Ve=(he=(me=y.value[0])==null?void 0:me.infants)==null?void 0:he.ticketNos)!=null&&Ve.length){y.value[0].infants.isChecked=!0;const{ticketNos:He,secondFactor:ze={}}=y.value[0].infants;await ae(He[0],ze,!0,ne)}},Y=async(U,ne)=>{const me=U.find(z)??{};U.forEach(he=>{!z(he)&&(he.specialPassengerType!=="INF"||he.passengerType!=="INF")&&y.value.push(he)}),(me==null?void 0:me.specialPassengerType)==="INF"?x(me):await O(me,ne)},fe=async(U,ne,me)=>{var he,Ve;try{me&&await(me==null?void 0:me.validate()),o.value=!0;const He=((Ve=(he=await Po({pnrNo:U},ne))==null?void 0:he.data)==null?void 0:Ve.value)??{},{passengers:ze=[]}=He;if(Ae(ze),!ke(ze,U))return;await Y(ze,ne)}finally{o.value=!1}},ye=U=>{const ne={pnrHandleType:U,pnrNo:P.value?"":ce.value,xePnr:Fe()?"":!P.value&&!Fe()?M.value:ce.value,passengerInfoList:[]};return bt().length?bt().forEach(me=>{var he,Ve;ne.passengerInfoList.push({name:Ft.encode(((he=me==null?void 0:me.infants)==null?void 0:he.passengerNameSuffix)??""),psgType:"INF",ticketNo:((Ve=me==null?void 0:me.infants)==null?void 0:Ve.ticketNos[0])??""})}):T.value.forEach(me=>{!P.value&&me.ticket.psgType==="INF"&&ne.passengerInfoList.push({name:Ft.encode(me.ticket.name),psgType:me.ticket.psgType,ticketNo:me.ticket.ticketNo})}),ne},Se=()=>T.value.map(U=>{var ne,me,he;return{ticketNo:((me=(ne=U.ticket.segment)==null?void 0:ne[0])==null?void 0:me.tktTag)??"",domestic:(he=U.ticket.tktType)==null?void 0:he.includes("D"),printNo:U.printerNo}}),Fe=()=>T.value.every(U=>U.ticket.psgType==="INF"),Be=async U=>{await mn(d("app.agentTicketRefund.refundSuccess")),A.value=!0,h.value=!0;const ne=yt(T.value);T.value=[],ne.forEach(async me=>{var he,Ve;await ae(((Ve=(he=me.ticket.segment)==null?void 0:he[0])==null?void 0:Ve.tktTag)??"",me.ticket.secondFactor,!0,U)})},Ke=U=>(U??[]).reduce((ne,me)=>{let he="";ne||(he=`<p class="text-gray-1 text-lg font-normal pb-2.5">${d("app.agentTicketRefund.refundPartFail")}</p>`);const Ve=me.success?'<i class="iconfont icon-ticket text-green-2 mr-2.5"></i>':'<i class="iconfont icon-close text-red-1 mr-2.5"></i>',He=`${he}<p class="text-sm font-bold leading-normal mt-4">${Ve}${me.ticketNo}</p>`;return ne+He},""),ve=async(U,ne)=>{Je.confirm(Ke(U),{icon:Ce(nt,{color:"#FF3636",size:32},()=>Ce(wa)),customClass:"invalidated-warning-msg crs-btn-ui",closeOnClickModal:!1,showClose:!0,showCancelButton:!1,confirmButtonText:d("app.agentTicketRefund.confirm"),dangerouslyUseHTMLString:!0,draggable:!0}),h.value=!0;const me=yt(T.value);T.value=[];for(let he=0;he<me.length;he++)try{o.value=!0;const Ve=(await qn({tktNo:me[he].ticket.ticketNo,secondFactor:me[he].ticket.secondFactor,refundQuery:!0},ne)).data.value;if(!(Ve!=null&&Ve.data))return;Ve.data.ticket.ticketSegment=L(Ve.data.ticket.segment??[],"tktTag"),T.value.push(Ve.data)}finally{o.value=!1}T.value.forEach(he=>{he.ticket.isRefundFail=U.some(Ve=>Ve.ticketNo.replace("-","").includes(he.ticket.ticketNo)&&!Ve.success)})},Ye=U=>{R.value=!0,N.value="auto",C.value=(U??[]).map(ne=>({ticketNo:ne.ticketNo,trfdNo:ne.trfdno??""})),(U??[]).forEach(ne=>{ne.amount&&D.value.push(ne.amount)})},le=U=>{N.value="manual",C.value=U??[]},pt=U=>{const ne=U.indexOf("("),me=U.indexOf("*");if(ne>-1&&me>-1){const he=Math.min(ne,me);return(U==null?void 0:U.substring(0,he))??""}else{if(ne===-1&&me>-1)return(U==null?void 0:U.substring(0,me))??"";if(ne>-1&&me===-1)return(U==null?void 0:U.substring(0,ne))??""}return U.trim()},at=(U,ne)=>{const me=yt(T.value);T.value=[],me.forEach(async he=>{var He,ze;const Ve={secondFactorCode:"NM",secondFactorValue:pt(he.ticket.name)};U&&M.value&&(ce.value=M.value),await ae(((ze=(He=he.ticket.segment)==null?void 0:He[0])==null?void 0:ze.tktTag)??"",Ve,!0,ne)})},xe=(U,ne)=>{at(!0,ne);const me=`/v2/crs/pnrManagement?pnrNo=${U}`;yn.setLink(me)},F=U=>U&&(P.value||Fe()||bt().length===0),Te=U=>{A.value=!0,h.value=!0;const ne=T.value[0].ticket.tktType==="I";if(ne||(mn(d("app.agentTicketRefund.refundSuccess")),at(!1,U)),F(ne)){let me="C",he=M.value?M.value:ce.value,Ve=d("app.agentTicketRefund.cancelPnr"),He=d("app.agentTicketRefund.refundSuccessCanclePnrTips",{pnrNo:M.value?M.value:ce.value});Fe()&&(he=ce.value,me="D",Ve=d("app.agentTicketRefund.xePassenger"),He=d("app.agentTicketRefund.refundSuccessDeleteTips",{pnrNo:ce.value}));const ze=Je;ze.confirm(Ce("div",{},[Ce("span",{className:"text-[18px] text-gray-1"},He),Ce("span",{className:"text-[18px] underline text-brand-2 cursor-pointer",onClick:()=>{xe(he,U),ze.close()}},d("app.agentTicketRefund.toOrder")),Ce("span",{className:"text-[18px] text-gray-1"},d("app.agentTicketRefund.handlePnr"))]),{icon:Ce(nt,{color:Rs("--bkc-tw-green-2",null).value,size:32},()=>Ce(Kn)),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:Ve,cancelButtonText:d("app.agentTicketRefund.cancel"),showClose:!1}).then(async()=>{await We(me,me==="C"?he:"",U),at(me==="C",U)}).catch(()=>{at(me==="C",U)})}else ne&&!P.value&&(pe.value=!0)},$e=async U=>{let ne=d("app.agentTicketRefund.cancelPnr"),me="C";const he=T.value[0].ticket.tktType==="I";let Ve=bt().length&&!he?d("app.agentTicketRefund.refundSeatTipsNonCarryInft",{pnrNo:M.value?M.value:ce.value,ticketNo:It(bt())}):d("app.agentTicketRefund.refundSeatTips",{pnrNo:M.value?M.value:ce.value});Fe()&&(me="D",ne=d("app.agentTicketRefund.xePassenger"),Ve=d("app.agentTicketRefund.refundSeatDeleteTips",{pnrNo:ce.value})),Je.confirm(Ce("div",{className:"whitespace-pre-line"},Ve),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:ne,cancelButtonText:d("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1}).then(async()=>{await We(me,me==="C"?M.value?M.value:ce.value:"",U),(j.value==="S"||X.value==="S")&&Oe(U)}).catch(async He=>{var ze,Ot,qt;if(He==="cancel"&&M.value){const Vt=(qt=(Ot=(ze=T.value.filter(Fn=>Fn.ticket.psgType!=="INF"))==null?void 0:ze[0])==null?void 0:Ot.ticket)==null?void 0:qt.ticketNo,zt={secondFactorCode:"CN",secondFactorValue:M.value},On={title:`${d("app.agentTicketRefund.newRefund")}${Vt}`,name:`newRefund?type=ticketNo&sign=${Vt}`,content:Jt(xn)};i("addNewTab",On,"",zt),await cn(),i("removeTab",-1,`newRefund?type=ticketNo&sign=${w.value}`)}})},Ee=async U=>{o.value=!0;const ne=(await Ta({ticketList:Se(),save:"NO"},U)).data.value;ne!=null&&ne.allSuccess&&Be(U),!(ne!=null&&ne.allSuccess)&&!(ne!=null&&ne.allFail)&&ve((ne==null?void 0:ne.refundResult)??[],U),Ye((ne==null?void 0:ne.refundResult)??[])},Oe=async U=>{o.value=!0;let ne;try{ne=(await Ta({ticketList:Se(),save:"NO"},U)).data.value}finally{o.value=!1}if(ne!=null&&ne.needSeatVacated){$e(U);return}ne!=null&&ne.allSuccess&&Te(U),!(ne!=null&&ne.allSuccess)&&!(ne!=null&&ne.allFail)&&ve((ne==null?void 0:ne.refundResult)??[],U),Ye((ne==null?void 0:ne.refundResult)??[])},Xe=async U=>{if(U&&j.value==="S"&&T.value[0].ticket.tktType==="I"){await mn(d("app.agentTicketRefund.pnrCancelSuccess",{pnrNo:U}));return}if(Fe()&&X.value==="S"&&T.value[0].ticket.tktType==="I"){await mn(d("app.agentTicketRefund.cancelPassengerSuccess"));return}const ne=X.value==="S"||j.value==="S";await lt({message:d(ne?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:ne?"success":"error",customClass:"z-3000"})},tt=U=>({fullName:U.passengerName,paxId:Number(U.index.replace("P","")),unMinor:!1,unMinorAge:0}),st=()=>{const U=[];return y.value.forEach(ne=>{ne.isChecked&&U.push(tt(ne))}),U},Ge=async U=>{var he;const ne={orderId:"",count:0,passengerRecordLocator:ce.value,travellers:st(),isGrp:!1},me=(await ta(ne,U)).data.value;M.value=((he=me==null?void 0:me.splitedOrder)==null?void 0:he.passengerRecordLocator)??"",lt({message:d("app.agentTicketRefund.splitSuccess"),type:"success"})},We=async(U,ne,me)=>{o.value=!0;try{const he=(await bn(ye(U),me)).data.value;j.value=(he==null?void 0:he.xePnrExecutionStatus)??"N",X.value=(he==null?void 0:he.deleteInfantExecutionStatus)??"N"}finally{o.value=!1}await Xe(ne)},It=U=>{let ne=[];return U.forEach(me=>{var he;ne=ne.concat(((he=me.infants)==null?void 0:he.ticketNos)??[])}),ne.join("、")},Ut=async()=>{let U=!1;if(!P.value&&!Fe()){const ne=T.value[0].ticket.tktType==="I",me=bt().length&&!ne?d("app.agentTicketRefund.splitTipNonCarryInf",{ticketNo:It(bt())}):d("app.agentTicketRefund.splitTip");await Je.confirm(Ce("div",{className:"whitespace-pre-line"},me),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:d("app.agentTicketRefund.sure"),cancelButtonText:d("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1}).catch(async he=>{var Ve,He,ze;if(U=!0,he==="cancel"&&bt().length&&T.value[0].ticket.tktType!=="I"){const Ot=(ze=(He=(Ve=T.value.filter(zt=>zt.ticket.psgType!=="INF"))==null?void 0:Ve[0])==null?void 0:He.ticket)==null?void 0:ze.ticketNo,qt={secondFactorCode:"CN",secondFactorValue:ce.value},Vt={title:`${d("app.agentTicketRefund.newRefund")}${Ot}`,name:`newRefund?type=ticketNo&sign=${Ot}`,content:Jt(xn)};i("addNewTab",Vt,"",qt),await cn(),i("removeTab",-1,`newRefund?type=ticketNo&sign=${w.value}`)}})}return U},bt=()=>y.value.filter(ne=>!ne.isChecked&&ne.infants&&ne.infants.isChecked),an=U=>{var me;return((me=T.value.filter(he=>he.ticket.ticketNo===U)[0])==null?void 0:me.printerNo)??""},Pn=()=>bt().map(ne=>{var me,he;return{ticketNo:(me=ne.infants)==null?void 0:me.ticketNos[0],domestic:T.value[0].ticket.tktType==="D",printNo:an(((he=ne.infants)==null?void 0:he.ticketNos[0])??"")}}),An=async U=>{if(U){const ne=d("app.agentTicketRefund.refundSeatDeleteTips",{pnrNo:ce.value});await Je.confirm(Ce("div",{className:"whitespace-pre-line"},ne),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:d("app.agentTicketRefund.xePassenger"),cancelButtonText:d("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})}},Dn=async U=>{if(!P.value&&!Fe()&&bt().length&&T.value[0].ticket.tktType!=="I"){let me;try{o.value=!0,me=(await mo({ticketList:Pn(),save:"NO"},U)).data.value}finally{o.value=!1}await An((me==null?void 0:me.needSeatVacated)??!1),me!=null&&me.needSeatVacated&&await We("D","",U)}if(!await Ut())try{o.value=!0,!P.value&&!Fe()&&await Ge(U),await Oe(U)}finally{o.value=!1}},En=()=>{const U=y.value.filter(he=>he.isChecked&&he.passengerType==="ADT"),ne=y.value.filter(he=>!he.isChecked&&he.passengerType==="INF");return(U??[]).some(he=>ne.some(Ve=>{var He,ze;return Ve.ticketNos.includes(((ze=(He=he.infants)==null?void 0:He.ticketNos)==null?void 0:ze[0])??"")}))},Z=async U=>{if(ce.value)Dn(U);else try{o.value=!0,await Ee(U)}finally{o.value=!1}},be=async U=>{if(En()){const ne=d("app.agentTicketRefund.refundWithBabyTip");await Je.confirm(ne,{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:d("app.agentTicketRefund.sure"),cancelButtonText:d("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})}U()},qe=()=>T.value.some(ne=>!((ne==null?void 0:ne.ticketManagementOrganizationCode)??"").includes("CDS")&&!ne.printerNo?(ne.printError=d("app.ticketStatus.deviceNumNull"),!0):!1),Ie=async()=>{if(qe())return;const U=d("app.agentTicketRefund.autoRefundTip");await Je.confirm(U,{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:d("app.agentTicketRefund.sure"),cancelButtonText:d("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1});const ne=we("091Q0105");be(()=>{Z(ne)})},kt=async()=>{b.value=!0},ft=async()=>{qe()||be(kt)},Qt=U=>{l.isAlreadyClick=U,Tt()},xt=()=>{var U,ne,me,he,Ve,He,ze,Ot;return{tktNos:T.value.map(qt=>{var Vt,zt;return((zt=(Vt=qt.ticket.segment)==null?void 0:Vt[0])==null?void 0:zt.tktTag)??""}),tktType:((me=(ne=(U=T.value)==null?void 0:U[0])==null?void 0:ne.ticket)==null?void 0:me.tktType)??"D",cityCode:((Ot=(ze=(He=(Ve=(he=T.value)==null?void 0:he[0])==null?void 0:Ve.ticket)==null?void 0:He.segment)==null?void 0:ze[0])==null?void 0:Ot.departureCode)??""}},Tt=async()=>{var ne,me;const U=we("091Q0103");if(T.value.length!==0){S.value={};try{o.value=!0,S.value=(me=(ne=await Ao(xt(),U))==null?void 0:ne.data)==null?void 0:me.value}catch{S.value.status="FAILURE"}finally{l.isAlreadySuccessSearch=S.value.status==="SUCCESS",o.value=!1}}},ls=U=>{var he;const ne=y.value[U].infants&&((he=y.value[U].infants)==null?void 0:he.isChecked)&&y.value[U].isChecked,me=y.value[U].infants&&!y.value[U].isChecked;return ne||me},rs=async U=>{var me,he,Ve,He,ze,Ot,qt,Vt,zt;const ne=we("091Q0101");if(ls(U)){const On=(((he=(me=y.value[U].infants)==null?void 0:me.ticketNos)==null?void 0:he[0])??"").replace("-",""),Fn=(((Ve=y.value[U].ticketNos)==null?void 0:Ve[0])??"").replace("-","");await Promise.all([ae(Fn,(He=y.value[U])==null?void 0:He.secondFactor,y.value[U].isChecked??!1,ne),ae(On??"",(qt=(Ot=(ze=y.value)==null?void 0:ze[U])==null?void 0:Ot.infants)==null?void 0:qt.secondFactor,((Vt=y.value[U].infants)==null?void 0:Vt.isChecked)??!1,ne)])}else await ae(y.value[U].ticketNos[0].replace("-",""),(zt=y.value[U])==null?void 0:zt.secondFactor,y.value[U].isChecked??!1,ne);l.isAlreadySuccessSearch&&Tt()},cs=(U,ne)=>{$.value=ne,m.value=!ne,A.value=ne,k.value=U.map(me=>{const he=me.resultpre;return{...he.amount,...he.ticket}})},us=async()=>{const U=yt(T.value);T.value=[],C.value=[],R.value=!1;const ne=we("091Q0101");U.forEach(async me=>{var he,Ve;await ae(((Ve=(he=me.ticket.segment)==null?void 0:he[0])==null?void 0:Ve.tktTag)??"",me.ticket.secondFactor,!0,ne)})};return Ct(()=>b.value,U=>{f.setShowManualRefund(U)}),mt(async()=>{const U=we("091Q0101");ce.value=await ae(a.tktNo,a.factor,!0,U),[E.pnrNo,_.value]=[ce.value,!1],ce.value&&await fe(ce.value,U)}),{splitPnrNo:M,isDragonBoatOffice:G,pnrNo:ce,amountRef:u,fullscreenLoading:o,allPassengerList:y,ticketDetailList:T,manualDisabled:W,manualDialogVisible:b,packageData:de,clacAmountInfosRes:S,clacAmountInfo:l,partSuccess:m,isAutoRefundFinished:h,isXePnr:P,currentTktNo:w,deviceNum:Q,isFinishManualRefund:$,manualRefundAmountInfo:k,isCanRefund:ge,queryTicketDetail:ae,manualRefund:ft,handleAutoRefund:Ie,getCalcInfo:Qt,queryCalcAmount:Tt,queryTicketAndCalcAmount:rs,getManualRefundAmountDetail:cs,refresh:us,ticketTrfdNoDetails:C,isShowTrfdNo:R,getTicketTrfdNoDetailsFromManualRefund:le,refundType:N,isRelatedCorrectPnr:K,updatePnrForm:te,updatePnrFormData:E,updatePnrFormRules:ue,queryAllPassenger:fe,queryPnrTip:oe,showInterRefundSuccess:pe,refreshTicketDetail:at,refundTicketSuccess:A,batchAutoAmount:D}},Ei=(a,i)=>{const{t:u}=Ze(),p=Rt(),f=I(),d=I(),o=z=>f.value=(z==null?void 0:z.$el)||z,_=I(!1),h=()=>{_.value=!0},y=()=>{_.value=!1},T=De(!1),b=I(),S=27,k=je(()=>a.reuseTicketInfo),$=I(""),m=I([]),A=z=>{var x,O;return(m.value??[]).some(Y=>z===Y.value)&&z?z:((O=(x=m.value)==null?void 0:x[0])==null?void 0:O.value)??""},l=it({...a.refundData}),P={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},j=je(()=>{var z;return(z=p.state.user)==null?void 0:z.entityType}),X=je(()=>!["CDS","GPCDS"].includes(l.ticketManagementOrganizationCode)),ce=je(()=>{var z;return((z=a.refundData)==null?void 0:z.tktType)==="I"}),M=(z,ke,x)=>{var Y;const O=Number(z.field.split(".")[1]);l.taxs[O].name&&!ke?x(u("app.agentTicketRefund.taxAmount")):!l.taxs[O].name&&!ke&&((Y=b.value)==null||Y.clearValidate(`taxs.${O}.name`),x()),x()},w=(z,ke,x)=>{var Y;const O=Number(z.field.split(".")[1]);l.taxs[O].value&&!ke?x(u("app.agentTicketRefund.taxes")):!l.taxs[O].value&&!ke&&((Y=b.value)==null||Y.clearValidate(`taxs.${O}.value`),x()),x()},W=(z,ke,x)=>{l.payType==="TC"&&(ke?!l.isDragonBoatOffice&&!Zn.test(ke)?x(u("app.agentTicketRefund.creditCardInput")):l.isDragonBoatOffice&&!ea.test(ke)&&x(u("app.agentTicketRefund.dragonBoatOfficeInput")):x(u("app.agentTicketRefund.creditCardNotEmpty"))),x()},C=(z,ke,x)=>{l.remark&&!_a.test(ke)&&x(u("app.agentTicketRefund.formatError")),x()},R=(z,ke,x)=>{var O;ke?((O=b.value)==null||O.validateField("remarkCode"),l.remarkCode==="IC"?(Fa.test(`${l.remarkCode}${ke}`)||x(u("app.agentTicketRefund.remarkIC")),x()):l.remarkCode&&!_n.test(`${l.remarkCode}${ke}`)&&x(u("app.agentTicketRefund.remarkHint")),x()):l.remarkCode&&x(u("app.agentTicketRefund.remarkHint")),x()},N={currency:[{required:!0,message:u("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:u("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:Jn,message:u("app.agentTicketRefund.paymentInput"),trigger:"change"}],totalAmount:[{pattern:gt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxValue:[{pattern:Ea,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"},{validator:M,trigger:"change"}],taxName:[{pattern:Oa,message:u("app.agentTicketRefund.taxes"),trigger:"change"},{validator:w,trigger:"change"}],commisionRate:[{pattern:gt,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],otherDeductionRate:[{pattern:gt,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],commision:[{pattern:gt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],psdName:[{pattern:Hs,message:u("app.agentTicketRefund.psgNameError"),trigger:"change"}],creditCard:[{validator:W,trigger:"change"}],remarkCode:[{pattern:_a,message:u("app.agentTicketRefund.formatError"),trigger:["change","blur"]},{validator:C,trigger:["change","blur"]}],remark:[{validator:R,trigger:["change","blur"]}],remarkInfo:[{pattern:_n,message:u("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],netRefund:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:gt,message:u("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"}]},K=()=>{if(l.taxs.length!==S)if(l.taxs.length>22&&l.taxs.length<S){const z=new Array(S-l.taxs.length).fill({name:"",value:""});l.taxs=l.taxs.concat(z).map(ke=>({...ke}))}else{const z=new Array(5).fill({name:"",value:""});l.taxs=l.taxs.concat(z).map(ke=>({...ke}))}},oe=()=>{const{totalAmount:z,totalTaxs:ke,otherDeduction:x,commision:O,commisionRate:Y}=l;if(ue()){q();return}if(Y){const fe=Lt(jt(Number(z),Number(Y)),100).toString(),ye=rn(Number(fe),2).toString();l.commision=ye;const Se=`${At(Qe(Number(z),Number(ke)),Qe(Number(x),Number(ye)))}`;l.netRefund=Number(Se).toFixed(2)}else{const fe=`${At(Qe(Number(z),Number(ke)),Qe(Number(x),Number(O)))}`;l.netRefund=Number(fe).toFixed(2)}l.commision!==""&&(l.commision=Number(l.commision).toFixed(2))},te=()=>{l.commisionRate||(l.commision="")},D=()=>{let z=new dn(0);l.taxs.forEach((ke,x)=>{var O;(O=b.value)==null||O.validateField(`taxs.${x}.value`).then(Y=>{Y&&(z=z.add(new dn(ke.value?ke.value:0)),l.totalTaxs=Nt(l.currency)?z.toString():Number(z).toFixed(2),Nt(l.currency)?ge(""):oe())})})},E=async()=>{const z=[];l.taxs.forEach((ke,x)=>{var O,Y;z.push((O=b.value)==null?void 0:O.validateField(`taxs.${x}.name`)),z.push((Y=b.value)==null?void 0:Y.validateField(`taxs.${x}.value`))}),await Promise.all(z),l.taxs.forEach((ke,x)=>{l.taxs[x].value&&(l.taxs[x].value=Nt(l.currency)?l.taxs[x].value??0:Number(l.taxs[x].value??0).toFixed(2))}),D(),q()},pe=z=>z&&!gt.test(z),ue=()=>{const{totalAmount:z,otherDeductionRate:ke,otherDeduction:x,commision:O,commisionRate:Y}=l;return pe(z??"")||pe(ke??"")||pe(x??"")||pe(O??"")||pe(Y??"")},ge=z=>{if(z==="otherDeductionRate"&&l.otherDeductionRate){const ye=Lt(jt(Number(l.totalAmount),Number(l.otherDeductionRate)),100).toString();l.otherDeduction=rn(Number(ye),2).toString()}const{totalAmount:ke,totalTaxs:x,otherDeduction:O,commision:Y,commisionRate:fe}=l;if(!ue())if(fe){const ye=Lt(jt(Number(ke),Number(fe)),100).toString(),Se=rn(Number(ye),2).toString();l.commision=Se.endsWith(".00")?Se.slice(0,-3):Se;const Fe=`${At(Qe(Number(ke),Number(x)),Qe(Number(O),Number(Se)))}`;l.netRefund=Number(Fe).toFixed(2),l.netRefund.endsWith(".00")&&(l.netRefund=l.netRefund.slice(0,-3))}else l.netRefund=`${At(Qe(Number(ke),Number(x)),Qe(Number(O),Number(Y)))}`},se=async z=>{if(ue()){q();return}if(Nt(l.currency)){ge(z),q();return}if(z==="otherDeductionRate"&&l.otherDeductionRate){l.otherDeductionRate=Number(l.otherDeductionRate).toFixed(2);const ke=Lt(jt(Number(l.totalAmount),Number(l.otherDeductionRate)),100).toString();l.otherDeduction=Number(ke).toFixed(2)}oe(),l.totalAmount!==""&&(l.totalAmount=Number(l.totalAmount).toFixed(2)),l.commision!==""&&(l.commision=Number(l.commision).toFixed(2)),l.commisionRate!==""&&(l.commisionRate=Number(l.commisionRate).toFixed(2)),l.otherDeduction!==""&&(l.otherDeduction=Number(l.otherDeduction).toFixed(2)),q()},de=()=>{b.value.clearValidate("creditCard"),l.payType.toUpperCase()==="TC"&&(l.creditCard=l.isDragonBoatOffice?na:"")},H=z=>{z.target.value!==""&&!wn.some(ke=>ke.label===z.target.value)&&(l.payType=z.target.value),q()},Q=async()=>{var z;try{return await((z=b.value)==null?void 0:z.validate())}catch{return!1}},q=async()=>{const z=await Q();i("validateChange",a.ticketIndex,z)},V=()=>l,B=z=>z.length<10?z.concat(new Array(10-z.length).fill({name:"",value:""})).map(ke=>({...ke})):z,v=async()=>{var z,ke;try{const x=we("091Q0203");T.value=!0;const O=l.ticketNo.indexOf("-"),Y=O!==-1?l.ticketNo.substring(0,O):l.ticketNo,fe=(ke=(z=await Ua({ticketNo:Y},x))==null?void 0:z.data)==null?void 0:ke.value;if(!(fe!=null&&fe.rtKTTaxes)||(fe==null?void 0:fe.rtKTTaxes.length)===0)return;l.taxs=[],((fe==null?void 0:fe.rtKTTaxes)??[]).forEach(ye=>{l.taxs.push({name:ye.taxType,value:ye.taxAmount})}),l.taxs=B(l.taxs),D()}finally{T.value=!1}},G=z=>{i("reuseTicket",z)},L=z=>{const ke=z.indexOf("("),x=z.indexOf("*");if(ke>-1&&x>-1){const O=Math.min(ke,x);return(z==null?void 0:z.substring(0,O))??""}else{if(ke===-1&&x>-1)return(z==null?void 0:z.substring(0,x))??"";if(ke>-1&&x===-1)return(z==null?void 0:z.substring(0,ke))??""}return z.trim()};Sa(()=>{[...l.segment.values()].forEach(z=>{let ke="";const x=[];z.forEach(O=>{ke=O.tktTag,x.push({...O,isAllowCheck:O.isAble==="1"})}),l.segment.set(ke,x)})});const ae=(z,ke)=>Nt(ke)?z.endsWith(".00")?z.slice(0,-3):z:z&&Number(z).toFixed(2),ie=(z,ke)=>z.map(x=>({name:x.name,value:ae(x.value,ke)}));Ct([()=>a.reuseTimes,()=>k.value],async()=>{if(!k.value||a.currentTicket!==l.ticketNo)return;const z=yt(k.value);l.commision=ae((z==null?void 0:z.commision)??"",(z==null?void 0:z.currency)??""),l.taxs=ie(z==null?void 0:z.taxs,(z==null?void 0:z.currency)??""),l.otherDeduction=ae((z==null?void 0:z.otherDeduction)??"",(z==null?void 0:z.currency)??""),l.totalAmount=ae((z==null?void 0:z.totalAmount)??"",(z==null?void 0:z.currency)??""),l.totalTaxs=ae((z==null?void 0:z.totalTaxs)??"",(z==null?void 0:z.currency)??""),l.currency=(z==null?void 0:z.currency)??"",l.payType=(z==null?void 0:z.payType)??"",l.etTag=(z==null?void 0:z.etTag)??"",l.remarkInfo=(z==null?void 0:z.remarkInfo)??"",l.creditCard=(z==null?void 0:z.creditCard)??"",l.otherDeductionRate=ae((z==null?void 0:z.otherDeductionRate)??"",(z==null?void 0:z.currency)??""),l.commisionRate=ae((z==null?void 0:z.commisionRate)??"",(z==null?void 0:z.currency)??""),D();const ke=await Q();i("validateChange",a.ticketIndex,ke)});const Ne=()=>{l.commision=ae(l.commision,l==null?void 0:l.currency),l.taxs=ie(l.taxs,l==null?void 0:l.currency),l.otherDeduction=ae(l==null?void 0:l.otherDeduction,l==null?void 0:l.currency),l.totalAmount=ae(l==null?void 0:l.totalAmount,l==null?void 0:l.currency),l.totalTaxs=ae(l==null?void 0:l.totalTaxs,l==null?void 0:l.currency),l.otherDeductionRate=ae((l==null?void 0:l.otherDeductionRate)??"",l==null?void 0:l.currency),l.commisionRate=ae(l==null?void 0:l.commisionRate,l==null?void 0:l.currency)},Ae=()=>{var z,ke,x,O,Y,fe,ye,Se,Fe,Be;((z=j.value)!=null&&z.includes("$$$")||(ke=j.value)!=null&&ke.includes("BSP"))&&(m.value.push(P.BSP),m.value.push(P.GPBSP)),!((x=j.value)!=null&&x.includes("BSP"))&&((O=j.value)!=null&&O.includes("GP"))&&m.value.push(P.GPBSP),((Y=j.value)!=null&&Y.includes("$$$")||(fe=j.value)!=null&&fe.includes("BOP"))&&m.value.push(P.BOPBSP),((ye=j.value)!=null&&ye.includes("$$$")||(Se=j.value)!=null&&Se.includes("CDS"))&&(m.value.push(P.CDS),m.value.push(P.GPCDS)),((Fe=j.value)!=null&&Fe.includes("$$$")||(Be=j.value)!=null&&Be.includes("本票"))&&m.value.push(P.ARL),l.ticketManagementOrganizationCode=A(l.ticketManagementOrganizationCode)};return mt(()=>{Ne(),Nt(l==null?void 0:l.currency)?ge(""):oe(),l.name=L(l.name),Ae(),q()}),{refundFormRef:b,refundFormData:l,fullscreenLoading:T,FORM_RULES:N,MAX_TAX_NUM:S,reuseValue:$,addTax:K,checkTax:E,calcAmount:se,changePayType:de,bindPaymentValue:H,deliverValid:q,getEditRefundData:V,getTaxAll:v,changeReuseTicket:G,commisionRateChange:te,isInternational:ce,ticketOrganizationList:m,isShowPrintNo:X,realRef:f,setRealRef:o,popoverRef:d,showTicketOriginalContainer:_,show:h,hide:y}},Wt=a=>(wt("data-v-159591ec"),a=a(),St(),a),Oi={class:"refund-form"},Fi={class:"relative"},Vi={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},Mi={key:0,class:"text-gray-3 text-xs absolute top-0 right-[0]"},Li={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},ji={class:"self-stretch justify-start items-start gap-5 inline-flex"},Bi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ii={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ui=Wt(()=>t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1)),Qi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},qi={class:"w-[84px] text-gray-3 text-xs shrink-0"},zi=Wt(()=>t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1)),Gi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] ticketManagementOrganizationCode"},Hi={key:1,class:"inline-block w-[12px]"},Yi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ki={class:"w-[84px] text-gray-3 text-xs shrink-0"},Wi={key:0,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Xi={key:1,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ji={class:"self-stretch justify-start items-start gap-5 inline-flex"},Zi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},el={class:"w-[84px] text-gray-3 text-xs shrink-0"},tl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},nl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},al={class:"w-[84px] text-gray-3 text-xs shrink-0"},sl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ol={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},il={class:"w-[84px] text-gray-3 text-xs shrink-0"},ll={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},rl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},cl={class:"w-[84px] text-gray-3 text-xs shrink-0"},ul={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},dl={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},pl={class:"self-stretch justify-start items-start gap-5 inline-flex"},fl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ml={class:"w-[84px] text-gray-3 text-xs shrink-0"},gl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},kl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},yl={class:"w-[84px] text-gray-3 text-xs shrink-0"},vl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},hl={class:"text-gray-3 text-xs shrink-0 w-[84px]"},_l={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},bl=Wt(()=>t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),xl={class:"self-stretch justify-start items-start gap-5 inline-flex"},Tl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] passenger-name"},Nl={class:"grow shrink basis-0 min-h-[32px] justify-start gap-1 flex mb-[10px]"},$l={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0"},Rl={class:"justify-start items-start text-gray-2 text-xs font-bold"},Cl={class:"justify-start gap-4 flex h-[20px]"},wl={class:"text-gray-2 text-xs leading-tight"},Sl={class:"flex items-center h-[20px] text-gray-3 font-normal text-xs leading-tight"},Pl={class:"justify-start gap-4 flex h-[20px]"},Al={class:"text-gray-2 text-xs leading-tight"},Dl={class:"justify-start items-start gap-5 inline-flex"},El={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require amount"},Ol={key:0,class:"not-required-tip"},Fl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require pay-type"},Vl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require refund-type"},Ml={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Ll={class:"self-stretch justify-start items-start gap-5 inline-flex"},jl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Bl={class:"w-full mb-[10px]"},Il={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},Ul={class:"ml-[20px]"},Ql={class:"text-gray-2 font-[700]"},ql={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},zl={class:"w-[20px] text-gray-3 text-xs shrink-0 leading-8"},Gl={class:"w-[40px] mr-[6px] shrink-0"},Hl={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},Yl={class:"self-stretch justify-start items-start gap-5 inline-flex"},Kl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Wl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Xl=Wt(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),Jl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Zl=Wt(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),er={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},tr={class:"self-stretch justify-start items-start gap-5 inline-flex"},nr={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},ar={class:"w-[72px] text-gray-3 text-xs shrink-0"},sr=Wt(()=>t("span",{class:"iconfont icon-info-circle-line absolute ml-[4px]"},null,-1)),or={class:"justify-start items-center flex text-gray-2 text-xs relative"},ir={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},lr={class:"text-gray-2 font-[700]"},rr=Wt(()=>t("span",null,[t("i",{class:"iconfont icon-info-circle-line text-[20px] font-normal ml-[10px] text-gray-4"})],-1)),cr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ur=Wt(()=>t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),dr=Le({__name:"ManualRefundForm",props:{refundData:{},ticketIndex:{},ticketList:{},reuseTicketInfo:{},reuseTimes:{},currentTicket:{}},emits:["validateChange","reuseTicket"],setup(a,{expose:i,emit:u}){const p=a,f=u,{refundFormRef:d,refundFormData:o,fullscreenLoading:_,FORM_RULES:h,MAX_TAX_NUM:y,reuseValue:T,addTax:b,checkTax:S,calcAmount:k,changePayType:$,bindPaymentValue:m,deliverValid:A,getEditRefundData:l,getTaxAll:P,changeReuseTicket:j,commisionRateChange:X,ticketOrganizationList:ce,isShowPrintNo:M}=Ei(p,f);return i({getEditRefundData:l}),(w,W)=>{const C=Zt,R=en,N=nt,K=ct,oe=vt,te=tn,D=Rn,E=et,pe=_t,ue=ut,ge=ht;return s(),g("div",Oi,[t("div",Fi,[Ue((s(),g("div",Vi,[J(n(w.$t("app.agentTicketRefund.refundInformationForm")),1)])),[[ge,e(_),void 0,{fullscreen:!0,lock:!0}]]),w.ticketList.length>1?(s(),g("div",Mi,[J(n(w.$t("app.agentTicketRefund.reuse"))+" ",1),r(R,{modelValue:e(T),"onUpdate:modelValue":W[0]||(W[0]=se=>Me(T)?T.value=se:null),class:"reuse w-[174px] mx-[6px]",onChange:e(j)},{default:c(()=>[(s(),re(C,{key:0,label:"",value:""})),(s(!0),g(_e,null,Re(w.ticketList.filter(se=>{var de;return se!==((de=e(o))==null?void 0:de.ticketNo)}),(se,de)=>(s(),re(C,{key:de+1,label:se,value:se},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),J(" "+n(w.$t("app.agentTicketRefund.refundInfo")),1)])):ee("",!0)]),r(ue,{ref_key:"refundFormRef",ref:d,model:e(o),"require-asterisk-position":"right"},{default:c(()=>{var se,de,H,Q,q,V,B,v,G,L,ae;return[t("div",Li,[t("div",ji,[t("div",Bi,[t("div",Ii,n(w.$t("app.agentTicketRefund.refundTicketNumber")),1),Ui]),t("div",Qi,[t("div",qi,n(w.$t("app.agentTicketRefund.rtType")),1),zi]),t("div",Gi,[r(K,{label:w.$t("app.refundForm.ticketManagementOrganizationCode"),prop:"ticketManagementOrganizationCode",rules:e(h).ticketManagementOrganizationCode},{default:c(()=>[r(R,{modelValue:e(o).ticketManagementOrganizationCode,"onUpdate:modelValue":W[1]||(W[1]=ie=>e(o).ticketManagementOrganizationCode=ie),disabled:!e(o).ticketManagementOrganizationCode,placeholder:e(o).ticketManagementOrganizationCode?"":w.$t("app.agentTicketQuery.noData")},{default:c(()=>[(s(!0),g(_e,null,Re(e(ce),ie=>(s(),re(C,{key:ie.value,label:ie.label,value:ie.value},{default:c(()=>[t("span",null,[e(o).ticketManagementOrganizationCode===ie.value?(s(),re(N,{key:0,size:12,class:"iconfont icon-right-line"})):(s(),g("span",Hi))]),J(" "+n(ie.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label","rules"])]),t("div",Yi,[t("div",Ki,n(w.$t("app.agentTicketRefund.prntNo")),1),e(M)?(s(),g("div",Wi,n(e(o).ticketManagementOrganizationCode==="CDS"?"-":((se=e(o))==null?void 0:se.printNo)??"-"),1)):(s(),g("div",Xi,"-"))])]),t("div",Ji,[t("div",Zi,[t("div",el,n(w.$t("app.agentTicketRefund.refundAgent")),1),t("div",tl,n(((de=e(o))==null?void 0:de.agent)??"-"),1)]),t("div",nl,[t("div",al,n(w.$t("app.agentTicketRefund.refundIataNo")),1),t("div",sl,n(((H=e(o))==null?void 0:H.iata)??"-"),1)]),t("div",ol,[t("div",il,n(w.$t("app.agentTicketRefund.refundOffice")),1),t("div",ll,n(((Q=e(o))==null?void 0:Q.office)??"-"),1)]),t("div",rl,[t("div",cl,n(w.$t("app.agentTicketRefund.refundDate")),1),t("div",ul,n(((q=e(o))==null?void 0:q.refundDate)??"-"),1)])])]),t("div",dl,[t("div",pl,[t("div",fl,[t("div",ml,n(w.$t("app.agentTicketRefund.refundAirlineSettlementCode")),1),t("div",gl,n(((V=e(o))==null?void 0:V.airline)??"-"),1)]),t("div",kl,[t("div",yl,n(w.$t("app.agentTicketRefund.refundTicketNo")),1),r(un,{"tkt-index":((B=e(o))==null?void 0:B.ticketNo)??"-","ticket-number":((v=e(o))==null?void 0:v.ticketNo)??"-","second-factor":(G=e(o))==null?void 0:G.secondFactor},null,8,["tkt-index","ticket-number","second-factor"])]),t("div",vl,[t("div",hl,n(w.$t("app.agentTicketRefund.numberOfCombinedTickets")),1),t("div",_l,n(e(o).tktType==="I"?(L=e(o))==null?void 0:L.conjunction:1),1)]),bl]),t("div",xl,[t("div",Tl,[r(K,{label:w.$t("app.agentTicketRefund.passName"),prop:"name",rules:e(h).psdName},{default:c(()=>[r(oe,{modelValue:e(o).name,"onUpdate:modelValue":W[2]||(W[2]=ie=>e(o).name=ie),clearable:"",onInput:W[3]||(W[3]=ie=>e(o).name=e(o).name.toUpperCase()),onBlur:e(A)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])]),t("div",Nl,[t("div",$l,n(w.$t("app.agentTicketRefund.refundSeg")),1),t("div",Rl,[e(o).tktType==="D"?(s(!0),g(_e,{key:0},Re([...e(o).segment.values()][0],(ie,Ne)=>(s(),g("div",{key:Ne,class:"inline-block leading-[32px] dom-tikcet"},[r(K,{prop:`segment[${Ne}]`},{default:c(()=>[t("div",Cl,[r(te,{modelValue:ie.isAllowCheck,"onUpdate:modelValue":Ae=>ie.isAllowCheck=Ae,disabled:"",label:ie},{default:c(()=>[t("div",wl,n((ie==null?void 0:ie.departureCode)??"")+"-"+n((ie==null?void 0:ie.arriveCode)??""),1)]),_:2},1032,["modelValue","onUpdate:modelValue","label"]),t("div",{class:Pe([ie.isAllowCheck?"text-brand-2":"text-gray-5","text-[14px] justify-center items-center flex relative right-[12px] h-[16px] w-[16px] top-[5px]"])},n(w.$t(`app.queryRefunds.number_${Ne+1}`)),3)])]),_:2},1032,["prop"])]))),128)):(s(!0),g(_e,{key:1},Re([...e(o).segment.values()],(ie,Ne)=>(s(),g("div",{key:Ne,class:Pe({"mb-[10px]":Ne<[...e(o).segment.values()].length-1})},[t("div",Sl,n(w.$t(`app.agentTicketRefund.couponNo_${Ne+1}`)),1),r(K,{prop:`segment[${Ne}]`},{default:c(()=>[t("div",Pl,[(s(!0),g(_e,null,Re(ie,(Ae,z)=>(s(),g("div",{key:z+"segmet",class:"justify-start items-center gap-[2px] flex h-[20px]"},[r(te,{modelValue:Ae.isAllowCheck,"onUpdate:modelValue":ke=>Ae.isAllowCheck=ke,label:Ae,disabled:""},{default:c(()=>[t("div",Al,n(Ae.departureCode)+"-"+n(Ae.arriveCode),1)]),_:2},1032,["modelValue","onUpdate:modelValue","label"]),t("div",{class:Pe([Ae.isAllowCheck?"text-brand-2":"text-gray-5","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},n(w.$t(`app.queryRefunds.number_${z+1}`)),3)]))),128))])]),_:2},1032,["prop"])],2))),128))])])]),t("div",Dl,[t("div",El,[r(K,{label:w.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",rules:e(h).totalAmount,class:Pe({"not-required-container":!e(o).totalAmount})},{default:c(()=>[r(oe,{modelValue:e(o).totalAmount,"onUpdate:modelValue":W[4]||(W[4]=ie=>e(o).totalAmount=ie),modelModifiers:{trim:!0},clearable:"",onBlur:W[5]||(W[5]=ie=>e(k)("totalAmount"))},null,8,["modelValue"]),e(o).totalAmount?ee("",!0):(s(),g("div",Ol,n(w.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),t("div",Fl,[r(K,{label:w.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:e(h).payType},{default:c(()=>[r(R,{modelValue:e(o).payType,"onUpdate:modelValue":W[6]||(W[6]=ie=>e(o).payType=ie),modelModifiers:{trim:!0},filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:w.$t("app.agentTicketRefund.paymentSel"),clearable:"",onChange:e($),onBlur:e(m)},{default:c(()=>[(s(!0),g(_e,null,Re(e(wn),(ie,Ne)=>(s(),re(C,{key:Ne,label:ie.label,value:ie.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),t("div",Vl,[r(K,{label:w.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:e(h).currency},{default:c(()=>[r(oe,{modelValue:e(o).currency,"onUpdate:modelValue":W[7]||(W[7]=ie=>e(o).currency=ie),modelModifiers:{trim:!0},clearable:"",onInput:W[8]||(W[8]=ie=>e(o).currency=e(o).currency.toUpperCase()),onBlur:e(A)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])])])]),t("div",Ml,[t("div",Ll,[t("div",jl,[r(K,{label:w.$t("app.agentTicketRefund.etTag")},{default:c(()=>[r(D,{modelValue:e(o).etTag,"onUpdate:modelValue":W[9]||(W[9]=ie=>e(o).etTag=ie),"inline-prompt":"","active-text":"Y","inactive-text":"N","active-value":"1","inactive-value":"0"},null,8,["modelValue"])]),_:1},8,["label"])])]),t("div",Bl,[t("div",Il,[t("div",null,[t("span",null,n(w.$t("app.agentTicketRefund.refundTax")),1),t("span",Ul,n(w.$t("app.fare.singleFare.totalTax")),1),t("span",Ql," "+n(e(o).currency)+" "+n(e(o).totalTaxs),1)]),t("div",null,[r(E,{link:"",type:"primary","data-gid":"091Q0203",size:"small",onClick:e(P)},{default:c(()=>[J(n(w.$t("app.agentTicketRefund.rtktTax")),1)]),_:1},8,["onClick"]),r(E,{link:"",type:"primary",size:"small",disabled:((ae=e(o).taxs)==null?void 0:ae.length)===e(y),onClick:e(b)},{default:c(()=>[J(n(w.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"])])]),t("div",ql,[(s(!0),g(_e,null,Re(e(o).taxs,(ie,Ne)=>(s(),g("div",{key:Ne,class:"grow shrink-0 basis-0 h-[32px] justify-start flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[t("div",zl,n(Ne+1),1),t("div",Gl,[r(K,{prop:"taxs."+Ne+".name",rules:e(h).taxName},{default:c(()=>[r(oe,{modelValue:ie.name,"onUpdate:modelValue":Ae=>ie.name=Ae,modelModifiers:{trim:!0},onInput:Ae=>ie.name=ie.name.toUpperCase(),onBlur:e(S)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"])]),_:2},1032,["prop","rules"])]),r(K,{prop:"taxs."+Ne+".value",rules:e(h).taxValue},{default:c(()=>[r(oe,{modelValue:ie.value,"onUpdate:modelValue":Ae=>ie.value=Ae,modelModifiers:{trim:!0},onBlur:e(S)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["prop","rules"])]))),128))])])]),t("div",Hl,[t("div",Yl,[t("div",Kl,[r(K,{label:w.$t("app.agentTicketRefund.commision"),prop:"commision",rules:e(h).commision},{default:c(()=>[r(oe,{modelValue:e(o).commision,"onUpdate:modelValue":W[10]||(W[10]=ie=>e(o).commision=ie),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:W[11]||(W[11]=ie=>e(k)("commision"))},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",Wl,[r(K,{label:w.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(h).commisionRate},{default:c(()=>[r(oe,{modelValue:e(o).commisionRate,"onUpdate:modelValue":W[12]||(W[12]=ie=>e(o).commisionRate=ie),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:W[13]||(W[13]=ie=>e(k)("commisionRate")),onInput:e(X)},null,8,["modelValue","onInput"]),Xl]),_:1},8,["label","rules"])]),t("div",Jl,[r(K,{label:w.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",rules:e(h).otherDeductionRate},{default:c(()=>[r(oe,{modelValue:e(o).otherDeductionRate,"onUpdate:modelValue":W[14]||(W[14]=ie=>e(o).otherDeductionRate=ie),modelModifiers:{trim:!0},placeholder:"1-100",onBlur:W[15]||(W[15]=ie=>e(k)("otherDeductionRate"))},null,8,["modelValue"]),Zl]),_:1},8,["label","rules"])]),t("div",er,[r(K,{label:w.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",rules:e(h).totalAmount},{default:c(()=>[r(oe,{modelValue:e(o).otherDeduction,"onUpdate:modelValue":W[16]||(W[16]=ie=>e(o).otherDeduction=ie),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:W[17]||(W[17]=ie=>e(k)("otherDeduction"))},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",tr,[t("div",nr,[t("div",ar,[J(n(w.$t("app.agentTicketRefund.remark"))+" ",1),r(pe,{placemant:"top",content:w.$t("app.agentTicketRefund.remarkTips")},{default:c(()=>[sr]),_:1},8,["content"])]),t("div",or,[r(K,{prop:"remarkInfo",rules:e(h).remarkInfo},{default:c(()=>[r(oe,{modelValue:e(o).remarkInfo,"onUpdate:modelValue":W[18]||(W[18]=ie=>e(o).remarkInfo=ie),clearable:"",placeholder:w.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:W[19]||(W[19]=ie=>e(o).remarkInfo=e(o).remarkInfo.toUpperCase()),onBlur:e(A)},null,8,["modelValue","placeholder","onBlur"])]),_:1},8,["rules"])])]),t("div",ir,[r(K,{label:w.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund"},{default:c(()=>[t("span",lr,n(e(o).currency)+" "+n(e(o).netRefund),1),r(pe,{placement:"top",effect:"dark"},{content:c(()=>[J(n(w.$t("app.agentTicketRefund.netRefundTip")),1)]),default:c(()=>[rr]),_:1})]),_:1},8,["label"])]),t("div",cr,[r(K,{label:w.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:e(h).creditCard},{default:c(()=>[r(oe,{modelValue:e(o).creditCard,"onUpdate:modelValue":W[20]||(W[20]=ie=>e(o).creditCard=ie),modelModifiers:{trim:!0},clearable:"",onInput:W[21]||(W[21]=ie=>e(o).creditCard=e(o).creditCard.toUpperCase()),onBlur:e(A)},null,8,["modelValue","onBlur"])]),_:1},8,["label","rules"])]),ur])])]}),_:1},8,["model"])])}}});const pr=dt(dr,[["__scopeId","data-v-159591ec"]]),fr=(a,i)=>{const u=I(0),p=I([]);return{currentTicketIndex:u,formRefs:p,validateChange:(_,h)=>{i("validateChange",a.passengerIndex,_,h)},getEditRefundDatas:()=>(p.value??[]).map(h=>h.getEditRefundData()),reuseTicket:_=>{i("reuseTicket",_)}}},mr={class:"border px-[8px] py-[4px] rounded-[1px] cursor-pointer ticket-number"},gr=Le({__name:"RefundItem",props:{ticketInfos:{},passengerIndex:{},ticketList:{},reuseTicketInfo:{},reuseTimes:{},activeName:{},currentTicket:{}},emits:["validateChange","reuseTicket"],setup(a,{expose:i,emit:u}){const p=a,f=u,{currentTicketIndex:d,formRefs:o,getEditRefundDatas:_,validateChange:h,reuseTicket:y}=fr(p,f);return i({getEditRefundDatas:_,currentTicketIndex:d}),(T,b)=>{const S=nt,k=Ha,$=Ya;return s(),re($,{modelValue:e(d),"onUpdate:modelValue":b[0]||(b[0]=m=>Me(d)?d.value=m:null),class:"ticket-container"},{default:c(()=>[(s(!0),g(_e,null,Re(T.ticketInfos,(m,A)=>(s(),re(k,{key:m.ticketNo,label:m.ticketNo,name:A},{label:c(()=>[t("div",mr,[J(n(e(Gt)(m.ticketNo))+" ",1),Ue(r(S,{class:"pass-icon"},{default:c(()=>[r(e(Bn))]),_:2},1536),[[kn,m.validate]])])]),default:c(()=>[t("div",null,[(s(),re(pr,{ref_for:!0,ref:l=>{l&&(e(o)[A]=l)},key:A,"refund-data":m.refundData,"ticket-index":A,"ticket-list":T.ticketList,"reuse-ticket-info":T.reuseTicketInfo,"reuse-times":T.reuseTimes,"current-ticket":T.currentTicket,onValidateChange:e(h),onReuseTicket:e(y)},null,8,["refund-data","ticket-index","ticket-list","reuse-ticket-info","reuse-times","current-ticket","onValidateChange","onReuseTicket"]))])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])}}});const kr=dt(gr,[["__scopeId","data-v-5e1166c7"]]),yr=je(()=>{const a=Rt().getters.userPreferences.backfieldEnName;return a===null||a}),vr=a=>yr.value&&!Ys.test(a)?a:"",hr=a=>{let i=[];return i=a.map(u=>({name:u.name,value:u.value})),i.length<10?i.concat(new Array(10-i.length).fill({name:"",value:""})).map(u=>({...u})):i},Ra=(a,i,u,p,f)=>{var b,S;const d=ot().format("DDMMMYY/HHmm").toUpperCase();let o="";if((b=a.ticket)!=null&&b.payType){const k=a.ticket.payType.toUpperCase();k.startsWith("CASH")?o="CASH":(k.startsWith("CC")||k.startsWith("TC"))&&(o="TC")}const _=(k,$)=>k&&k!=="0.00"?k:k==="0.00"&&$!=="0.00"?"":k??"",h=(k,$)=>$&&$!=="0.00"&&k==="0.00"?$:"",y=(k,$,m)=>{const A=Qe(Number(k),Number($??"0"));return Nt(m)?A.toString():Qe(Number(k),Number($??"0")).toFixed(2)};return{iata:a.iata,agent:a.agent,office:a.office,volunteer:"VOLUNTEER_MANUAL",createUser:a.operator,printNo:a.printerNo,marketAirline:a.ticket.marketAirline,currency:a.ticket.currency,name:vr(a.ticket.passengerNameSuffix??""),psgType:a.ticket.psgType,etTag:(S=a.ticket)==null?void 0:S.etTag,remark:"",remarkCode:"",remarkInfo:"",creditCard:"",conjunction:a.conjunction,airline:a.ticket.airline,tktType:a.ticket.tktType,payType:o,secondFactor:a.ticket.secondFactor,ticketNo:p,totalAmount:Number(a.conjunction)>1&&a.ticket.tktType==="D"?"":a.ticket.totalAmount,commision:_(a.ticket.commission,a.ticket.commissionRate),commisionRate:h(a.ticket.commission,a.ticket.commissionRate),otherDeduction:Nt(a.ticket.currency)?"0":"0.00",otherDeductionRate:"",netRefund:y(a.ticket.totalAmount,a.ticket.totalTaxs,a.ticket.currency),totalTaxs:a.ticket.totalTaxs??"0",taxs:hr(a.ticket.taxs??[]),rate:"0",receiptPrinted:a.receiptPrinted,segment:u,crsPnrNo:a.ticket.crsPnrNo,pnr:a.ticket.pnr,isCoupon:a.ticket.isCoupon,isDragonBoatOffice:i,refundDate:d,conjunctionTicketNos:f,ticketManagementOrganizationCode:a.ticketManagementOrganizationCode??""}},_r=a=>[...a.segment.values()].filter(u=>u.some(p=>p.isAllowCheck)).length.toString(),br=(a,i)=>a.map(p=>({modificationType:"ONLY_REFUND",prntNo:p.ticketManagementOrganizationCode==="CDS"?"":p.printNo,ticketManagementOrganizationCode:p.ticketManagementOrganizationCode??"",resultpre:{amount:{commision:p.commision!==""&&Number(p.commision)>0?p.commision:"0",commisionRate:p.commisionRate??"",netRefund:p.netRefund,otherDeduction:p.otherDeduction||"0",taxs:p.taxs.filter(f=>f.value!==""),totalAmount:p.totalAmount||"",totalTaxs:p.totalTaxs},conjunction:p.tktType==="D"?p.conjunction:_r(p),creditCard:p.creditCard,isCoupon:p.isCoupon,office:p.office,operator:p.createUser,remark:p.remarkInfo??"",segList:[],ticket:{airline:p.airline,crsPnrNo:i||p.crsPnrNo,currency:p.currency,etTag:p.etTag,marketAirline:p.marketAirline,name:Ft.encode(p.name.trim()),payType:p.payType.toUpperCase(),pnr:p.pnr,psgType:p.psgType,segment:[...p.segment.values()].flatMap(f=>f.map(d=>({arriveCode:d.arriveCode,departureCode:d.departureCode,e8Rph:d.e8Rph,isAble:d.isAble,rph:d.rph,tktTag:d.tktTag,isCheck:d.isAble}))),ticketNo:p.tktType==="I"?[...p.segment.keys()].join("-"):p.ticketNo??"",secondFactor:p.secondFactor,tktType:p.tktType},volunteer:"NON_VOLUNTEER_MANUAL"}})),xr=(a,i)=>{const{t:u}=Ze(),p=I(0),f=I(0),d=I([]),o=I([]),_=I(""),h=I(!1),y=I([]),T=I([]),b={ONLY_REFUND:{label:u("app.agentTicketRefund.onlyRefund",{pnrNo:a.pnrNo}),value:"ONLY_REFUND"},XE_PNR:{label:u("app.agentTicketRefund.xePnr",{pnrNo:a.pnrNo}),value:"XE_PNR"},DELETE_PASSENGER:{label:u("app.agentTicketRefund.deleteRefundPassenger",{pnrNo:a.pnrNo}),value:"DELETE_PASSENGER"},SPLIT_PASSENGER:{label:u("app.agentTicketRefund.splitPassenger",{pnrNo:a.pnrNo}),value:"SPLIT_PASSENGER"},DELETE_SPLIT_PASSENGER:{label:u("app.agentTicketRefund.deleteSplitPassenger",{pnrNo:a.pnrNo}),value:"DELETE_SPLIT_PASSENGER"}},S=je(()=>{const x=[];return d.value.forEach(O=>(O.ticketInfos??[]).forEach(Y=>x.push(Y.ticketNo??""))),x}),k=I(),$=I(0),m=I(!1),A=I([]),l=De(a.pnrNo),P=I([]),j=()=>{i("update:modelValue",!1)},X=(x,O,Y)=>{d.value[x].ticketInfos[O].validate=Y},ce=()=>{P.value=(y.value??[]).map(x=>({ticketNo:x.ticketNo,trfdNo:x.trfdno??""})),i("getTicketTrfdNoDetails",P.value),i("update:isShowTrfdNo",!0)},M=()=>{const x=[];return A.value.forEach(O=>{x.push(...O.getEditRefundDatas())}),{refundList:br(x,a.pnrNo)}},w=(x,O)=>{i("getManualRefundAmountDetail",x,O)},W=()=>{const x=we("091Q0202"),O=yt(a.ticketDetailList);i("update:ticketDetailList",[]),O.forEach(async Y=>{var ye;const fe={secondFactorCode:"NM",secondFactorValue:Y.ticket.name};await i("queryTicketDetail",((ye=Y.ticket.segment)==null?void 0:ye[0].tktTag)??"",fe,!0,x)})},C=()=>{w(T.value,h.value),W(),ce(),j()},R=async x=>{const O=`/v2/crs/pnrManagement?pnrNo=${x}`;await yn.setLink(O),Je.close(),j(),C()},N=async(x,O,Y)=>{const fe=Ce("p",{className:"flex flex-wrap"},[Ce("span",{className:"text-[18px]"},`${u("app.agentTicketRefund.handlePNR")}`),Ce("span",{className:"text-[18px] text-brand-2 cursor-pointer underline",onClick:()=>{R(Y?l.value:a.pnrNo)}},`${u("app.agentTicketRefund.order")}`),Ce("span",{className:"text-[18px]"},`${u("app.agentTicketRefund.handle")}`)]),ye=Ce("div",{className:"flex flex-wrap"},[Ce("p",{className:"text-[18px] w-full"},`${u("app.agentTicketRefund.cancelPNR",{pnrNo:Y?l.value:a.pnrNo})}`),Ce("p",{className:"text-[18px]"},[Ce("span",{className:"text-[18px]"},`${u("app.agentTicketRefund.orPlease")}`),Ce("span",{className:"text-[18px] text-brand-2 cursor-pointer underline",onClick:()=>{R(Y?l.value:a.pnrNo)}},`${u("app.agentTicketRefund.order")}`),Ce("span",{className:"text-[18px]"},`${u("app.agentTicketRefund.handle")}`)])]),Se=Ce("div",{className:"flex flex-wrap"},[Ce("p",{className:"text-[18px] w-full"},`${u("app.agentTicketRefund.deletePNRPassenger",{pnrNo:Y?l.value:a.pnrNo})}`),Ce("p",{className:"text-[18px]"},[Ce("span",{className:"text-[18px]"},`${u("app.agentTicketRefund.orPlease")}`),Ce("span",{className:"text-[18px] text-brand-2 cursor-pointer underline",onClick:()=>{R(Y?l.value:a.pnrNo)}},`${u("app.agentTicketRefund.order")}`),Ce("span",{className:"text-[18px]"},`${u("app.agentTicketRefund.handle")}`)])]),Fe=u("app.agentTicketRefund.refundSuccess"),Be=x,Ke=O?`${u("app.agentTicketRefund.cancelPnr")}`:`${u("app.agentTicketRefund.xePassenger")}`;return lo(Fe,Ke,Be,x?O?ye:Se:fe)},K=async()=>h.value?(await lt({message:u("app.agentTicketRefund.refundSuccess"),type:"success"}),!1):(await xa(y.value??[],u("app.agentTicketRefund.partFaild"),"error"),!0),oe=(x,O)=>({passengerName:x,ticketNo:O}),te=x=>{const O=[];return(a.allPassengers??[]).forEach(Y=>{var fe,ye,Se;x&&Y.isChecked&&O.push(oe(Y.passengerName,((fe=Y.ticketNos)==null?void 0:fe[0])??"")),x&&Y.isChecked&&Y.infants&&Y.infants.isChecked&&O.push(oe(Y.infants.passengerName,((ye=Y.infants.ticketNos)==null?void 0:ye[0])??"")),!x&&!Y.isChecked&&Y.infants&&Y.infants.isChecked&&O.push(oe(Y.infants.passengerName,((Se=Y.infants.ticketNos)==null?void 0:Se[0])??""))}),O},D=x=>({pnr:x?l.value:a.pnrNo,passengerInfos:te(x)}),E=async x=>{var O,Y;try{m.value=!0;const fe=D(x),ye=we("091Q0202");return(Y=(O=await Eo(fe,ye))==null?void 0:O.data)==null?void 0:Y.value}finally{m.value=!1}},pe=async()=>{if(["DELETE_PASSENGER","DELETE_SPLIT_PASSENGER"].includes(_.value)){const O=await E(!1)??!1;await N(O,!1,!1).then(async()=>{await L(),C()}).catch(()=>{C()})}else C()},ue=async x=>{let O=!0;x&&(O=await G()??!1),O&&_.value==="DELETE_SPLIT_PASSENGER"?pe():C()},ge=async()=>{if(await K()){C();return}try{const O=["XE_PNR","SPLIT_PASSENGER","DELETE_SPLIT_PASSENGER"].includes(_.value);if(O){const Y=await E(!0)??!1;N(Y,O,!0).then(()=>{ue(O)}).catch(()=>{pe()})}else _.value==="DELETE_PASSENGER"?pe():C()}catch{C()}},se=async()=>{var Y,fe,ye,Se;const x=Date.now();let O=null;try{m.value=!0;const Fe=we("091Q0202"),Be=M(),Ke=(fe=(Y=await Do(Be,Fe))==null?void 0:Y.data)==null?void 0:fe.value;h.value=((ye=Ke==null?void 0:Ke.data)==null?void 0:ye.status)==="ALL_SUCCESS",y.value=((Se=Ke==null?void 0:Ke.data)==null?void 0:Se.passengerStatuses)??[],T.value=(Be==null?void 0:Be.refundList)??[],O=Date.now(),ws(x,O,"手工退票")}finally{m.value=!1}ge()},de=x=>{if(!x){k.value=null;return}const O=[];A.value.forEach(Y=>{O.push(...Y.getEditRefundDatas())}),k.value=yt(O.find(Y=>Y.ticketNo===x)),$.value+=1},H=()=>{const x=(d.value??[]).every(O=>(O.ticketInfos??[]).every(Y=>Y.validate));if(!x){const O=(d.value??[]).flatMap(Y=>(Y.ticketInfos??[]).map(fe=>({ticketNo:fe.ticketNo??"",passengerName:Y.name??"",success:fe.validate})));xa(O,u("app.agentTicketRefund.pleaseCompleteInfo"),"warn")}return x},Q=()=>{const x=[];return(a.allPassengers??[]).map(O=>{O.isChecked&&x.push({fullName:O.passengerName,paxId:Number(O.index.replace("P","")),unMinor:!1,unMinorAge:0})}),x},q=()=>({orderId:"",count:0,passengerRecordLocator:a.pnrNo,travellers:Q()}),V=async()=>{var x,O;try{m.value=!0;const Y=we("091Q0202"),fe=q(),ye=await ta(fe,Y);l.value=((O=(x=ye.data.value)==null?void 0:x.splitedOrder)==null?void 0:O.passengerRecordLocator)??"",lt({message:u("app.agentTicketRefund.splitSuccess"),type:"success"})}finally{m.value=!1}},B=()=>{const x=[];return(a.ticketDetailList??[]).forEach(O=>{O.ticket.psgType==="INF"&&x.push({name:Ft.encode(O.ticket.name),psgType:O.ticket.psgType,ticketNo:O.ticket.ticketNo})}),x},v=(x,O)=>({pnrHandleType:O?"C":"D",pnrNo:x?a.pnrNo:"",xePnr:O?l.value:"",passengerInfoList:B()}),G=async()=>{var x;try{m.value=!0;const O=we("091Q0202"),Y=v(!1,!0),ye=((x=(await bn(Y,O)).data.value)==null?void 0:x.xePnrExecutionStatus)==="S";return lt({message:u(ye?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:ye?"success":"error"}),ye}catch{C()}finally{m.value=!1}},L=async()=>{var x;try{m.value=!0;const O=we("091Q0202"),Y=v(!0,!1),ye=((x=(await bn(Y,O)).data.value)==null?void 0:x.deleteInfantExecutionStatus)==="S";lt({message:u(ye?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:ye?"success":"error"})}catch{C()}finally{m.value=!1}},ae=async x=>{let O=a.isXepnr?u("app.agentTicketRefund.notDeletePnrTip"):u("app.agentTicketRefund.notDeletePassengerTip");x&&(O=u("app.agentTicketRefund.splitRefundPassenger")),await Je.confirm(Ce("div",{className:"whitespace-pre-line"},O),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:u("app.agentTicketRefund.sure"),cancelButtonText:u("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})},ie=async()=>{H()&&(_.value==="ONLY_REFUND"&&await ae(!1),["SPLIT_PASSENGER","DELETE_SPLIT_PASSENGER"].includes(_.value)&&(await ae(!0),await V()),se())},Ne=x=>[...new Set((x??[]).map(O=>O.tktTag))],Ae=()=>((a==null?void 0:a.ticketDetailList)??[]).map(x=>{const O=[],Y=Ne(x.ticket.segment);if(x.ticket.tktType==="I"){const fe=new Map;Y.forEach(Fe=>{const Be=(x.ticket.segment??[]).filter(Ke=>Ke.tktTag===Fe);fe.set(Fe,Be)});const ye=(Y==null?void 0:Y.length)>1?Y:[],Se=Ra(x,a.isDragonBoatOffice,fe,x.ticket.ticketNo,ye);O.push({validate:!0,ticketNo:x.ticket.ticketNo,refundData:Se})}else Y.forEach(fe=>{const ye=(x.ticket.segment??[]).filter(Ke=>Ke.tktTag===fe);if(!(ye??[]).some(Ke=>Ka.includes(Ke.ticketStatus??"")))return;const Fe=new Map;Fe.set(fe,ye);const Be=Ra(x,a.isDragonBoatOffice,Fe,fe,[]);O.push({ticketNo:fe,refundData:Be,validate:!0})});return{ticketInfos:O,tktType:x.ticket.tktType,psgType:x.ticket.psgType,name:x.ticket.passengerNameSuffix??""}}),z=()=>(a.allPassengers??[]).some(x=>!x.isChecked&&x.infants&&x.infants.isChecked),ke=()=>{const x=d.value.every(O=>O.psgType==="INF");o.value.push(b.ONLY_REFUND),_.value=b.ONLY_REFUND.value,a.pnrNo&&(a.isXepnr&&o.value.push(b.XE_PNR),!a.isXepnr&&x&&o.value.push(b.DELETE_PASSENGER),!a.isXepnr&&!x&&!z()&&o.value.push(b.SPLIT_PASSENGER),!a.isXepnr&&!x&&z()&&o.value.push(b.DELETE_SPLIT_PASSENGER))};return mt(()=>{d.value=Ae(),ke()}),Cs(()=>{A.value=[]}),{passengers:d,activeName:p,refundOperation:o,closeDialog:j,refundType:_,refundItemRefs:A,validateChange:X,submitRefund:ie,fullscreenLoading:m,ticketList:S,reuseTicket:de,reuseTicketInfo:k,reuseTimes:$,tabIndex:f}},Ja=a=>(wt("data-v-fd4c8e8c"),a=a(),St(),a),Tr=Ja(()=>t("i",{class:"iconfont icon-close"},null,-1)),Nr=[Tr],$r={class:"title relative"},Rr={class:"text-[16px] max-w-[150px] font-bold cursor-pointer inline-block whitespace-nowrap overflow-hidden text-ellipsis"},Cr=Ja(()=>t("span",{class:"line"},null,-1)),wr={class:"flex justify-center pt-[10px] w-full mt-[10px] footer items-center crs-btn-dialog-ui"},Sr=Le({__name:"ManualRefundDialog",props:{packageData:{},allPassengers:{},deviceNum:{},pnrNo:{},tktNo:{},ticketDetailList:{},isXepnr:{type:Boolean},isDragonBoatOffice:{type:Boolean}},emits:["update:ticketDetailList","update:modelValue","queryTicketDetail","getManualRefundAmountDetail","getTicketTrfdNoDetails","reuseTicket"],setup(a,{emit:i}){const u=a,p=i,{passengers:f,activeName:d,refundOperation:o,closeDialog:_,refundType:h,refundItemRefs:y,validateChange:T,submitRefund:b,fullscreenLoading:S,ticketList:k,reuseTicket:$,reuseTicketInfo:m,reuseTimes:A}=xr(u,p);return(l,P)=>{const j=_t,X=Ha,ce=Ya,M=Dt,w=Et,W=et,C=rt,R=$n("debounce"),N=ht;return s(),re(C,{width:"1040",title:l.$t("app.agentTicketRefund.manualRefundBtn"),"show-close":!1,"close-on-click-modal":!1,class:"manual-refund-dialog","align-center":"true",onClose:e(_)},{default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:P[0]||(P[0]=(...K)=>e(_)&&e(_)(...K))},Nr),Ue((s(),g("div",null,[r(ce,{modelValue:e(d),"onUpdate:modelValue":P[1]||(P[1]=K=>Me(d)?d.value=K:null),class:"tabs-container"},{default:c(()=>[(s(!0),g(_e,null,Re(e(f),(K,oe)=>(s(),re(X,{key:oe,name:oe,class:"test"},{label:c(()=>[t("span",$r,[r(j,{effect:"dark",content:K.name,placement:"top"},{default:c(()=>[t("span",Rr,n(K.name),1)]),_:2},1032,["content"]),Cr])]),default:c(()=>{var te,D,E,pe,ue,ge;return[(s(),re(kr,{ref_for:!0,ref:se=>{se&&(e(y)[oe]=se)},key:oe,"ticket-infos":K.ticketInfos,lazy:!0,"passenger-index":oe,"ticket-list":e(k),"reuse-ticket-info":e(m),"reuse-times":e(A),"active-name":e(d),"current-ticket":((ge=(ue=(D=(te=e(f))==null?void 0:te[e(d)])==null?void 0:D.ticketInfos)==null?void 0:ue[((pe=(E=e(y))==null?void 0:E[e(d)??0])==null?void 0:pe.currentTicketIndex)??0])==null?void 0:ge.ticketNo)??"",onValidateChange:e(T),onReuseTicket:e($)},null,8,["ticket-infos","passenger-index","ticket-list","reuse-ticket-info","reuse-times","active-name","current-ticket","onValidateChange","onReuseTicket"]))]}),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]),t("div",wr,[r(w,{modelValue:e(h),"onUpdate:modelValue":P[2]||(P[2]=K=>Me(h)?h.value=K:null)},{default:c(()=>[(s(!0),g(_e,null,Re(e(o),K=>(s(),re(M,{key:K.value,label:K.value},{default:c(()=>[J(n(K.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),Ue((s(),re(W,{type:"primary"},{default:c(()=>[J(n(l.$t("app.agentTicketRefund.refund")),1)]),_:1})),[[R,e(b)]])])])),[[N,e(S)]])]),_:1},8,["title","onClose"])}}});const Pr=dt(Sr,[["__scopeId","data-v-fd4c8e8c"]]),Ar=(a,i)=>{const u=je(()=>a.allPassengerList),p=o=>o.ticketNos.length===0||a.refundTicketSuccess,f=o=>{var _;return p(o)||o.isChecked&&((_=o==null?void 0:o.infants)==null?void 0:_.isChecked)},d=async(o,_,h)=>{!_&&(h!=null&&h.infants)&&(h.infants.isChecked=!0),!(h.isChecked&&_)&&await i("queryTicketAndCalcAmount",o)};return mt(async()=>{}),{allPnrPassengerList:u,judgeIsDisabled:p,infantIsDisabled:f,handleCheckPassenger:d}},Dr={class:"new-refund-passenger-card"},Er={class:"text-gray-1 text-sm font-bold leading-normal pt-2.5 pb-[4px]"},Or={class:"flex flex-row flex-wrap"},Fr={class:"text-center text-gray-3 text-xs font-normal leading-tight px-1 py-[2px] bg-gray-7 rounded-sm"},Vr={key:0,class:"iconfont icon-connect mr-[-5px] ml-[-5px] mt-[5px]"},Mr={class:"text-center text-gray-3 text-xs font-normal leading-tight px-1 py-[2px] bg-gray-7 rounded-sm"},Lr=Le({__name:"Passenger",props:{allPassengerList:{},clacAmountInfo:{},refundTicketSuccess:{type:Boolean}},emits:["queryTicketDetail","queryTicketAndCalcAmount"],setup(a,{emit:i}){const u=a,p=i,{allPnrPassengerList:f,judgeIsDisabled:d,handleCheckPassenger:o}=Ar(u,p);return(_,h)=>{const y=_t,T=tn;return s(),g("div",Dr,[t("div",Er,n(_.$t("app.agentTicketRefund.rtPassenger")),1),t("div",Or,[(s(!0),g(_e,null,Re(e(f),(b,S)=>(s(),g("div",{key:S+b.index,class:Pe([[b.isChecked?"refund-passenger":"grey-connect"],"flex"])},[t("div",{class:Pe(["flex items-center rounded border mb-2.5",[b.isChecked?"border-brand-2 bg-brand-4":"border-3",b.infants?"mr-0":"mr-2.5"]])},[r(T,{modelValue:b.isChecked,"onUpdate:modelValue":k=>b.isChecked=k,disabled:e(d)(b),onChange:k=>e(o)(S,!1,b)},{default:c(()=>[r(y,{effect:"dark",placement:"top",content:b.passengerNameSuffix},{default:c(()=>[t("span",{class:Pe(["text-gray-1 text-xs font-bold leading-tight mr-1 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e($t)(b.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(b.passengerNameSuffix),3)]),_:2},1032,["content"]),t("div",Fr,n(e($t)(b.specialPassengerType)),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])],2),b.infants?(s(),g("div",Vr)):ee("",!0),b.infants?(s(),g("div",{key:1,class:Pe(["flex items-center rounded border mr-2.5 mb-2.5",[b.infants.isChecked?"border-brand-2 bg-brand-4":"border-3"]])},[r(T,{modelValue:b.infants.isChecked,"onUpdate:modelValue":k=>b.infants.isChecked=k,disabled:e(d)(b.infants)||b.isChecked,onChange:k=>e(o)(S,!0,b)},{default:c(()=>[r(y,{effect:"dark",placement:"top",content:b.infants.passengerNameSuffix},{default:c(()=>[t("span",{class:Pe(["text-gray-1 text-xs font-bold leading-tight mr-1 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e($t)(b.infants.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(b.infants.passengerNameSuffix),3)]),_:2},1032,["content"]),t("div",Mr,n(e($t)(b.infants.specialPassengerType)),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])],2)):ee("",!0)],2))),128))])])}}});const jr=(a,i)=>{const u=I(0),p=I(0),f=I(""),d=I(!1),o=()=>{d.value=!0,a("get-calc-info",d.value)},_=()=>{var y,T;if(u.value=0,p.value=0,f.value=((T=(y=i.amountData)==null?void 0:y[0])==null?void 0:T.currency)??"",i.isAutoRefundFinished){(i.batchAutoAmount??[]).forEach(b=>{u.value=Qe(Number((b==null?void 0:b.otherDeduction)??"0"),u.value),p.value=Qe(Number((b==null?void 0:b.netRefund)??"0"),p.value)});return}(i.amountData??[]).forEach(b=>{var S,k;u.value=Qe(Number(((S=b==null?void 0:b.amount)==null?void 0:S.otherDeduction)??"0"),u.value),p.value=Qe(Number(((k=b==null?void 0:b.amount)==null?void 0:k.netRefund)??"0"),p.value)})},h=()=>{var y,T;u.value=0,p.value=0,f.value=((T=(y=i.manualRefundAmountInfo)==null?void 0:y[0])==null?void 0:T.currency)??"",(i.manualRefundAmountInfo??[]).forEach(b=>{u.value=Qe(Number((b==null?void 0:b.otherDeduction)??"0"),u.value),p.value=Qe(Number((b==null?void 0:b.netRefund)??"0"),p.value)})};return Ct([()=>i.amountData,()=>i.isFinishManualRefund],()=>{i.isFinishManualRefund?h():_()},{deep:!0}),mt(()=>{d.value=!1}),{otherDeduction:u,totalAmount:p,currency:f,isAlreadyClickCalcAmount:d,clacAmount:o}},Br={class:"w-[1820px] h-8 justify-start items-center gap-2.5 inline-flex mt-[14px]"},Ir={class:"justify-start items-center gap-0.5 flex"},Ur={class:"text-gray-1 text-sm font-bold leading-snug"},Qr={key:0,class:"text-red-1 text-base font-bold leading-normal"},qr={key:0,class:"text-red-1 text-base font-bold leading-normal"},zr={key:1,class:"text-red-1 text-base font-bold leading-normal"},Gr={key:2,class:"text-red-1 text-base font-bold leading-normal"},Hr={class:"justify-start items-center gap-0.5 flex"},Yr={class:"text-gray-1 text-sm font-bold leading-snug"},Kr={key:0,class:"text-red-1 text-base font-bold leading-normal"},Wr={key:0,class:"text-red-1 text-base font-bold leading-normal"},Xr={key:1,class:"text-red-1 text-base font-bold leading-normal"},Jr={key:2,class:"text-red-1 text-base font-bold leading-normal"},Zr={key:0,class:"px-2.5 py-[5px] justify-center items-center gap-2.5 flex"},ec=Le({__name:"RefundAmount",props:{amountData:{},status:{},isAutoRefundFinished:{type:Boolean},isFinishManualRefund:{type:Boolean},manualRefundAmountInfo:{},isCanRefund:{type:Boolean},batchAutoAmount:{}},emits:["get-calc-info"],setup(a,{emit:i}){const u=i,p=a,{otherDeduction:f,totalAmount:d,currency:o,isAlreadyClickCalcAmount:_,clacAmount:h}=jr(u,p);return(y,T)=>{const b=et;return s(),g("div",null,[t("div",Br,[t("div",Ir,[t("div",Ur,n(y.$t("app.agentTicketRefund.charge")),1),y.isFinishManualRefund?(s(),g("div",Qr,n(e(o))+" "+n(e(f).toFixed(2)),1)):(s(),g(_e,{key:1},[!e(_)||e(_)&&!y.status?(s(),g("div",qr,"--")):ee("",!0),e(_)&&y.status&&y.status!=="SUCCESS"?(s(),g("div",zr,n(y.$t("app.agentTicketRefund.calcFail")),1)):ee("",!0),e(_)&&y.status==="SUCCESS"?(s(),g("div",Gr,n(e(o))+" "+n(e(f).toFixed(2)),1)):ee("",!0)],64))]),t("div",Hr,[t("div",Yr,n(y.$t("app.agentTicketRefund.TotalAmountToBeRefunded")),1),y.isFinishManualRefund?(s(),g("div",Kr,n(e(o))+" "+n(e(d).toFixed(2)),1)):(s(),g(_e,{key:1},[!e(_)||e(_)&&!y.status?(s(),g("div",Wr,"--")):ee("",!0),e(_)&&y.status&&y.status!=="SUCCESS"?(s(),g("div",Xr,n(y.$t("app.agentTicketRefund.calcFail")),1)):ee("",!0),e(_)&&y.status==="SUCCESS"?(s(),g("div",Jr,n(e(o))+" "+n(e(d).toFixed(2)),1)):ee("",!0)],64))]),!y.isAutoRefundFinished&&!y.isFinishManualRefund?(s(),g("div",Zr,[r(b,{disabled:!y.isCanRefund,"data-gid":"091Q0103",onClick:e(h)},{default:c(()=>[J(n(e(_)?y.$t("app.agentTicketRefund.recalc"):y.$t("app.agentTicketRefund.calcAmount")),1)]),_:1},8,["disabled","onClick"])])):ee("",!0)])])}}});const tc=dt(ec,[["__scopeId","data-v-208a0628"]]),sn=Pa.global.t,nc={BSP:{label:sn("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:sn("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:sn("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:sn("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:sn("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:sn("app.agentTicketQuery.OWNTicket"),value:"ARL"}},ac={key:0},sc={class:"font-bold leading-5 text-gray-0"},oc={class:"flex tx-xs text-gray-6"},ic={class:"w-[42px] leading-6"},lc={class:"leading-6"},rc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},cc={class:"w-[42px] leading-6"},uc={class:"leading-6"},dc={key:1},pc={class:"font-bold leading-5 text-gray-0"},fc={class:"flex tx-xs text-gray-6"},mc={class:"w-[42px] leading-6"},gc={class:"leading-6"},kc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},yc={class:"w-[42px] leading-6"},vc={class:"leading-6"},hc={key:1,class:"flex tx-xs"},_c=t("div",{class:"w-[42px] leading-6"},"-",-1),bc=t("div",{class:"leading-6"},"-",-1),xc=[_c,bc],Tc={key:2},Nc={class:"font-bold leading-5 text-gray-0"},$c={class:"flex tx-xs text-gray-6"},Rc={class:"w-[42px] leading-6"},Cc={class:"leading-6"},wc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},Sc={class:"w-[42px] leading-6"},Pc={class:"leading-6"},Ac={key:3},Dc={class:"text-gray-0"},Hn=Le({__name:"RefundTaxDetailPopover",props:{ticket:{},taxs:{},isFinishManualRefund:{type:Boolean},partSuccess:{type:Boolean},isAutoRefundFinished:{type:Boolean},batchAutoTaxes:{}},setup(a){return(i,u)=>{const p=Cn;return s(),re(p,{placement:"top",width:150,trigger:"hover","popper-class":`refund-tax-detail-popper_${i.ticket.ticketNo} refund-tax-detail-popper`},{reference:c(()=>[Yt(i.$slots,"tax-deatil")]),default:c(()=>{var f,d;return[((f=i.ticket.taxs)==null?void 0:f.length)>0&&!(i.isFinishManualRefund||i.partSuccess)&&!i.isAutoRefundFinished?(s(),g("div",ac,[t("div",sc,n(i.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",oc,[t("div",ic,n(i.$t("app.agentTicketRefund.taxes")),1),t("div",lc,n(i.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",rc,[(s(!0),g(_e,null,Re(i.ticket.taxs,(o,_)=>(s(),g("div",{key:_,class:"flex tx-xs"},[t("div",cc,n(o.name),1),t("div",uc,n(i.ticket.currency)+" "+n(Number(o.value).toFixed(2)),1)]))),128))])])])):i.isFinishManualRefund||i.partSuccess?(s(),g("div",dc,[t("div",pc,n(i.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",fc,[t("div",mc,n(i.$t("app.agentTicketRefund.taxes")),1),t("div",gc,n(i.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",kc,[(i.taxs??[]).length!==0?(s(!0),g(_e,{key:0},Re(i.taxs,(o,_)=>(s(),g("div",{key:_,class:"flex tx-xs"},[t("div",yc,n(o.name),1),t("div",vc,n(i.ticket.currency)+" "+n(Number(o.value).toFixed(2)),1)]))),128)):(s(),g("div",hc,xc))])])])):i.batchAutoTaxes&&((d=i.batchAutoTaxes)==null?void 0:d.length)>0&&i.isAutoRefundFinished?(s(),g("div",Tc,[t("div",Nc,n(i.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",$c,[t("div",Rc,n(i.$t("app.agentTicketRefund.taxes")),1),t("div",Cc,n(i.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",wc,[(s(!0),g(_e,null,Re(i.batchAutoTaxes,(o,_)=>(s(),g("div",{key:_,class:"flex tx-xs"},[t("div",Sc,n(o.name),1),t("div",Pc,n(i.ticket.currency)+" "+n(Number(o.value).toFixed(2)),1)]))),128))])])])):(s(),g("div",Ac,[t("span",Dc,n(i.$t("app.agentTicketRefund.noData")),1)]))]}),_:3},8,["popper-class"])}}});const Ec=(a,i)=>{const{t:u}=Ze(),p=Rt(),f=je(()=>{var P;return(P=p.state.user)==null?void 0:P.entityType}),d=I(),o=I(""),_=I([]),h=it({printerNo:"",ticketManagementOrganizationCode:""}),y={ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],printerNo:[{required:!0,message:u("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Ht,trigger:"blur",message:u("app.ticketStatus.deviceError")}]},T=je(()=>!["CDS","GPCDS"].includes(h.ticketManagementOrganizationCode)),b=P=>{o.value=P},S=P=>{var X,ce;return(_.value??[]).some(M=>P===M.value)&&P?P:((ce=(X=_.value)==null?void 0:X[0])==null?void 0:ce.value)??""},k=async()=>{d.value.validate(async P=>{if(!P)return;const j=we("091T0104");i("openDialog",j,h.printerNo,o.value,h.ticketManagementOrganizationCode),i("update:modelValue",!1)})},$=()=>{i("update:modelValue",!1)},m=()=>{T.value||(h.printerNo="")},A={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},l=()=>{var P,j,X,ce,M,w,W,C,R,N;((P=f.value)!=null&&P.includes("$$$")||(j=f.value)!=null&&j.includes("BSP"))&&(_.value.push(A.BSP),_.value.push(A.GPBSP)),!((X=f.value)!=null&&X.includes("BSP"))&&((ce=f.value)!=null&&ce.includes("GP"))&&_.value.push(A.GPBSP),((M=f.value)!=null&&M.includes("$$$")||(w=f.value)!=null&&w.includes("BOP"))&&_.value.push(A.BOPBSP),((W=f.value)!=null&&W.includes("$$$")||(C=f.value)!=null&&C.includes("CDS"))&&(_.value.push(A.CDS),_.value.push(A.GPCDS)),((R=f.value)!=null&&R.includes("$$$")||(N=f.value)!=null&&N.includes("本票"))&&_.value.push(A.ARL),h.ticketManagementOrganizationCode=S(a.ticketManagementOrganizationCode??"")};return mt(async()=>{l()}),{formDate:d,printNoFrom:h,PRINTER_NO_RULES:y,ticketOrganizationList:_,confirmPrinterNo:k,closeDialog:$,deliverPrintType:b,isShowPrintNo:T,changeTicketManagementOrganizationCode:m}},Oc=t("i",{class:"iconfont icon-close"},null,-1),Fc=[Oc],Vc={class:"carType-option-panel"},Za=Le({__name:"PrintNoDialog",props:{ticketManagementOrganizationCode:{}},emits:["openDialog","update:modelValue","update:showTicketRefundFormDialog"],setup(a,{emit:i}){const u=i,p=a,{formDate:f,printNoFrom:d,PRINTER_NO_RULES:o,ticketOrganizationList:_,confirmPrinterNo:h,closeDialog:y,deliverPrintType:T,isShowPrintNo:b,changeTicketManagementOrganizationCode:S}=Ec(p,u);return(k,$)=>{const m=nt,A=Zt,l=en,P=ct,j=ut,X=et,ce=rt;return s(),re(ce,{title:k.$t("app.ticketStatus.selectTicket"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(y)},{footer:c(()=>[t("div",null,[r(X,{type:"primary",onClick:$[4]||($[4]=M=>e(h)())},{default:c(()=>[J(n(k.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),r(X,{onClick:e(y)},{default:c(()=>[J(n(k.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:$[0]||($[0]=(...M)=>e(y)&&e(y)(...M))},Fc),r(j,{ref_key:"formDate",ref:f,model:e(d),rules:e(o),"label-position":"left","require-asterisk-position":"right"},{default:c(()=>[r(P,{prop:"ticketManagementOrganizationCode",label:k.$t("app.agentTicketQuery.ticketOrganization")},{default:c(()=>[r(l,{modelValue:e(d).ticketManagementOrganizationCode,"onUpdate:modelValue":$[1]||($[1]=M=>e(d).ticketManagementOrganizationCode=M),class:"ticket-management-organization",disabled:e(d).ticketManagementOrganizationCode==="",placeholder:e(d).ticketManagementOrganizationCode===""?k.$t("app.agentTicketQuery.noData"):"",onChange:e(S)},{default:c(()=>[(s(!0),g(_e,null,Re(e(_),M=>(s(),re(A,{key:M.value,label:M.label,value:M.value},{default:c(()=>[t("div",Vc,[t("div",{class:Pe(e(d).ticketManagementOrganizationCode===M.value?"show-select":"hidden-select")},[r(m,null,{default:c(()=>[r(e(Yn))]),_:1})],2),t("span",null,n(M.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["label"]),e(b)?(s(),re(P,{key:0,prop:"printerNo",label:k.$t("app.ticketStatus.deviceNum")},{default:c(()=>[r(Kt,{modelValue:e(d).printerNo,"onUpdate:modelValue":[$[2]||($[2]=M=>e(d).printerNo=M),$[3]||($[3]=M=>e(f).validateField("printerNo"))],"select-class":"w-[340px]",onDeliverPrintType:e(T)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):ee("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const Mc=a=>{const{t:i}=Ze(),u=De(!1),p=I([]),f=I([]),d=Q=>q=>{q&&(p.value[Q]={el:q.$el||q})},o=Q=>q=>{q&&(f.value[Q]=q)},_=I([]),h=Q=>{_.value[Q]=!0},y=Q=>{_.value[Q]=!1},T=De(!1),b=De(!1),S=De(""),k=De(""),$=I({ticketType:"",ticketNo:""}),m=I({}),A=I(a.ticketDetailList??[]),l=(Q,q)=>{const V=new Map;return Q.forEach(B=>{var G;const v=(G=B[q])==null?void 0:G.replace(/-/,"");V.set(v,V.get(v)||[]),V.get(v).push(B)}),V},P=je(()=>{const Q=a.isAutoRefundFinished?yt(a.batchAutoAmount??[]):yt(a.ticketAmountList??[]).map(q=>q.amount);return l(Q,"ticketNo")}),j=je(()=>{const Q=new Map;return(yt(a.ticketTrfdNoDetails)??[]).forEach(V=>{var B,v,G,L;if(((B=Gt(V.ticketNo))==null?void 0:B.length)===14)Q.set((v=V.ticketNo)==null?void 0:v.replace(/-/,""),V.trfdNo);else{const ae=(L=(G=V.ticketNo)==null?void 0:G.split("-"))==null?void 0:L[0];Q.set(`${ae}`,V.trfdNo)}}),Q}),X=Q=>Number(Q.conjunction)>1&&Q.ticket.tktType==="D"&&a.refundType==="manual",ce=Q=>{var q,V,B;return(q=Q==null?void 0:Q.toUpperCase())!=null&&q.startsWith("CA")?i("app.agentTicketRefund.pay.cash"):(V=Q==null?void 0:Q.toUpperCase())!=null&&V.startsWith("CC")?i("app.agentTicketRefund.pay.tc"):(B=Q==null?void 0:Q.toUpperCase())!=null&&B.startsWith("CHECK")?i("app.agentTicketRefund.pay.check"):Q},M=Q=>{var q,V,B;return(q=Q==null?void 0:Q.toUpperCase())!=null&&q.startsWith("CC")&&`${((V=Q==null?void 0:Q.split("/"))==null?void 0:V[1])??""}${((B=Q==null?void 0:Q.split("/"))==null?void 0:B[2])??""}`||"--"},w=Q=>{var V,B;const q=(B=P.value.get(((V=Q.segment)==null?void 0:V[0].tktTag)??""))==null?void 0:B[0];return a.ticketAmountList&&q?Q.currency:""},W=(Q,q)=>{var V;if(a.ticketAmountList){const B=(V=P.value.get(Q))==null?void 0:V[0];return B?B[q]:i("app.agentTicketRefund.calcFail")}return"--"},C=(Q,q)=>{var V;if(a.ticketAmountList){const B=(V=P.value.get(Q))==null?void 0:V[0],v=B?B[q]:[];return typeof v=="string"?[]:v}return[]},R=Q=>[...Q.keys()].map(q=>a.manualRefundAmountInfo.find(V=>{var B,v;return((v=(B=a.manualRefundAmountInfo)==null?void 0:B[0])==null?void 0:v.tktType)==="D"?V.ticketNo===q:V.ticketNo.split("-")[0]===q})),N=Q=>a.manualRefundAmountInfo.find(q=>q.ticketNo===Q),K=Q=>{var q;return((q=N(Q))==null?void 0:q.currency)??""},oe=(Q,q)=>(Q??[]).map(V=>({...V,currency:q})),te=Q=>(Q??[]).reduce((q,V)=>q+Number(V.value),0).toFixed(2),D=(Q,q)=>{var V;if(a.isFinishManualRefund||a.partSuccess){const B=R(Q),v=((V=B==null?void 0:B[0])==null?void 0:V.currency)??"";let G=0;const L=q==="commision"&&B.every(ae=>(ae==null?void 0:ae.commision)==="0")&&B.every(ae=>(ae==null?void 0:ae.commisionRate)!=="");return(B??[]).forEach(ae=>{q==="commision"&&!L?G=Qe(Number((ae==null?void 0:ae.commision)??"0"),G):q==="commision"&&L&&(G=Qe(Number((ae==null?void 0:ae.commisionRate)??"0"),G)),q==="otherDeduction"&&(G=Qe(Number((ae==null?void 0:ae.otherDeduction)??"0"),G)),q==="netRefund"&&(G=Qe(Number((ae==null?void 0:ae.netRefund)??"0"),G)),q==="totalAmount"&&(G=Qe(Number((ae==null?void 0:ae.totalAmount)??"0"),G)),q==="tax"&&(G=Qe(Number((ae==null?void 0:ae.totalTaxs)??"0"),G))}),L?`${Number(G).toFixed(2)}%`:`${v} ${Number(G).toFixed(2)}`}return"--"},E=Q=>{if(a.isFinishManualRefund||a.partSuccess){const q=[...R(Q).values()],V=[];q.forEach(v=>{((v==null?void 0:v.taxs)??[]).forEach(G=>{const L={...G,currency:v.currency};V.push(L)})});const B=(V??[]).reduce((v,G)=>(v[G.name]?v[G.name]+=Number(G.value):v[G.name]=Number(G.value),v),{});return V.forEach(v=>{v.value=B[v.name]}),[...new Set(V.map(v=>JSON.stringify(v)))].map(v=>JSON.parse(v))}},pe=()=>({ticketNo:$.value.ticketNo,ticketType:k.value?k.value:$.value.ticketType,ticketManagementOrganizationCode:$.value.ticketManagementOrganizationCode,printerNo:S.value,refundNo:$.value.refundNo,secondFactor:$.value.secondFactor}),ue=async Q=>{var V;const q=pe();try{u.value=!0;const{data:B}=await ln(q,Q);m.value=(V=B.value)==null?void 0:V.data,m.value.ticketManagementOrganizationCode=$.value.ticketManagementOrganizationCode??"",T.value=!0}finally{u.value=!1}},ge=async(Q,q,V,B)=>{S.value=q,k.value=V,$.value.ticketManagementOrganizationCode=B,await ue(Q)},se=async(Q,q,V,B,v,G)=>{if($.value={ticketNo:Q,ticketType:q,secondFactor:v,ticketManagementOrganizationCode:V,refundNo:G},k.value=q,S.value=B??"",$.value.ticketManagementOrganizationCode&&B){const L=we("091T0104");await ue(L)}else b.value=!0},de=Q=>{if(!A.value[Q].printerNo){A.value[Q].printError=i("app.ticketStatus.deviceNumNull");return}if(!Ht.test(A.value[Q].printerNo)){A.value[Q].printError=i("app.ticketStatus.deviceError");return}A.value[Q].printError=""},H=(Q,q)=>q&&(q==null?void 0:q.length)>=9&&Q!=="ARL"?q.slice(-9):q;return Ct(()=>a.ticketDetailList,()=>{A.value=a.ticketDetailList??[]},{deep:!0}),{loading:u,ticketList:A,isManualDomesticConjunctionTicket:X,showRefundFormDialog:T,printNo:S,printType:k,refundOperationCondition:$,refundFormData:m,getPayType:ce,getCreditCard:M,getCurrencyByTicket:w,getRefundFeeByTicketNo:W,getAmountAndCurrency:D,getManualTax:E,getDomManualCurrency:K,getDomManualTicket:N,getDomManualTax:oe,dealTaxTotal:te,ticketTrfdNo:j,openRefundFormDialog:se,checkPrinterNo:de,showPrintNoDialog:b,openDialog:ge,getRefundFormNumber:H,getBatchAutoTaxes:C,realRefs:p,setRealRefs:d,setPopoverRefs:o,showTicketOriginalContainerList:_,show:h,hide:y}},ca=a=>(wt("data-v-eef626b7"),a=a(),St(),a),Lc={class:"w-full h-[44px] p-[10px] bg-brand-4 border-b border-brand-3 justify-between items-center inline-flex"},jc={class:"justify-start items-center flex"},Bc={class:"justify-start items-start gap-2 flex mr-2.5"},Ic={key:0,class:"text-brand-2 text-base font-bold leading-normal"},Uc={key:1},Qc={class:"px-1 bg-gray-7 rounded-sm justify-start items-start gap-2.5 flex"},qc={class:"text-center text-gray-3 text-xs font-normal leading-tight"},zc={class:"justify-start items-center gap-2.5 flex ml-2.5"},Gc={class:"justify-start items-center gap-1 flex"},Hc={key:0,class:"iconfont icon-inf mr-[4px] text-gray-4"},Yc={key:1,class:"iconfont icon-user-fill mr-[4px] text-gray-4"},Kc={class:"text-gray-1 text-base font-bold leading-normal"},Wc={class:"px-1 bg-gray-7 rounded-sm justify-start items-start gap-2.5 flex"},Xc={class:"text-center text-gray-3 text-xs font-normal leading-tight"},Jc={class:"justify-start items-center gap-3.5 flex"},Zc={key:0,class:"justify-start items-start flex"},eu={class:"text-gray-3 text-sm font-normal leading-snug"},tu=["onClick"],nu={class:"justify-start items-start flex"},au={class:"text-gray-3 text-sm font-normal leading-snug"},su={class:"text-gray-1 text-sm font-normal leading-snug"},ou={class:"justify-start items-start flex"},iu={class:"text-gray-3 text-sm font-normal leading-snug"},lu={class:"text-gray-1 text-sm font-normal leading-snug"},ru={class:"justify-start items-start flex"},cu={class:"text-gray-3 text-sm font-normal leading-snug"},uu={class:"text-gray-1 text-sm font-normal leading-snug"},du={key:1,class:"justify-start items-center flex"},pu={class:"text-gray-3 text-sm font-normal leading-snug"},fu={class:"text-gray-1 text-sm font-normal leading-snug"},mu={key:0,class:"w-full h-5 px-[10px] justify-start items-center gap-2.5 inline-flex align-center"},gu=ca(()=>t("div",{class:"grow border-t-[1px] border-dashed border-gray-6"},null,-1)),ku={key:0,class:"text-xs w-[150px]"},yu=["onClick"],vu={key:0,class:"w-[135px] flex items-center"},hu={key:0,class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},_u={class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},bu={class:"text-gray-1 text-sm font-normal leading-snug"},xu={key:1,class:"w-[135px] flex items-center"},Tu={class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Nu={class:"text-gray-1 text-sm font-normal leading-snug"},$u={key:2,class:"w-[200px]"},Ru={class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Cu={key:3,class:"w-[200px] self-stretch justify-start items-center gap-5 flex"},wu={class:"w-[78px] text-gray-1 text-sm font-normal leading-snug"},Su={class:"text-gray-3 text-sm font-normal leading-snug"},Pu={class:"w-[220px] self-stretch justify-start items-center gap-2.5 flex"},Au={class:"text-gray-1 text-sm font-normal leading-snug"},Du={class:"text-gray-3 text-sm font-normal leading-snug"},Eu={key:0},Ou={class:"w-[400px] self-stretch px-1 justify-start items-center gap-3 flex"},Fu={key:1,class:"w-full h-[42px] p-[10px] justify-between items-center inline-flex border-gray-6"},Vu={class:"justify-start items-center gap-3.5 flex"},Mu={class:"justify-start items-start gap-0.5 flex"},Lu={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},ju={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},Bu={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},Iu={class:"justify-start items-start gap-0.5 flex"},Uu={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},Qu={class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},qu={class:"justify-start items-start gap-2.5 flex"},zu={class:"justify-start items-center gap-0.5 flex"},Gu={class:"text-gray-1 text-sm font-normal leading-snug"},Hu={class:"text-red-1 text-sm font-normal leading-snug"},Yu={class:"justify-start items-center gap-0.5 flex"},Ku={class:"text-gray-1 text-sm font-normal leading-snug"},Wu={class:"text-red-1 text-sm font-normal leading-snug"},Xu={class:"justify-start items-center gap-0.5 flex"},Ju={class:"text-gray-1 text-sm font-normal leading-snug"},Zu={class:"text-red-1 text-sm font-normal leading-snug"},ed={key:0,class:"justify-start items-start gap-2.5 flex"},td={class:"text-red-1 text-[14px] font-[700]"},nd=ca(()=>t("div",{class:"w-full border-t-[1px] border-dashed border-gray-6"},null,-1)),ad={class:"w-full h-[52px] p-[10px] justify-between items-center inline-flex"},sd={class:"justify-start items-center gap-3.5 flex"},od={key:0,class:"justify-start items-start gap-0.5 flex"},id={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},ld={class:"text-gray-1 text-sm font-normal leading-snug"},rd={key:1,class:"justify-start items-start gap-0.5 flex"},cd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},ud={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},dd={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},pd={class:"justify-start items-start gap-0.5 flex"},fd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},md={key:0,class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},gd={key:1,class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},kd={key:2},yd={class:"text-gray-2"},vd=ca(()=>t("span",{class:"text-red-1 mx-[3px]"},"*",-1)),hd={key:0,class:"justify-start items-start gap-2.5 flex"},_d={class:"justify-start items-center gap-0.5 flex"},bd={class:"text-gray-1 text-sm font-normal leading-snug"},xd={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Td={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Nd={class:"justify-start items-center gap-0.5 flex"},$d={class:"text-gray-1 text-sm font-normal leading-snug"},Rd={class:"text-red-1 text-sm font-normal leading-snug"},Cd={class:"justify-start items-center gap-0.5 flex"},wd={class:"text-gray-1 text-sm font-normal leading-snug"},Sd={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Pd={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Ad={class:"justify-start items-center gap-0.5 flex"},Dd={class:"text-gray-1 text-sm font-normal leading-snug"},Ed={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Od={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Fd={key:1,class:"justify-start items-start gap-2.5 flex"},Vd={class:"text-red-1 text-[14px] font-[700]"},Md=Le({__name:"TicketDetailInfo",props:{ticketDetailList:{},ticketAmountList:{},isFinishManualRefund:{type:Boolean},partSuccess:{type:Boolean},manualRefundAmountInfo:{},isShowTrfdNo:{type:Boolean},ticketTrfdNoDetails:{},refundType:{},deviceNum:{},refundTicketSuccess:{type:Boolean},batchAutoAmount:{},isAutoRefundFinished:{type:Boolean}},setup(a){const i=a,{loading:u,ticketList:p,isManualDomesticConjunctionTicket:f,showRefundFormDialog:d,printNo:o,printType:_,refundOperationCondition:h,refundFormData:y,getPayType:T,getCreditCard:b,getCurrencyByTicket:S,getRefundFeeByTicketNo:k,getAmountAndCurrency:$,getManualTax:m,getDomManualCurrency:A,getDomManualTicket:l,getDomManualTax:P,dealTaxTotal:j,ticketTrfdNo:X,openRefundFormDialog:ce,checkPrinterNo:M,showPrintNoDialog:w,openDialog:W,getRefundFormNumber:C,getBatchAutoTaxes:R}=Mc(i);return(N,K)=>{const oe=ct,te=ut,D=ht;return s(),g(_e,null,[(s(!0),g(_e,null,Re(e(p),(E,pe)=>{var ue,ge,se,de,H,Q,q,V,B,v,G,L,ae,ie,Ne,Ae,z,ke,x,O,Y,fe,ye,Se,Fe,Be,Ke;return Ue((s(),g("div",{key:pe,class:Pe(["rounded border border-brand-3 overflow-hidden",pe!==((ue=e(p))==null?void 0:ue.length)-1?"mb-[10px]":"mb-[0px]"])},[t("div",Lc,[t("div",jc,[t("div",Bc,[Number(E.conjunction)>1?(s(),g("div",Ic,n(e(Gt)(E.ticket.ticketNo)),1)):(s(),g("div",Uc,[r(un,{"tkt-index":pe,"ticket-number":E.ticket.ticketNo,"second-factor":E.ticket.secondFactor,"refund-class-type":"0"},null,8,["tkt-index","ticket-number","second-factor"])]))]),t("div",Qc,[t("div",qc,n(E!=null&&E.ticketManagementOrganizationCode?e(nc)[E==null?void 0:E.ticketManagementOrganizationCode].label:""),1)]),t("div",zc,[t("div",Gc,[((ge=E==null?void 0:E.ticket)==null?void 0:ge.specialPassengerType)==="INF"?(s(),g("em",Hc)):(s(),g("em",Yc)),t("div",Kc,n(E.ticket.passengerNameSuffix),1),t("div",Wc,[t("div",Xc,n(e($t)(((se=E==null?void 0:E.ticket)==null?void 0:se.specialPassengerType)??"ADT")),1)])])])]),t("div",Jc,[!e(f)(E)&&N.isShowTrfdNo?(s(),g("div",Zc,[t("div",eu,n(N.$t("app.agentTicketRefund.refundTicketNumber"))+"：",1),t("div",{class:"text-brand-2 text-sm font-bold leading-snug cursor-pointer",onClick:ve=>{var Ye,le,pt,at;return e(ce)((le=(Ye=E.ticket.ticketNo)==null?void 0:Ye.split("-"))==null?void 0:le[0],E.ticket.tktType,E.ticketManagementOrganizationCode??"",E==null?void 0:E.refundPrintNumber,E.ticket.secondFactor,e(X).get((at=(pt=E.ticket.ticketNo)==null?void 0:pt.split("-"))==null?void 0:at[0])??"")}},n(e(C)((E==null?void 0:E.ticketManagementOrganizationCode)??"",e(X).get(((H=(de=E.ticket.ticketNo)==null?void 0:de.split("-"))==null?void 0:H[0])??"")??"")||"--"),9,tu)])):ee("",!0),t("div",nu,[t("div",au,n(N.$t("app.agentTicketRefund.oldTicketNo"))+"：",1),t("div",su,n(e(Gt)(E.ticket.exchangeTktNo??"")||"--"),1)]),t("div",ou,[t("div",iu,n(N.$t("app.agentTicketRefund.electronic"))+"：",1),t("div",lu,n(N.$t("app.agentTicketRefund.yes")),1)]),t("div",ru,[t("div",cu,n(N.$t("app.agentTicketRefund.payment"))+"：",1),t("div",uu,n(e(T)(E.ticket.payType)),1)]),(q=(Q=E.ticket.payType)==null?void 0:Q.toUpperCase())!=null&&q.startsWith("CC")?(s(),g("div",du,[t("div",pu,n(N.$t("app.agentTicketRefund.creditCard"))+"：",1),t("div",fu,n(e(b)(E.ticket.payType)),1)])):ee("",!0)])]),(s(!0),g(_e,null,Re(E.ticket.ticketSegment,(ve,Ye)=>{var le,pt,at,xe,F,Te,$e,Ee,Oe,Xe,tt,st,Ge;return s(),g(_e,{key:Ye},[Number(E.conjunction)>1?(s(),g("div",mu,[r(un,{"tkt-index":`${pe}_${Ye}`,"ticket-number":(ve==null?void 0:ve[0])??"","second-factor":E.ticket.secondFactor,"refund-class-type":"1"},null,8,["tkt-index","ticket-number","second-factor"]),gu,e(f)(E)&&N.isShowTrfdNo?(s(),g("div",ku,[J(n(N.$t("app.agentTicketRefund.refundTicketNumber"))+"：",1),t("span",{class:"text-brand-2 font-bold cursor-pointer",onClick:We=>e(ce)(ve==null?void 0:ve[0],E.ticket.tktType,E.ticketManagementOrganizationCode??"",E==null?void 0:E.refundPrintNumber,E.ticket.secondFactor,e(X).get(ve==null?void 0:ve[0])??"")},n(e(C)((E==null?void 0:E.ticketManagementOrganizationCode)??"",e(X).get(ve==null?void 0:ve[0])??"")||"--"),9,yu)])):ee("",!0)])):ee("",!0),t("div",{class:Pe(["w-full px-[10px] flex-col justify-start items-start inline-flex",Number(E.conjunction)>1?"py-[4px]":"py-[8px]"])},[(s(!0),g(_e,null,Re(ve==null?void 0:ve[1],(We,It)=>{var Ut,bt,an;return s(),g("div",{key:It,class:"self-stretch h-7 justify-between items-center inline-flex"},[We.segmentType!=="2"?(s(),g("div",vu,[We.segmentType==="3"&&We.airline?(s(),g("span",hu,n(We.airline),1)):ee("",!0),t("span",_u,n(We.segmentType==="3"?"OPEN":"ARNK"),1),t("span",bu,n(We.cabinCode),1)])):(s(),g("div",xu,[t("div",Tu,n(We.flightNo),1),t("div",Nu,n(We.cabinCode),1)])),We.segmentType!=="2"?(s(),g("div",$u,[t("span",Ru,n(We.segmentType==="3"?"OPEN":"ARNK"),1)])):ee("",!0),We.departureDate||We.departureTime?(s(),g("div",Cu,[t("div",wu,n(We.departureDate),1),t("div",Su,n(((Ut=We.departureTime)==null?void 0:Ut.substring(0,5))??""),1)])):ee("",!0),t("div",Pu,[t("div",Au,n(We.departureCode)+"-"+n(We.arriveCode),1),t("div",Du,[J(n(E.ticket.pnr),1),E.ticket.pnr&&E.ticket.crsPnrNo?(s(),g("span",Eu,"/")):ee("",!0),J(n(E.ticket.crsPnrNo),1)])]),t("div",Ou,[t("div",{class:Pe(["text-sm font-bold leading-snug",e(Bt)[((bt=We.ticketStatus)==null?void 0:bt.trim())??""]?e(Bt)[((an=We.ticketStatus)==null?void 0:an.trim())??""].color:""])},n(We.ticketStatus),3)])])}),128))],2),E.ticket.tktType==="D"&&(N.isFinishManualRefund||N.partSuccess)&&Number(E.conjunction)>1?(s(),g("div",Fu,[t("div",Vu,[t("div",Mu,[t("div",Lu,n(N.$t("app.agentTicketRefund.totalTicketAmount")),1),(le=e(l)(ve==null?void 0:ve[0]))!=null&&le.totalAmount?(s(),g("div",Bu,n(e(A)(ve==null?void 0:ve[0]))+" "+n(((pt=e(l)(ve==null?void 0:ve[0]))==null?void 0:pt.totalAmount)??""),1)):(s(),g("div",ju,"--"))]),t("div",Iu,[t("div",Uu,n(N.$t("app.agentTicketRefund.totalTaxAmount")),1),(s(),re(Hn,{key:`DomesticRefundTaxDetailPopover${pe}_${Ye}`,ticket:E.ticket,taxs:e(P)((at=e(l)(ve==null?void 0:ve[0]))==null?void 0:at.taxs,(xe=e(l)(ve==null?void 0:ve[0]))==null?void 0:xe.currency),"is-finish-manual-refund":N.isFinishManualRefund,"part-success":N.partSuccess},{"tax-deatil":c(()=>{var We;return[t("div",Qu,n(e(A)(ve==null?void 0:ve[0]))+" "+n(e(j)((We=e(l)(ve==null?void 0:ve[0]))==null?void 0:We.taxs)),1)]}),_:2},1032,["ticket","taxs","is-finish-manual-refund","part-success"]))])]),t("div",qu,[t("div",zu,[t("div",Gu,n(!Number((F=e(l)(ve==null?void 0:ve[0]))==null?void 0:F.commision)&&((Te=e(l)(ve==null?void 0:ve[0]))==null?void 0:Te.commisionRate)!==""?N.$t("app.agentTicketRefund.commissionRate"):N.$t("app.agentTicketRefund.commission")),1),t("div",Hu,n(!Number(($e=e(l)(ve==null?void 0:ve[0]))==null?void 0:$e.commision)&&((Ee=e(l)(ve==null?void 0:ve[0]))==null?void 0:Ee.commisionRate)!==""?`${((Oe=e(l)(ve==null?void 0:ve[0]))==null?void 0:Oe.commisionRate)??""}%`:`${(Xe=e(l)(ve==null?void 0:ve[0]))==null?void 0:Xe.currency} ${Number((tt=e(l)(ve==null?void 0:ve[0]))==null?void 0:tt.commision).toFixed(2)}`),1)]),t("div",Yu,[t("div",Ku,n(N.$t("app.agentTicketRefund.charge")),1),t("div",Wu,n(e(A)(ve==null?void 0:ve[0]))+" "+n(((st=e(l)(ve==null?void 0:ve[0]))==null?void 0:st.otherDeduction)??""),1)]),t("div",Xu,[t("div",Ju,n(N.$t("app.agentTicketRefund.totalRefund")),1),t("div",Zu,n(e(A)(ve==null?void 0:ve[0]))+" "+n(((Ge=e(l)(ve==null?void 0:ve[0]))==null?void 0:Ge.netRefund)??""),1)])]),E.ticket.isRefundFail?(s(),g("div",ed,[t("span",td,n(N.$t("app.agentTicketRefund.refundFailMsg")),1)])):ee("",!0)])):ee("",!0)],64)}),128)),E.ticket.tktType==="D"&&(N.isFinishManualRefund||N.partSuccess)&&Number(E.conjunction)>1?ee("",!0):(s(),g(_e,{key:0},[nd,t("div",ad,[t("div",sd,[N.isAutoRefundFinished?(s(),g("div",od,[t("div",id,n(N.$t("app.agentTicketRefund.totalTicketAmount")),1),t("div",ld,n(`${E.ticket.currency??"CNY"} ${e(k)(((B=(V=E.ticket.segment)==null?void 0:V[0])==null?void 0:B.tktTag)??"","totalAmount")}`),1)])):(s(),g("div",rd,[t("div",cd,n(N.$t("app.agentTicketRefund.totalTicketAmount")),1),E.ticket.totalAmount?(s(),g("div",dd,n(N.isFinishManualRefund||N.partSuccess?e($)(E.ticket.ticketSegment,"totalAmount"):`${E.ticket.currency??"CNY"} ${E.ticket.totalAmount}`),1)):(s(),g("div",ud,"--"))])),t("div",pd,[t("div",fd,n(N.$t("app.agentTicketRefund.totalTaxAmount")),1),(s(),re(Hn,{key:`InternationalRefundTaxDetailPopover${pe}`,"is-auto-refund-finished":N.isAutoRefundFinished,"batch-auto-taxes":e(R)(((G=(v=E.ticket.segment)==null?void 0:v[0])==null?void 0:G.tktTag)??"","taxes"),ticket:E.ticket,taxs:e(m)(E.ticket.ticketSegment),"is-finish-manual-refund":N.isFinishManualRefund,"part-success":N.partSuccess},{"tax-deatil":c(()=>{var ve,Ye;return[N.isAutoRefundFinished?(s(),g("div",md,n(`${E.ticket.currency??"CNY"} ${e(k)(((Ye=(ve=E.ticket.segment)==null?void 0:ve[0])==null?void 0:Ye.tktTag)??"","totalTaxs")}`),1)):(s(),g("div",gd,n(N.isFinishManualRefund||N.partSuccess?e($)(E.ticket.ticketSegment,"tax"):`${E.ticket.currency??"CNY"} ${E.ticket.totalTaxs}`),1))]}),_:2},1032,["is-auto-refund-finished","batch-auto-taxes","ticket","taxs","is-finish-manual-refund","part-success"]))]),!N.refundTicketSuccess&&!((E==null?void 0:E.ticketManagementOrganizationCode)??"").includes("CDS")?(s(),g("div",kd,[r(te,{model:E,"require-asterisk-position":"right",class:"device-form"},{default:c(()=>[r(oe,{prop:"printNo",error:E.printError},{default:c(()=>[t("div",yd,[J(n(N.$t("app.agentTicketRefund.prntNo")),1),vd]),r(Kt,{modelValue:E.printerNo,"onUpdate:modelValue":ve=>E.printerNo=ve,"select-class":"w-[100px]",onBlur:ve=>e(M)(pe)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["error"])]),_:2},1032,["model"])])):ee("",!0)]),E.ticket.isRefundFail?ee("",!0):(s(),g("div",hd,[t("div",_d,[t("div",bd,n(e($)(E.ticket.ticketSegment,"commision").includes("%")?N.$t("app.agentTicketRefund.commissionRate"):N.$t("app.agentTicketRefund.commission")),1),!N.isFinishManualRefund&&!N.partSuccess?(s(),g("div",xd,n(e(k)(((ae=(L=E.ticket.segment)==null?void 0:L[0])==null?void 0:ae.tktTag)??"","commision")&&e(k)(((Ne=(ie=E.ticket.segment)==null?void 0:ie[0])==null?void 0:Ne.tktTag)??"","commision")!=="--"?`${e(S)(E.ticket)} ${e(k)(((z=(Ae=E.ticket.segment)==null?void 0:Ae[0])==null?void 0:z.tktTag)??"","commision")}`:"--"),1)):(s(),g("div",Td,n(e($)(E.ticket.ticketSegment,"commision")),1))]),t("div",Nd,[!N.isFinishManualRefund&&!N.partSuccess?(s(),g(_e,{key:0},[t("div",$d,n(N.$t("app.agentTicketRefund.commissionRate")),1),t("div",Rd,n(isNaN(Number(e(k)(((x=(ke=E.ticket.segment)==null?void 0:ke[0])==null?void 0:x.tktTag)??"","commisionRate")))?e(k)(((ye=(fe=E.ticket.segment)==null?void 0:fe[0])==null?void 0:ye.tktTag)??"","commisionRate"):`${e(k)(((Y=(O=E.ticket.segment)==null?void 0:O[0])==null?void 0:Y.tktTag)??"","commisionRate")??""}%`),1)],64)):ee("",!0)]),t("div",Cd,[t("div",wd,n(N.$t("app.agentTicketRefund.charge")),1),!N.isFinishManualRefund&&!N.partSuccess?(s(),g("div",Sd,n(e(S)(E.ticket))+" "+n(e(k)(((Fe=(Se=E.ticket.segment)==null?void 0:Se[0])==null?void 0:Fe.tktTag)??"","otherDeduction")),1)):(s(),g("div",Pd,n(e($)(E.ticket.ticketSegment,"otherDeduction")),1))]),t("div",Ad,[t("div",Dd,n(N.$t("app.agentTicketRefund.totalRefund")),1),!N.isFinishManualRefund&&!N.partSuccess?(s(),g("div",Ed,n(e(S)(E.ticket))+" "+n(e(k)(((Ke=(Be=E.ticket.segment)==null?void 0:Be[0])==null?void 0:Ke.tktTag)??"","netRefund")),1)):(s(),g("div",Od,n(e($)(E.ticket.ticketSegment,"netRefund")),1))])])),E.ticket.isRefundFail?(s(),g("div",Fd,[t("span",Vd,n(N.$t("app.agentTicketRefund.refundFailMsg")),1)])):ee("",!0)])],64))],2)),[[D,e(u),void 0,{fullscreen:!0,lock:!0}]])}),128)),e(w)?(s(),re(Za,{key:0,modelValue:e(w),"onUpdate:modelValue":K[0]||(K[0]=E=>Me(w)?w.value=E:null),"ticket-management-organization-code":e(h).ticketManagementOrganizationCode??"",onOpenDialog:e(W)},null,8,["modelValue","ticket-management-organization-code","onOpenDialog"])):ee("",!0),e(d)?(s(),re(aa,{key:1,modelValue:e(d),"onUpdate:modelValue":K[1]||(K[1]=E=>Me(d)?d.value=E:null),"printer-no":e(o),"printer-type":e(_),"is-supplement-refund":!1,"refund-operation-condition":e(h),"refund-ticket-data":e(y)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data"])):ee("",!0)],64)}}});const Ld=dt(Md,[["__scopeId","data-v-eef626b7"]]),jd={class:"flex text-[16px] text-[#000]"},Bd={class:"ml-[10px] w-full"},Id={class:"pb-[6px]"},Ud={key:0,class:"flex"},Qd={class:"w-[294px]"},qd={key:1,class:"text-green-1"},zd={key:2,class:"flex"},Gd={class:"w-[294px]"},Hd={key:3,class:"text-green-1"},Yd={key:4,class:"mt-[12px] mb-[12px]"},Kd={class:"text-end w-full"},Wd=Le({__name:"interRefundSuccessdialog",props:{newPnr:{},oldPnr:{},ticketDetailList:{}},emits:["update:modelValue","refresh"],setup(a,{emit:i}){const u=a,p=i,{t:f}=Ze(),d=Nn(),o=I(!0),_=I(!1),h=I(!1),y=I(!1),T=async l=>{d.push({name:"PnrManagement",query:{pnrNo:l,time:new Date().getTime()}});const P=we("091Q0104");p("refresh",!0,P),p("update:modelValue",!1)},b=(l,P)=>{lt({message:l?f("app.agentTicketRefund.cancelPnrSuccess",{pnr:P}):f("app.agentTicketRefund.manualDeleteSuccess"),type:"success"})},S=l=>{const P={pnrNo:l?"":u.oldPnr,xePnr:l?u.newPnr:"",passengerInfoList:[],pnrHandleType:l?"C":"D"};return(u.ticketDetailList??[]).forEach(j=>{!l&&j.ticket.psgType==="INF"&&P.passengerInfoList.push({name:Ft.encode(j.ticket.name),psgType:j.ticket.psgType,ticketNo:j.ticket.ticketNo})}),P},k=async l=>{y.value=!0;let P;try{const j=we("09200113");P=(await bn(S(l),j)).data.value}finally{y.value=!1}l&&(P!=null&&P.xePnrExecutionStatus.includes("S"))?(b(l,u.newPnr),h.value=!0):!l&&(P!=null&&P.deleteInfantExecutionStatus.includes("S"))&&(b(l),_.value=!0)},$=()=>{k(!1)},m=()=>{k(!0)},A=()=>{const l=we("091Q0104");p("refresh",!0,l),p("update:modelValue",!1)};return(l,P)=>{const j=nt,X=et,ce=rt,M=ht;return Ue((s(),g("div",null,[r(ce,{modelValue:o.value,"onUpdate:modelValue":P[2]||(P[2]=w=>o.value=w),"show-close":!1,"close-on-click-modal":!1,modal:!1,"close-on-press-escape":!1,class:"refund-success-dialog",width:"440"},{default:c(()=>[t("div",jd,[t("div",null,[r(j,{class:"top-[calc(50%-30px)]",size:30,color:"var(--bkc-color-special-green-2)"},{default:c(()=>[r(e(Kn))]),_:1})]),t("div",Bd,[t("p",Id,n(e(f)("app.agentTicketRefund.autoRefundSuccess")),1),_.value?(s(),g("div",qd,"PNR : "+n(l.oldPnr)+n(e(f)("app.agentTicketRefund.passengerBeDelete")),1)):(s(),g("div",Ud,[t("div",Qd,[J(n(e(f)("app.agentTicketRefund.whetherAutoDelete")),1),t("span",{class:"underline text-brand-2 cursor-pointer",onClick:P[0]||(P[0]=w=>T(l.oldPnr))},"PNR : "+n(l.oldPnr),1),J(n(e(f)("app.agentTicketRefund.refundTicketTip")),1)]),r(X,{class:"operate-btn",type:"primary",onClick:$},{default:c(()=>[J(n(e(f)("app.agentTicketRefund.deletePassengerOperate")),1)]),_:1})])),h.value?(s(),g("div",Hd,"PNR : "+n(l.newPnr)+" "+n(e(f)("app.agentTicketRefund.canceled")),1)):(s(),g("div",zd,[t("div",Gd,[J(n(e(f)("app.agentTicketRefund.whetherAutoCancel")),1),t("span",{class:"underline text-brand-2 cursor-pointer",onClick:P[1]||(P[1]=w=>T(l.newPnr))},"PNR : "+n(l.newPnr),1),J(" ？ ")]),r(X,{class:"operate-btn",type:"primary",onClick:m},{default:c(()=>[J(n(e(f)("app.agentTicketRefund.cancelPnr")),1)]),_:1})])),h.value&&_.value?ee("",!0):(s(),g("div",Yd,n(e(f)("app.agentTicketRefund.goToOrder")),1))])]),t("div",Kd,[h.value&&_.value?(s(),re(X,{key:0,type:"primary",onClick:A},{default:c(()=>[J(n(e(f)("app.agentTicketRefund.sure")),1)]),_:1})):(s(),re(X,{key:1,onClick:A},{default:c(()=>[J(n(e(f)("app.agentTicketRefund.cancel")),1)]),_:1}))])]),_:1},8,["modelValue"])])),[[M,y.value,void 0,{fullscreen:!0,lock:!0}]])}}});const Xd={class:"bg-[var(--bkc-el-bg-color)] p-[10px] rounded-lg mt-[10px] min-h-[calc(100vh_-_33vh)] shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},Jd={class:"flex items-center"},Zd={class:"text-gray-1 text-base font-bold leading-normal"},ep={key:0,class:"ml-[14px] p-[4px] rounded-[2px] bg-brand-3 text-brand-1 text-[14px]"},tp={class:"inline-block h-full border-yellow-2 border-solid bg-yellow-3 text-yellow-1 text-[12px] rounded-[2px] py-[8px] px-[10px] flex items-center"},np={key:1,class:"ml-[14px] p-[4px] rounded-[2px] bg-brand-3 text-brand-1 text-[14px]"},ap={class:"footer"},sp=Le({__name:"TicketRefundContainer",props:{tktNo:{},factor:{}},emits:["addNewTab","removeTab"],setup(a,{emit:i}){const u=a,p=i,{splitPnrNo:f,isDragonBoatOffice:d,pnrNo:o,amountRef:_,fullscreenLoading:h,allPassengerList:y,ticketDetailList:T,manualDialogVisible:b,packageData:S,clacAmountInfosRes:k,clacAmountInfo:$,isAutoRefundFinished:m,isXePnr:A,currentTktNo:l,deviceNum:P,isFinishManualRefund:j,manualRefundAmountInfo:X,partSuccess:ce,isCanRefund:M,queryTicketDetail:w,manualRefund:W,handleAutoRefund:C,getCalcInfo:R,queryTicketAndCalcAmount:N,getManualRefundAmountDetail:K,refresh:oe,ticketTrfdNoDetails:te,isShowTrfdNo:D,getTicketTrfdNoDetailsFromManualRefund:E,refundType:pe,isRelatedCorrectPnr:ue,updatePnrForm:ge,updatePnrFormData:se,updatePnrFormRules:de,queryAllPassenger:H,queryPnrTip:Q,showInterRefundSuccess:q,refreshTicketDetail:V,refundTicketSuccess:B,batchAutoAmount:v}=Di(u,p);return(G,L)=>{const ae=vt,ie=ct,Ne=et,Ae=nt,z=ut,ke=$n("permission"),x=ht;return Ue((s(),g("div",Xd,[t("div",Jd,[t("div",Zd,n(G.$t("app.agentTicketRefund.refund")),1),t("div",null,[e(o)?(s(),g(_e,{key:0},[e(ue)?(s(),g("span",ep,"PNR："+n(e(o)),1)):(s(),re(z,{key:1,ref_key:"updatePnrForm",ref:ge,model:e(se),inline:!0,rules:e(de),"require-asterisk-position":"right",class:"ml-[14px] h-[32px] flex items-center updatePnrForm"},{default:c(()=>[r(ie,{prop:"pnrNo",label:G.$t("app.agentTicketQuery.pnrNumber")},{default:c(()=>[r(ae,{modelValue:e(se).pnrNo,"onUpdate:modelValue":L[0]||(L[0]=O=>e(se).pnrNo=O),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),r(ie,null,{default:c(()=>[r(Ne,{type:"primary",onClick:L[1]||(L[1]=O=>e(H)(e(se).pnrNo,"091Q0101",e(ge)))},{default:c(()=>[J(n(G.$t("app.agentTicketQuery.queryBtn")),1)]),_:1})]),_:1}),t("span",tp,[r(Ae,{size:"14px",class:"mr-[5px]"},{default:c(()=>[r(e(Wn))]),_:1}),J(n(e(Q)),1)])]),_:1},8,["model","rules"]))],64)):(s(),g("span",np,"PNR：-"))])]),r(Lr,{"all-passenger-list":e(y),"clac-amount-info":e($),"refund-ticket-success":e(B),onQueryTicketDetail:e(w),onQueryTicketAndCalcAmount:e(N)},null,8,["all-passenger-list","clac-amount-info","refund-ticket-success","onQueryTicketDetail","onQueryTicketAndCalcAmount"]),e(T).length?(s(),re(Ld,{key:0,"ticket-detail-list":e(T),"refund-ticket-success":e(B),"is-finish-manual-refund":e(j),"is-show-trfd-no":e(D),"part-success":e(ce),"manual-refund-amount-info":e(X),"ticket-amount-list":e(k).queryRefundFeeAggregateRespDTOList,"ticket-trfd-no-details":e(te),"refund-type":e(pe),"device-num":e(P),"is-auto-refund-finished":e(m),"batch-auto-amount":e(v)},null,8,["ticket-detail-list","refund-ticket-success","is-finish-manual-refund","is-show-trfd-no","part-success","manual-refund-amount-info","ticket-amount-list","ticket-trfd-no-details","refund-type","device-num","is-auto-refund-finished","batch-auto-amount"])):ee("",!0),r(tc,{ref_key:"amountRef",ref:_,"batch-auto-amount":e(v),"amount-data":e(k).queryRefundFeeAggregateRespDTOList,status:e(k).status,"is-auto-refund-finished":e(m),"is-finish-manual-refund":e(j),"manual-refund-amount-info":e(X),"is-can-refund":e(M),onGetCalcInfo:e(R)},null,8,["batch-auto-amount","amount-data","status","is-auto-refund-finished","is-finish-manual-refund","manual-refund-amount-info","is-can-refund","onGetCalcInfo"]),t("div",ap,[e(B)?ee("",!0):(s(),g(_e,{key:0},[Ue((s(),re(Ne,{"data-gid":"091Q0105",disabled:!e($).isAlreadySuccessSearch||!e(M),onClick:e(C)},{default:c(()=>[J(n(G.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["disabled","onClick"])),[[ke,"crs-ticket-manage-refund-page-refund-button"]]),Ue((s(),re(Ne,{disabled:!e(M),onClick:e(W)},{default:c(()=>[J(n(G.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1},8,["disabled","onClick"])),[[ke,"crs-ticket-manage-refund-page-manual-refund-button"]])],64)),r(Ne,{"data-gid":"091Q0104",onClick:e(oe)},{default:c(()=>[J(n(G.$t("app.agentTicketRefund.refresh")),1)]),_:1},8,["onClick"])]),e(b)?(s(),re(Pr,{key:1,modelValue:e(b),"onUpdate:modelValue":L[2]||(L[2]=O=>Me(b)?b.value=O:null),"ticket-detail-list":e(T),"onUpdate:ticketDetailList":L[3]||(L[3]=O=>Me(T)?T.value=O:null),isShowTrfdNo:e(D),"onUpdate:isShowTrfdNo":L[4]||(L[4]=O=>Me(D)?D.value=O:null),"all-passengers":e(y),"is-dragon-boat-office":e(d),"pnr-no":e(o),"is-xepnr":e(A),"tkt-no":e(l),"package-data":e(S),"device-num":e(P),onQueryTicketDetail:e(w),onGetManualRefundAmountDetail:e(K),onGetTicketTrfdNoDetails:e(E)},null,8,["modelValue","ticket-detail-list","isShowTrfdNo","all-passengers","is-dragon-boat-office","pnr-no","is-xepnr","tkt-no","package-data","device-num","onQueryTicketDetail","onGetManualRefundAmountDetail","onGetTicketTrfdNoDetails"])):ee("",!0),e(q)?(s(),re(Wd,{key:2,modelValue:e(q),"onUpdate:modelValue":L[5]||(L[5]=O=>Me(q)?q.value=O:null),"ticket-detail-list":e(T),"new-pnr":e(f),"old-pnr":e(o),onRefresh:e(V)},null,8,["modelValue","ticket-detail-list","new-pnr","old-pnr","onRefresh"])):ee("",!0)])),[[x,e(h),void 0,{fullscreen:!0,lock:!0}]])}}});const xn=dt(sp,[["__scopeId","data-v-8fb5d4c9"]]),op=(a,i)=>{const{t:u}=Ze(),p=Rt(),f=je(()=>{var v;return(v=p.state.user)==null?void 0:v.entityType}),d=it({pnrNo:""}),o=I(),_=I(),h=I({travellerInfoList:[]}),y=I([]),T=I(!1),b=De(!1),S=I([]),k={ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],ticketMachineNumber:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"},{pattern:Ht,trigger:"blur",message:u("app.agentTicketQuery.repelTicket.ticketMachineNumberReg")}],pnrNo:[{required:!0,message:u("app.agentTicketQuery.repelTicket.pnrNotAllowNull"),trigger:"blur"},{pattern:gn,message:u("app.agentTicketQuery.repelTicket.pnrReg"),trigger:"blur"}]},$=je(()=>{var v;return!(d.pnrNo&&((v=y.value)==null?void 0:v.length)>0)}),m=je(()=>{var G,L;const v=h.value.travellerInfoList.filter(ae=>ae.alreadyInvalid)??[];return!!((L=(G=y.value)==null?void 0:G[0])!=null&&L.crsPnrNo)&&h.value.travellerInfoList.length>0&&y.value.length>0&&h.value.travellerInfoList.length===y.value.length+v.length}),A=je(()=>{const v=h.value.travellerInfoList.filter(G=>G.invalid)??[];return h.value.travellerInfoList.length>0&&y.value.length>0&&v.length===y.value.length}),l=je(()=>h.value.travellerInfoList.every(v=>!v.invalid)),P=je(()=>!d.pnrNo),j=v=>{if(!v){y.value=[];return}y.value=h.value.travellerInfoList.filter(G=>G.invalid)??[]},X=()=>({pnrNo:d.pnrNo??""}),ce=v=>{var L,ae;return(S.value??[]).some(ie=>v===ie.value)&&v?v:((ae=(L=S.value)==null?void 0:L[0])==null?void 0:ae.value)??""},M=v=>{var G;[h.value.travellerInfoList,y.value,b.value]=[[],[],!0],h.value.travellerInfoList=yt(v)??[],h.value.travellerInfoList.forEach(L=>{L.ticketManagementOrganizationCode=ce(L.ticketManagementOrganizationCode),L.ticketMachineNumber="",L.ticketMachineNumberError="",L.ticketOrganizationError=""}),((G=h.value.travellerInfoList)==null?void 0:G.length)===1&&h.value.travellerInfoList[0].invalid&&y.value.push(h.value.travellerInfoList[0])},w=async()=>{o.value.validate(async v=>{if(!v)return;const G=we("09200129"),L=(await Xt(go(X(),G))).data.value;M(L)})},W=()=>y.value.map(v=>({etNo:v.ticketNo??"",passengerName:Ft.encode(v.passengerNameSuffix)??"",etType:v.etType??"",passengerType:v.specialPassengerType??"",printerNo:v.ticketMachineNumber??"",ticketTypeCode:v.ticketTypeCode,governmentPurchase:v.governmentPurchase,pnrNo:v.crsPnrNo||d.pnrNo,paymentBOP:v.paymentBOP,ticketManagementOrganizationCode:v.ticketManagementOrganizationCode})),C=()=>({invalidTicketDetails:W(),xePnr:T.value}),R=()=>{Je.close();const v=`/v2/crs/pnrManagement?pnrNo=${d.pnrNo}`;yn.setLink(v),a("update:modelValue",!1)},N=async v=>Je({message:v,icon:Ce("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"success-message-common crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:u("app.agentTicketQuery.repelTicket.confirm")}),K=async v=>{T.value&&v?await mn(u("app.agentTicketQuery.repelTicket.repelTicketXEPnrSuccess")):T.value&&!v?await N(Ce("div",null,[Ce("span",{class:"block"},u("app.agentTicketQuery.repelTicket.repelTicketSuccess")),Ce("span",null,u("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_1")),Ce("span",{class:"text-brand-2 cursor-pointer",onClick:R},u("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_2")),Ce("span",null,u("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_3"))])):await N(Ce("div",null,[Ce("span",{class:"block"},u("app.agentTicketQuery.repelTicket.repelTicketSuccess")),Ce("span",null,u("app.agentTicketQuery.repelTicket.repelTicketSuccessNoXEPnrTip_1"))])),a("update:modelValue",!1),a("reQueryTicket")},oe=v=>(v??[]).reduce((G,L)=>{let ae="";G||(ae=`<p class="text-gray-1 text-lg font-normal pb-2.5">${u("app.agentTicketQuery.repelTicket.partRepelTicketSuccess")}</p>`);const ie=L.vtSuccess?'<i class="iconfont icon-ticket text-emerald-600 mr-2.5"></i>':'<i class="iconfont icon-close text-rose-600 mr-2.5"></i>',Ne=`${ae}<p class="mt-4">${ie}${L.passengerNameSuffix}<span class="text-center text-gray-3 text-xs font-normal inline-block bg-gray-7 px-1 py-0.5 text-gray-3 leading-tight ml-2.5"><span>${$t(L.specialPassengerType)}</span></span></p>`;return G+Ne},""),te=v=>{Je.confirm(oe(v),{icon:Ce(nt,{color:"#FF3636",size:32},()=>Ce(wa)),customClass:"invalidated-warning-msg",closeOnClickModal:!1,showClose:!0,showCancelButton:!1,confirmButtonText:u("app.agentTicketQuery.repelTicket.confirm"),dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{a("update:modelValue",!1),a("reQueryTicket")})},D=v=>{h.value.travellerInfoList[v].ticketMachineNumberError="",h.value.travellerInfoList=[...h.value.travellerInfoList]},E=()=>{const v=y.value.map(G=>G.ticketNo);h.value.travellerInfoList.forEach(G=>{v.includes(G.ticketNo)||(G.ticketMachineNumberError="")})},pe=()=>{const v=y.value.map(G=>G.ticketNo);for(let G=0;G<h.value.travellerInfoList.length;G++){const L=h.value.travellerInfoList[G];if(v.includes(L.ticketNo)&&de(L.ticketManagementOrganizationCode)){if(!L.ticketMachineNumber){L.ticketMachineNumberError=u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber");continue}if(!Ht.test(L.ticketMachineNumber)){L.ticketMachineNumberError=u("app.agentTicketQuery.repelTicket.ticketMachineNumberReg");continue}}else L.ticketMachineNumberError=""}return h.value.travellerInfoList.every(G=>!G.ticketMachineNumberError)},ue=()=>{const v=y.value.map(G=>G.ticketNo);for(let G=0;G<h.value.travellerInfoList.length;G++){const L=h.value.travellerInfoList[G];if(v.includes(L.ticketNo)&&!L.ticketManagementOrganizationCode){L.ticketOrganizationError=u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber");continue}}return h.value.travellerInfoList.every(G=>!G.ticketOrganizationError)},ge=()=>{const v=we("091P0102");y.value.length!==0&&_.value.validate(async G=>{if(!G||!ue()||!pe())return;const{entireSuccess:L,invalidTicketItems:ae,xePnrSuccess:ie}=(await Xt(ko(C(),v))).data.value;L?K(ie):!L&&ae.length>0&&te(ae)})},se=()=>{a("update:modelValue",!1)},de=v=>!["CDS","GPCDS"].includes(v),H={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},Q=()=>{var v,G,L,ae,ie,Ne,Ae,z,ke,x;((v=f.value)!=null&&v.includes("$$$")||(G=f.value)!=null&&G.includes("BSP"))&&(S.value.push(H.BSP),S.value.push(H.GPBSP)),!((L=f.value)!=null&&L.includes("BSP"))&&((ae=f.value)!=null&&ae.includes("GP"))&&S.value.push(H.GPBSP),((ie=f.value)!=null&&ie.includes("$$$")||(Ne=f.value)!=null&&Ne.includes("BOP"))&&S.value.push(H.BOPBSP),((Ae=f.value)!=null&&Ae.includes("$$$")||(z=f.value)!=null&&z.includes("CDS"))&&(S.value.push(H.CDS),S.value.push(H.GPCDS)),((ke=f.value)!=null&&ke.includes("$$$")||(x=f.value)!=null&&x.includes("本票"))&&S.value.push(H.ARL)},q=v=>{var L,ae,ie,Ne,Ae,z,ke,x,O,Y,fe,ye,Se;return{passengerName:((L=v==null?void 0:v[0])==null?void 0:L.passengerName)??"",passengerNameSuffix:((ae=v==null?void 0:v[0])==null?void 0:ae.passengerNameSuffix)??"",passengerType:((ie=v==null?void 0:v[0])==null?void 0:ie.passengerType)??"",pnrPsgType:"",specialPassengerType:((Ne=v==null?void 0:v[0])==null?void 0:Ne.specialPassengerType)??"",ticketNo:((Ae=v==null?void 0:v[0])==null?void 0:Ae.ticketNo)??"",invalid:(z=v==null?void 0:v[0])==null?void 0:z.invalid,etType:(ke=v==null?void 0:v[0])==null?void 0:ke.etType,alreadyInvalid:(x=v==null?void 0:v[0])==null?void 0:x.alreadyInvalid,ticketMachineNumber:"",crsPnrNo:((O=v==null?void 0:v[0])==null?void 0:O.crsPnrNo)??"",ticketTypeCode:((Y=v==null?void 0:v[0])==null?void 0:Y.ticketTypeCode)??"D",governmentPurchase:((fe=v==null?void 0:v[0])==null?void 0:fe.governmentPurchase)??!1,paymentBOP:((ye=v==null?void 0:v[0])==null?void 0:ye.paymentBOP)??!1,ticketManagementOrganizationCode:ce((Se=v==null?void 0:v[0])==null?void 0:Se.ticketManagementOrganizationCode),ticketMachineNumberError:"",ticketOrganizationError:""}},V=async()=>{var Ne,Ae;const v=i.invalidatedTicketQueryGid,{etNumber:G,secondFactor:L}=i.ticketOperationCondition,ae=(await Xt(yo({ticketNo:G,secondFactor:L},v))).data.value,ie=q(ae);ie.invalid&&(h.value.travellerInfoList.push(ie),y.value.push(ie),d.pnrNo=((Ae=(Ne=y.value)==null?void 0:Ne[0])==null?void 0:Ae.crsPnrNo)??"")},B=async(v,G)=>{var ie;const L=we("091P0103"),ae=((ie=(await Xt(ja(G.split("-").length>2?G.substring(0,14):G,L))).data.value)==null?void 0:ie.data)??{};h.value.travellerInfoList[v].ticketMachineNumber=ae.ticket.printNumber,h.value.travellerInfoList[v].paymentBOP=ae.ticket.ticketManagementOrganizationCode==="BOP",h.value.travellerInfoList[v].governmentPurchase=ae.ticket.ticketManagementOrganizationCode==="GP",h.value.travellerInfoList=[...h.value.travellerInfoList],ae.ticket.printNumber&&(h.value.travellerInfoList[v].ticketMachineNumberError="")};return mt(async()=>{var v,G;Q(),(v=i.ticketOperationCondition)!=null&&v.pnrNo&&(d.pnrNo=(G=i.ticketOperationCondition)==null?void 0:G.pnrNo),V()}),Ct(()=>m.value,()=>{m.value||(T.value=!1)}),{closeDialog:se,formData:d,REPEL_TICKET_RULES:k,travellerInfos:h,repelTicketBtnDisabled:$,isCheckAll:A,isCheckAllDisabled:l,queryBtnDisabled:P,showXEPnr:m,checkedXEPnr:T,isQueriedTravelers:b,ticketOrganizationList:S,dealCheckAllOperate:j,checkTravellerIndexList:y,repelTicketFormRef:o,repelTicketPassengerFormRef:_,confirmSearchOperate:w,confirmRepelTicketOperate:ge,queryRtkt:B,clearRepelTicketFormItemValidate:D,clearRepelTicketFormValidate:E,isShowPrintNo:de}},ip=t("i",{class:"iconfont icon-close"},null,-1),lp=[ip],rp={class:"w-full px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 inline-flex"},cp={class:"justify-start items-center gap-1 inline-flex"},up=t("div",{class:"w-4 h-4 relative"},[t("em",{class:"u-icon iconfont icon-warning-circle-fill text-yellow-1 relative bottom-1"})],-1),dp={class:"text-yellow-1 text-xs font-normal leading-tight"},pp={class:"mt-5 mb-[10px]"},fp={class:"w-full h-[auto] p-2.5 bg-gray-0 rounded border border-brand-3 flex-col justify-start items-start inline-flex"},mp={key:0},gp=t("span",null,null,-1),kp={class:"flex items-center w-[176px]"},yp={class:"text-center text-gray-3 text-xs font-normal inline-block bg-gray-7 px-1 py-0.5 text-gray-3 leading-tight"},vp={class:"h-[auto] text-gray-2 text-sm font-normal leading-snug grow flex flex-inline items-center"},hp=t("i",{class:"iconfont icon-ticket-fill mr-1.5 text-gray-400"},null,-1),_p={class:"carType-option-panel"},bp={key:1,class:"w-full text-center text-gray-4"},xp={class:"text-center mt-4 flex justify-center items-center"},Tp={class:"crs-btn-dialog-ui repel-ticket"},Np=Le({__name:"RepelTicketDialog",props:{invalidatedTicketQueryGid:{},ticketOperationCondition:{}},emits:["update:modelValue","reQueryTicket"],setup(a,{emit:i}){const u=i,p=a,{closeDialog:f,formData:d,REPEL_TICKET_RULES:o,travellerInfos:_,repelTicketBtnDisabled:h,queryBtnDisabled:y,isCheckAll:T,isCheckAllDisabled:b,showXEPnr:S,checkedXEPnr:k,isQueriedTravelers:$,dealCheckAllOperate:m,checkTravellerIndexList:A,repelTicketFormRef:l,repelTicketPassengerFormRef:P,ticketOrganizationList:j,confirmSearchOperate:X,confirmRepelTicketOperate:ce,queryRtkt:M,clearRepelTicketFormItemValidate:w,clearRepelTicketFormValidate:W,isShowPrintNo:C}=op(u,p);return(R,N)=>{const K=vt,oe=ct,te=et,D=ut,E=tn,pe=oa,ue=_t,ge=nt,se=Zt,de=en,H=sa,Q=Dt,q=Et,V=rt;return s(),re(V,{title:R.$t("app.agentTicketQuery.repelTicket.repelTicket"),width:"720px","close-on-click-modal":!1,"show-close":!1,"align-center":!0,class:"repel-tikect-dialog tc-input-pad-init",onClose:e(f)},{default:c(()=>{var B;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:N[0]||(N[0]=(...v)=>e(f)&&e(f)(...v))},lp),t("div",rp,[t("div",cp,[up,t("div",dp,n(R.$t("app.agentTicketQuery.repelTicket.repelTicketTip")),1)])]),t("div",pp,[r(D,{ref_key:"repelTicketFormRef",ref:l,model:e(d),rules:e(o),inline:!0,"label-position":"left","require-asterisk-position":"right",class:"w-full"},{default:c(()=>[r(oe,{prop:"pnrNo",label:"PNR"},{default:c(()=>[r(K,{modelValue:e(d).pnrNo,"onUpdate:modelValue":N[1]||(N[1]=v=>e(d).pnrNo=v),placeholder:"PNR",clearable:""},null,8,["modelValue"])]),_:1}),r(oe,null,{default:c(()=>[r(te,{type:"primary","data-gid":"091P0101",disabled:e(y),onClick:e(X)},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.queryBtn")),1)]),_:1},8,["disabled","onClick"])]),_:1})]),_:1},8,["model","rules"])]),t("div",fp,[((B=e(_).travellerInfoList)==null?void 0:B.length)>0?(s(),g(_e,{key:0},[e($)?(s(),g(_e,{key:0},[e($)?(s(),g("p",mp,[r(E,{modelValue:e(T),"onUpdate:modelValue":N[2]||(N[2]=v=>Me(T)?T.value=v:null),disabled:e(b),label:R.$t("app.agentTicketQuery.repelTicket.checkAll"),onChange:e(m)},null,8,["modelValue","disabled","label","onChange"])])):ee("",!0),r(pe,{"border-style":"dashed"})],64)):ee("",!0),r(H,{modelValue:e(A),"onUpdate:modelValue":N[3]||(N[3]=v=>Me(A)?A.value=v:null),onChange:e(W)},{default:c(()=>[r(D,{ref_key:"repelTicketPassengerFormRef",ref:P,model:e(_),rules:e(o),"require-asterisk-position":"right",class:"passengr-form"},{default:c(()=>[(s(!0),g(_e,null,Re(e(_).travellerInfoList,(v,G)=>(s(),g("div",{key:G+new Date,class:"h-auto w-[100%] flex items-center justify-between mb-[10px] last:mb-0"},[r(E,{label:v,class:"single-check",disabled:!v.invalid},{default:c(()=>[gp]),_:2},1032,["label","disabled"]),t("div",kp,[r(ue,{effect:"dark",placement:"top",content:v.passengerNameSuffix},{default:c(()=>[t("span",{class:Pe(["text-gray-1 text-sm font-normal leading-snug mr-2 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e($t)(v.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(v.passengerNameSuffix),3)]),_:2},1032,["content"]),t("span",yp,n(e($t)(v.specialPassengerType)),1)]),t("div",vp,[hp,J(" "+n(v.ticketNo||"--")+" ",1),r(oe,{prop:"travellerInfoList."+G+".ticketManagementOrganizationCode",error:v.ticketOrganizationError,class:"w-[90px] ml-2.5"},{default:c(()=>[r(de,{modelValue:v.ticketManagementOrganizationCode,"onUpdate:modelValue":L=>v.ticketManagementOrganizationCode=L,class:"ticket-management-organization",disabled:v.ticketManagementOrganizationCode==="",placeholder:v.ticketManagementOrganizationCode===""?R.$t("app.agentTicketQuery.noData"):""},{default:c(()=>[(s(!0),g(_e,null,Re(e(j),L=>(s(),re(se,{key:L.value,label:L.label,value:L.value},{default:c(()=>[t("div",_p,[t("div",{class:Pe(v.ticketManagementOrganizationCode===L.value?"show-select":"hidden-select")},[r(ge,null,{default:c(()=>[r(e(Yn))]),_:1})],2),t("span",null,n(L.label),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])]),_:2},1032,["prop","error"])]),e(C)(v.ticketManagementOrganizationCode)?(s(),re(oe,{key:0,prop:"travellerInfoList."+G+".ticketMachineNumber",label:R.$t("app.agentTicketQuery.repelTicket.ticketMachineNumber"),error:v.ticketMachineNumberError},{default:c(()=>[r(Kt,{modelValue:e(_).travellerInfoList[G].ticketMachineNumber,"onUpdate:modelValue":L=>e(_).travellerInfoList[G].ticketMachineNumber=L,"select-class":"w-[120px]",onBlur:L=>e(w)(G)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),r(ue,{content:R.$t("app.agentTicketQuery.repelTicket.queryIssueDeviceNo"),placement:"top"},{default:c(()=>[r(ge,{class:"ml-[4px] cursor-pointer",color:"#455AF7","data-gid":"091P0103",onClick:L=>e(M)(G,v.ticketNo??"")},{default:c(()=>[r(e(Ss))]),_:2},1032,["onClick"])]),_:2},1032,["content"])]),_:2},1032,["prop","label","error"])):ee("",!0)]))),128))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onChange"])],64)):(s(),g("div",bp,n(R.$t("app.agentTicketRefund.noPassengers")),1))]),t("div",xp,[Ue(r(q,{modelValue:e(k),"onUpdate:modelValue":N[4]||(N[4]=v=>Me(k)?k.value=v:null),class:"ml-4"},{default:c(()=>[r(Q,{label:!1},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.repelTicket.onlyRepelTicket")),1)]),_:1}),r(Q,{label:!0},{default:c(()=>{var v;return[J(n(R.$t("app.agentTicketQuery.repelTicket.xePnrAndRepelTicket"))+"(PNR:"+n((v=e(A)[0])==null?void 0:v.crsPnrNo)+")",1)]}),_:1})]),_:1},8,["modelValue"]),[[kn,e(S)]]),t("span",Tp,[r(te,{type:"primary",disabled:e(h),onClick:e(ce)},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.repelTicket.confirmRepelTicket")),1)]),_:1},8,["disabled","onClick"]),r(te,{onClick:e(f)},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.repelTicket.cancel")),1)]),_:1},8,["onClick"])])])]}),_:1},8,["title","onClose"])}}});const $p=a=>{const{t:i}=Ze(),u=Rt(),p=I(!0),f=I(),d=I(""),o=I(!1),_=I(),h=I({authLevel:"oneLevel",office:""}),y={office:[{required:!0,message:i("app.agentTicketQuery.ticketAuth.officeTipOne"),trigger:"blur"},{pattern:hn,message:i("app.agentTicketQuery.ticketAuth.officeTipTwo")}]},T=I(!1),b=I(),S=je(()=>{var M;return((M=u.state.user)==null?void 0:M.agent)??""}),k=async()=>{var M,w,W,C,R,N,K;try{const oe=we("091N0203");o.value=!0;const te={ticketNumber:a.ticketNo},D=await Ba(te,oe);((M=D==null?void 0:D.statusCode)==null?void 0:M.value)===200&&(d.value=((C=(W=(w=D.data)==null?void 0:w.value)==null?void 0:W.data.ticketDisplayAuthInfo)==null?void 0:C.bookOffice)??"",f.value=(K=(N=(R=D.data)==null?void 0:R.value)==null?void 0:N.data.ticketDisplayAuthInfo)==null?void 0:K.authInfoList.filter(E=>E.authTo!==""))}finally{o.value=!1}},$=async M=>{var w;try{o.value=!0;const W={removeOffice:M,ticketNumber:a.ticketNo},C=we("091N0202");((w=(await Un(W,C)).data.value)==null?void 0:w.code)==="200"&&(await lt({message:i("app.agentTicketQuery.ticketAuth.removeAuthSuccess"),type:"success"}),await k())}finally{o.value=!1}},m=()=>{T.value=!0},A=async M=>{M&&await M.validate(async w=>{if(w)try{const W=we("091N0201");o.value=!0;let C=!0;h.value.authLevel==="oneLevel"?C=!1:C=!0;const R={accreditOffice:h.value.office,reAuth:C,ticketNumber:a.ticketNo},N=[];N.push(Qn(R,W)),(await Promise.allSettled(N)).filter(te=>te.value.data.value.code!=="200").length<1&&(await lt({message:i("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await k())}finally{o.value=!1}})},l=async()=>{T.value=!1},P=()=>`TICKET_AUTH_OFFICE_${S.value}_`??"",j=()=>{try{return(JSON.parse(localStorage.getItem(`${P()}add`)??"")||[]).map(w=>({value:w}))}catch{return[]}},X=()=>{if(hn.test(h.value.office)){const M=j().map(W=>W.value).filter(W=>W!==h.value.office);M.unshift(h.value.office);const w=M.slice(0,5);localStorage.setItem(`${P()}add`,JSON.stringify(w))}},ce=M=>{var w;h.value.office=M.value,(w=_.value)==null||w.blur()};return mt(async()=>{p.value=a.modelValue,k()}),Ct(()=>h.value.office,M=>{h.value.office=(M==null?void 0:M.toUpperCase())??""}),{dialogTableVisible:p,tableData:f,authDelete:$,authFormRules:y,authForm:h,authSubmit:A,authTicketFormRef:b,showAdd:m,addAuthVisible:T,office:d,authCancel:l,showLoading:o,officeHistoryRef:_,loadOfficeHistory:j,saveOfficeHistory:X,selectOfficeHistory:ce}},es=a=>(wt("data-v-3b5cf850"),a=a(),St(),a),Rp=es(()=>t("i",{class:"iconfont icon-close"},null,-1)),Cp=[Rp],wp={class:"w-full px-0 pt-0.5 pb-5 bg-gray-0 rounded-md flex-col justify-start gap-5 inline-flex"},Sp={class:"self-stretch justify-between items-center inline-flex"},Pp={class:"text-gray-1 text-lg font-bold leading-normal"},Ap={class:"self-stretch h-full flex-col justify-center items-center gap-2.5 flex w-[632px]"},Dp={class:"self-stretch justify-between items-center inline-flex"},Ep={class:"justify-start items-center gap-[68px] flex"},Op={class:"text-gray-1 text-sm font-normal leading-snug"},Fp={class:"text-gray-1 text-sm font-normal leading-snug"},Vp={class:"flex-col justify-start items-start inline-flex"},Mp={class:"self-stretch p-2.5 rounded border border-brand-3 justify-start items-start inline-flex"},Lp={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},jp={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Bp={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},Ip={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},Up={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Qp={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},qp={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},zp={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},Gp={key:0},Hp={key:1},Yp={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Kp={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Wp={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},Xp=es(()=>t("i",{class:"cursor-pointer primary-color iconfont icon-delete text-brand-2"},null,-1)),Jp={key:0,class:"self-stretch flex-col justify-start items-start gap-3.5 flex add-auth-tkt"},Zp={class:"self-stretch h-9 px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 flex"},ef={class:"h-5 justify-start items-center inline-flex text-yellow-1"},tf={class:"text-yellow-1 text-xs font-normal leading-tight"},nf={class:"w-[632px] justify-start items-center gap-2.5 inline-flex"},af={class:"justify-start items-center gap-2.5 flex"},sf={class:"justify-start items-center flex"},of={class:"text-gray-2 text-xs font-normal leading-tight"},lf={class:"text-gray-2 text-xs font-normal leading-tight"},rf={class:"justify-start items-center flex crs-btn-dialog-ui"},cf=Le({__name:"AuthTicketDialog",props:{ticketNo:{}},emits:["update:modelValue"],setup(a){const i=a,{dialogTableVisible:u,tableData:p,authDelete:f,authForm:d,authFormRules:o,authSubmit:_,authTicketFormRef:h,showAdd:y,addAuthVisible:T,office:b,authCancel:S,showLoading:k,officeHistoryRef:$,loadOfficeHistory:m,saveOfficeHistory:A,selectOfficeHistory:l}=$p(i);return(P,j)=>{const X=et,ce=nt,M=Ma,w=Dt,W=Et,C=ct,R=Xa,N=ut,K=rt,oe=ht;return s(),re(K,{modelValue:e(u),"onUpdate:modelValue":j[4]||(j[4]=te=>Me(u)?u.value=te:null),"align-center":"true",width:"680px","close-on-click-modal":!1,class:"preview-dialog tickets-empower","show-close":!1},{default:c(()=>[Ue((s(),g("div",null,[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:j[0]||(j[0]=te=>P.$emit("update:modelValue",!1))},Cp),t("div",wp,[t("div",Sp,[t("div",Pp,n(P.$t("app.agentTicketQuery.ticketAuth.title")),1)]),t("div",Ap,[t("div",Dp,[t("div",Ep,[t("div",Op,n(P.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+"："+n(P.ticketNo),1),t("div",Fp,n(P.$t("app.agentTicketQuery.ticketAuth.ticketingOfficeNumber"))+"："+n(e(b)),1)]),t("div",Vp,[r(X,{size:"small",type:"primary",link:"",onClick:e(y)},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1},8,["onClick"])])]),t("div",Mp,[t("div",Lp,[t("div",jp,[t("div",Bp,n(P.$t("app.agentTicketQuery.ticketAuth.officeNum")),1)]),(s(!0),g(_e,null,Re(e(p),(te,D)=>(s(),g("div",{key:D,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",Ip,n(te.authTo),1)]))),128))]),t("div",Up,[t("div",Qp,[t("div",qp,n(P.$t("app.agentTicketQuery.ticketAuth.authAuthority")),1)]),(s(!0),g(_e,null,Re(e(p),(te,D)=>(s(),g("div",{key:D,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",zp,[te.reAuth?(s(),g("span",Gp,[r(ce,null,{default:c(()=>[r(e(Xn))]),_:1})])):(s(),g("span",Hp,n(P.$t("app.agentTicketQuery.ticketAuth.noAuth")),1))])]))),128))]),t("div",Yp,[t("div",Kp,[t("div",Wp,n(P.$t("app.agentTicketQuery.ticketAuth.operation")),1)]),(s(!0),g(_e,null,Re(e(p),(te,D)=>(s(),g("div",{key:D,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-5 inline-flex"},[r(M,{title:P.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),icon:e(on),teleported:!1,width:"160","icon-color":"var(--bkc-el-color-primary)","cancel-button-type":"button",onConfirm:E=>e(f)(te.authTo)},{reference:c(()=>[Xp]),_:2},1032,["title","icon","onConfirm"])]))),128))])]),e(T)?(s(),g("div",Jp,[t("div",Zp,[t("div",ef,[r(ce,{class:"tooltip-icon mr-[8px]"},{default:c(()=>[r(e(Wn))]),_:1}),t("div",tf,n(P.$t("app.agentTicketQuery.ticketAuth.addTipsTwo")),1)])]),t("div",nf,[t("div",af,[r(N,{ref_key:"authTicketFormRef",ref:h,model:e(d),inline:"",size:"small",rules:e(o),"require-asterisk-position":"right"},{default:c(()=>[r(C,null,{default:c(()=>[r(W,{modelValue:e(d).authLevel,"onUpdate:modelValue":j[1]||(j[1]=te=>e(d).authLevel=te)},{default:c(()=>[t("div",sf,[r(w,{label:"oneLevel",size:"large"},{default:c(()=>[t("span",of,n(P.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),r(w,{label:"twoLevel",size:"large"},{default:c(()=>[t("span",lf,n(P.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),r(C,{label:P.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:c(()=>[r(R,{ref_key:"officeHistoryRef",ref:$,modelValue:e(d).office,"onUpdate:modelValue":j[2]||(j[2]=te=>e(d).office=te),modelModifiers:{trim:!0},"fetch-suggestions":e(m),placeholder:P.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(l),onBlur:e(A)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t("div",rf,[e(T)?(s(),re(X,{key:0,type:"primary","data-gid":"091N0206",onClick:j[3]||(j[3]=te=>e(_)(e(h)))},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})):ee("",!0),e(T)?(s(),re(X,{key:1,onClick:e(S)},{default:c(()=>[J(n(P.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1},8,["onClick"])):ee("",!0)])])])):ee("",!0)])])])),[[oe,e(k)]])]),_:1},8,["modelValue"])}}});const uf=dt(cf,[["__scopeId","data-v-3b5cf850"]]),df="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let pf=(a=21)=>{let i="",u=crypto.getRandomValues(new Uint8Array(a|=0));for(;a--;)i+=df[u[a]&63];return i};const ff=(a,i)=>{var $;const{t:u}=Ze(),p=Rt(),f=I(),d=it(a.queryForm),{defaultOffice:o,defaultRoleWithPid:_,office:h}=p.getters.user,y=_?o:(($=h==null?void 0:h.split(";"))==null?void 0:$[0])??"",T=it({prntNo:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"change"},{pattern:ba,message:u("app.agentTicketRefund.prntNoError"),trigger:"change"}]}),b=()=>{var m;(m=f.value)==null||m.validate(A=>{var l;if(A){if(((l=p.getters.user)==null?void 0:l.entityType)==="CDS"){Je.alert(u("app.agentTicketRefund.bspTip"),{icon:Ce("em",{class:"iconfont icon-close-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,draggable:!0});return}i("refund",d)}})},S=m=>{m&&ba.test(m)&&As(m,y)},k=()=>{var m;(m=f.value)==null||m.validate(A=>{A&&i("openPreview",!0)})};return mt(async()=>{const m=Ps(y);d.prntNo=m??""}),{paramFormRef:f,forms:d,rules:T,refund:b,setDevice:S,openPreview:k}},Sn=a=>(wt("data-v-58b70490"),a=a(),St(),a),mf=Sn(()=>t("div",{class:"relative top-[8px] text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight"},"*",-1)),gf=Sn(()=>t("span",{class:"iconfont icon-info-circle-line"},null,-1)),kf=Sn(()=>t("div",{class:"relative top-[8px] text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight"},"*",-1)),yf=Sn(()=>t("div",{class:"relative top-[8px] text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight"},"*",-1)),vf=Le({__name:"TicketRefundCondition",props:{queryForm:{}},emits:["refund","openPreview"],setup(a,{emit:i}){const u=i,p=a,{paramFormRef:f,forms:d,rules:o,refund:_,setDevice:h}=ff(p,u);return(y,T)=>{const b=_t,S=vt,k=ct,$=Dt,m=Et,A=et,l=ut;return s(),re(l,{ref_key:"paramFormRef",ref:f,inline:!0,class:"ticket-refund-condition",model:e(d),"label-position":"left","require-asterisk-position":"right"},{default:c(()=>[r(k,{prop:"tktNo",class:"ticket-refund-condition-tkt"},{label:c(()=>[t("label",null,n(y.$t("app.agentTicketRefund.tktNo")),1),mf,r(b,{effect:"dark",content:y.$t("app.agentTicketRefund.conjunctionTip"),placement:"bottom-start"},{default:c(()=>[gf]),_:1},8,["content"])]),default:c(()=>[r(S,{modelValue:e(d).tktNo,"onUpdate:modelValue":T[0]||(T[0]=P=>e(d).tktNo=P),modelModifiers:{trim:!0},disabled:""},null,8,["modelValue"])]),_:1}),r(k,{prop:"prntNo",class:"ticket-refund-condition-prnt",rules:e(o).prntNo},{label:c(()=>[t("label",null,n(y.$t("app.agentTicketRefund.prntNo")),1),kf]),default:c(()=>[r(S,{modelValue:e(d).prntNo,"onUpdate:modelValue":T[1]||(T[1]=P=>e(d).prntNo=P),modelModifiers:{trim:!0},onBlur:T[2]||(T[2]=P=>e(h)(e(d).prntNo))},null,8,["modelValue"])]),_:1},8,["rules"]),r(k,null,{label:c(()=>[t("label",null,n(y.$t("app.agentTicketRefund.rtType")),1),yf]),default:c(()=>[r(m,{modelValue:e(d).volunteer,"onUpdate:modelValue":T[3]||(T[3]=P=>e(d).volunteer=P)},{default:c(()=>[r($,{label:"VOLUNTEER_AUTO"},{default:c(()=>[J(n(y.$t("app.agentTicketRefund.voluntary")),1)]),_:1}),r($,{label:"NON_VOLUNTEER_MANUAL"},{default:c(()=>[J(n(y.$t("app.agentTicketRefund.involuntary")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1}),r(k,null,{default:c(()=>[r(A,{type:"primary",onClick:e(_)},{default:c(()=>[J(n(y.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])}}});const hf=dt(vf,[["__scopeId","data-v-58b70490"]]),_f=a=>(wt("data-v-4a677a5f"),a=a(),St(),a),bf={class:"flex items-center"},xf=_f(()=>t("div",{class:"iconfont icon-info-circle-line text-[var(--bkc-el-color-primary)] mr-[16px]"},null,-1)),Tf={class:"text-lg text-[var(--bkc-color-gray-1)]"},Nf=Le({__name:"RefundSeatDialog",emits:["handle-xepnr","handle-manual-refund","handle-cancel"],setup(a,{emit:i}){const u=i;return(p,f)=>{const d=et,o=rt;return s(),re(o,{class:"refund-seat-dialog","show-close":!1,width:"500px",top:"40vh","close-on-click-modal":!1},{footer:c(()=>[r(d,{type:"primary",onClick:f[0]||(f[0]=_=>u("handle-xepnr"))},{default:c(()=>[J("XEPNR")]),_:1}),r(d,{type:"primary",onClick:f[1]||(f[1]=_=>u("handle-manual-refund"))},{default:c(()=>[J(n(p.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1}),r(d,{onClick:f[2]||(f[2]=_=>u("handle-cancel"))},{default:c(()=>[J(n(p.$t("app.agentTicketRefund.cancel")),1)]),_:1})]),default:c(()=>[t("div",bf,[xf,t("div",Tf,n(p.$t("app.agentTicketRefund.noSeatWithdRawal")),1)])]),_:1})}}});const $f=dt(Nf,[["__scopeId","data-v-4a677a5f"]]),Rf={class:"flex-col justify-start items-start gap-1 inline-flex mt-[14px] w-full"},Cf={class:"self-stretch justify-start items-start inline-flex"},wf={class:"self-stretch p-2.5 rounded border border-[var(--bkc-theme-2)] justify-start items-start gap-5 inline-flex"},Sf={key:0,class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Pf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Af={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Df={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Ef={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Of=t("div",{class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},"-",-1),Ff={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Vf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Mf=t("div",{class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},"-",-1),Lf={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},jf=t("div",{class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},"Office",-1),Bf={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},If={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Uf={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Qf={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},ua=Le({__name:"RefundInfo",props:{packageData:{},titleNormal:{type:Boolean},hideRefundType:{type:Boolean}},setup(a){return(i,u)=>(s(),g("div",Rf,[t("div",Cf,[t("div",{class:Pe(["text-[var(--bkc-color-gray-1)] text-base leading-normal",i.titleNormal?"font-normal":"font-bold"])},n(i.$t("app.agentTicketRefund.refundTicketInfo")),3)]),t("div",wf,[i.hideRefundType?ee("",!0):(s(),g("div",Sf,[t("div",Pf,n(i.$t("app.agentTicketRefund.refundTicketType")),1),t("div",Af,n(i.packageData.volunteer?i.$t("app.agentTicketRefund.refundType."+i.packageData.volunteer):""),1)])),t("div",Df,[t("div",Ef,n(i.$t("app.agentTicketRefund.refundTicketNumber")),1),Of]),t("div",Ff,[t("div",Vf,n(i.$t("app.agentTicketRefund.date")),1),Mf]),t("div",Lf,[jf,t("div",Bf,n(i.packageData.office),1)]),t("div",If,[t("div",Uf,n(i.$t("app.agentTicketRefund.user")),1),t("div",Qf,n(i.packageData.createUser),1)])])]))}}),qf={class:"flex-col justify-start items-start gap-1 inline-flex w-full mt-[14px]"},zf={class:"self-stretch justify-start items-start inline-flex"},Gf={class:"self-stretch p-2.5 rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-start gap-2.5 flex"},Hf={class:"self-stretch justify-start items-start gap-2.5 inline-flex border-dashed border-b border-[var(--bkc-color-gray-5)]"},Yf={class:"pr-10 pb-[6px] justify-start items-start gap-3.5 flex"},Kf={class:"justify-start items-center gap-1 flex"},Wf={class:"text-[var(--bkc-el-color-primary)] text-sm font-normal leading-snug"},Xf=t("em",{class:"iconfont icon-loginuser"},null,-1),Jf={class:"justify-start items-start flex"},Zf={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},em={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},tm={class:"justify-start items-start flex"},nm={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},am={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},sm={class:"justify-start items-start flex"},om={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},im={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},lm={class:"justify-start items-start flex"},rm={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},cm={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},um={key:0,class:"justify-start items-start flex"},dm=t("div",{class:"text-red-1 text-sm font-normal leading-snug"},"RECEIPT PRINTED",-1),pm=[dm],fm={class:"self-stretch flex-col justify-start items-start gap-2.5 flex"},mm={class:"self-stretch justify-start items-start gap-20 inline-flex"},gm={class:"w-[120px] flex-col justify-start items-start inline-flex"},km={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},ym={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},vm={class:"w-[120px] flex-col justify-start items-start inline-flex"},hm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},_m={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},bm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},xm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Tm={class:"w-[150px] flex-col justify-start items-start inline-flex"},Nm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},$m={class:"text-[var(--bkc-el-color-primary)] text-xs font-normal leading-tight mt-1"},Rm={class:"w-[120px] flex-col justify-start items-start inline-flex"},Cm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},wm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Sm={class:"self-stretch justify-start items-start inline-flex"},Pm={class:"text-[var(--bkc-color-gray-2)] text-xs font-bold leading-tight"},Am={class:"self-stretch flex-col justify-start items-start flex mt-1"},Dm={class:"justify-start items-start gap-5 inline-flex"},Em={class:"justify-start items-center flex mt-[3px] mr-[4px]"},Om={key:0,class:"bg-[#EAEAEA] rounded-sm border border-[var(--bkc-color-gray-6)] justify-start items-center flex"},Fm={class:"text-[var(--bkc-color-gray-2)] text-xs font-normal leading-tight"},Vm={class:"text-[var(--bkc-color-gray-5)] text-xs justify-center items-center leading-tight"},ts=Le({__name:"RefundPassenger",props:{packageData:{},titleNormal:{type:Boolean},hideCheck:{type:Boolean}},setup(a){return(i,u)=>{var f,d,o,_,h,y,T,b,S,k,$,m,A,l,P,j,X,ce,M,w,W,C,R,N,K,oe,te,D,E,pe,ue;const p=nt;return s(),g("div",qf,[t("div",zf,[t("div",{class:Pe(["text-[var(--bkc-color-gray-1)] text-base font-bold leading-normal",i.titleNormal?"font-normal":"font-bold"])},n(i.$t("app.agentTicketRefund.rtPassenger")),3)]),t("div",Gf,[t("div",Hf,[t("div",Yf,[t("div",Kf,[t("div",Wf,[Xf,J(n((d=(f=i.packageData)==null?void 0:f.formInfo)==null?void 0:d.name),1)])]),t("div",Jf,[t("div",Zf,n(i.$t("app.agentTicketRefund.ticketNum")),1),t("div",em,n((_=(o=i.packageData)==null?void 0:o.formInfo)==null?void 0:_.ticketNo),1)]),t("div",tm,[t("div",nm,n(i.$t("app.agentTicketRefund.airlineNum")),1),t("div",am,n((y=(h=i.packageData)==null?void 0:h.formInfo)==null?void 0:y.airline),1)]),t("div",sm,[t("div",om,n(i.$t("app.agentTicketRefund.ticketType")),1),t("div",im,n((b=(T=i.packageData)==null?void 0:T.formInfo)==null?void 0:b.tktType),1)]),t("div",lm,[t("div",rm,n(i.$t("app.agentTicketRefund.conjunction")),1),t("div",cm,n((k=(S=i.packageData)==null?void 0:S.formInfo)==null?void 0:k.conjunction),1)]),((m=($=i.packageData)==null?void 0:$.formInfo)==null?void 0:m.receiptPrinted)==="1"?(s(),g("div",um,pm)):ee("",!0)])]),t("div",fm,[t("div",mm,[t("div",gm,[t("div",km,n(i.$t("app.agentTicketRefund.electronic")),1),t("div",ym,n(((l=(A=i.packageData)==null?void 0:A.formInfo)==null?void 0:l.etTag)==="1"?i.$t("app.agentTicketRefund.yes"):i.$t("app.agentTicketRefund.no")),1)]),t("div",vm,[t("div",hm,n(i.$t("app.agentTicketRefund.currency")),1),t("div",_m,n((j=(P=i.packageData)==null?void 0:P.formInfo)==null?void 0:j.currency),1)]),t("div",{class:Pe([{"w-[200px]":((M=(ce=(X=i.packageData)==null?void 0:X.formInfo)==null?void 0:ce.payType)==null?void 0:M.length)>6},"w-[120px] flex-col justify-start items-start inline-flex"])},[t("div",bm,n(i.$t("app.agentTicketRefund.payment")),1),t("div",xm,n(e(za)((W=(w=i.packageData)==null?void 0:w.formInfo)==null?void 0:W.payType)),1)],2),t("div",Tm,[t("div",Nm,n(i.$t("app.agentTicketRefund.creditCard")),1),t("div",$m,n((R=(C=i.packageData)==null?void 0:C.formInfo)!=null&&R.creditCard?i.packageData.formInfo.creditCard:"-"),1)]),t("div",Rm,[t("div",Cm,n(i.$t("app.agentTicketRefund.remark")),1),t("div",wm,n((K=(N=i.packageData)==null?void 0:N.formInfo)!=null&&K.remark?(te=(oe=i.packageData)==null?void 0:oe.formInfo)==null?void 0:te.remark:"-"),1)])]),(s(!0),g(_e,null,Re(e(Ga)((E=(D=i.packageData)==null?void 0:D.formInfo)==null?void 0:E.ticketNo,((ue=(pe=i.packageData)==null?void 0:pe.formInfo)==null?void 0:ue.segment)??[]),(ge,se)=>(s(),g("div",{key:se+"seg",class:"self-stretch flex-col justify-start items-start flex"},[t("div",Sm,[t("div",Pm,n(i.$t("app.agentTicketRefund.couponNo",{a:e(zn)(se)})),1)]),t("div",Am,[t("div",Dm,[(s(!0),g(_e,null,Re(ge,(de,H)=>(s(),g("div",{key:H+"segmet",class:"justify-start items-center flex"},[t("div",Em,[i.hideCheck?ee("",!0):(s(),g("div",Om,[r(p,{size:"14"},{default:c(()=>[de.isAble==="1"?(s(),re(e(Xn),{key:0})):ee("",!0)]),_:2},1024)])),t("div",Fm,n(de.departureCode)+"-"+n(de.arriveCode),1)]),t("div",Vm,n(i.$t(`app.refundForm.number_${H+1}`)),1)]))),128))])])]))),128))])])])}}}),Mm={class:"flex-col justify-start items-start gap-1 inline-flex mt-[14px] w-full"},Lm={class:"justify-between items-start inline-flex"},jm={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Bm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Im={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Um={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Qm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},qm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},zm={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Gm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Hm={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Ym={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Km={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},Wm={key:0,class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Xm={key:1,class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},Jm={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Zm={class:"text-[var(--bkc-color-gray-4)] text-xs font-normal leading-tight"},eg={class:"text-[var(--bkc-color-gray-1)] text-xs font-normal leading-tight mt-1"},tg={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},ng={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},ag={class:"justify-center items-center gap-0.5 inline-flex"},sg={class:"text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight mt-1"},og={class:"u-tax-cont"},ig={class:"u-tax-title"},lg={key:1,class:"u-tax-item"},rg={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},cg={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},ug={class:"justify-start items-center gap-1 inline-flex"},dg={class:"text-[var(--bkc-color-special-red-1)] text-xs font-normal leading-tight mt-1"},pg={class:"self-stretch justify-start items-start gap-5 inline-flex"},fg={class:"justify-start items-start gap-1 flex"},mg={class:"text-[var(--bkc-color-gray-1)] text-base font-normal leading-normal"},gg={class:"text-[var(--bkc-color-special-red-1)] text-base font-bold leading-normal"},kg={class:"justify-start items-start gap-0.5 flex"},yg={class:"text-[var(--bkc-color-gray-1)] text-base font-normal leading-normal"},vg={class:"text-[var(--bkc-color-special-red-1)] text-base font-bold leading-normal"},hg=Le({__name:"RefundAmount",props:{packageData:{},titleNormal:{type:Boolean},refundFormStyle:{type:Boolean}},setup(a){return(i,u)=>{var o,_,h,y,T,b,S,k;const p=nt,f=ia,d=Cn;return s(),g("div",Mm,[t("div",Lm,[t("div",{class:Pe(["text-[var(--bkc-color-gray-1)] text-base leading-normal",i.titleNormal?"font-normal":"font-bold"])},n(i.$t("app.agentTicketRefund.amount")),3)]),t("div",{class:Pe(["self-stretch py-2.5 px-2.5 rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-center gap-2.5 flex",{"gap-0":i.refundFormStyle}])},[Yt(i.$slots,"refund-amount",{},()=>{var $,m,A,l,P,j,X,ce,M,w,W,C,R,N,K,oe,te,D,E,pe,ue,ge,se,de,H,Q,q,V;return[t("div",{class:Pe(["self-stretch justify-start items-start gap-5 inline-flex border-b border-solid border-[var(--bkc-theme-2)]",{"border-b-0 pb-0":i.refundFormStyle,"pb-[10px]":!i.refundFormStyle}])},[t("div",jm,[t("div",Bm,n(i.$t("app.agentTicketRefund.passName")),1),t("div",Im,n((m=($=i.packageData)==null?void 0:$.formInfo)==null?void 0:m.name),1)]),t("div",Um,[t("div",Qm,n(i.$t("app.agentTicketRefund.passType")),1),t("div",qm,n(e(Gn)((l=(A=i.packageData)==null?void 0:A.formInfo)==null?void 0:l.psgType)),1)]),t("div",zm,[t("div",Gm,n(i.$t("app.agentTicketRefund.totalAmount")),1),t("div",Hm,n((j=(P=i.packageData)==null?void 0:P.formInfo)==null?void 0:j.currency)+" "+n((ce=(X=i.packageData)==null?void 0:X.formInfo)==null?void 0:ce.totalAmount),1)]),t("div",Ym,[t("div",Km,n(i.$t("app.agentTicketRefund.commission")),1),((w=(M=i.packageData)==null?void 0:M.formInfo)==null?void 0:w.rate)==="1"?(s(),g("div",Wm,n((C=(W=i.packageData)==null?void 0:W.formInfo)==null?void 0:C.commisionRate)+"%",1)):(s(),g("div",Xm,n((N=(R=i.packageData)==null?void 0:R.formInfo)==null?void 0:N.currency)+" "+n((oe=(K=i.packageData)==null?void 0:K.formInfo)==null?void 0:oe.commision),1))]),t("div",Jm,[t("div",Zm,n(i.$t("app.agentTicketRefund.charge")),1),t("div",eg,n((D=(te=i.packageData)==null?void 0:te.formInfo)==null?void 0:D.currency)+" "+n((pe=(E=i.packageData)==null?void 0:E.formInfo)==null?void 0:pe.otherDeduction),1)]),t("div",tg,[t("div",ng,n(i.$t("app.agentTicketRefund.totalTax")),1),t("div",ag,[t("div",sg,n((ge=(ue=i.packageData)==null?void 0:ue.formInfo)==null?void 0:ge.currency)+" "+n((de=(se=i.packageData)==null?void 0:se.formInfo)==null?void 0:de.totalTaxs),1),i.refundFormStyle?ee("",!0):(s(),re(d,{key:0,placement:"top",title:i.$t("app.agentTicketRefund.taxDetails"),width:176,trigger:"hover","popper-class":"u-detail-tax-popper-crs"},{reference:c(()=>[r(p,{class:"tooltip-icon"},{default:c(()=>[r(e(on))]),_:1})]),default:c(()=>{var B,v;return[t("div",og,[((v=(B=i.packageData)==null?void 0:B.formInfo)==null?void 0:v.taxs.length)>0?(s(),g(_e,{key:0},[t("div",ig,[t("span",null,n(i.$t("app.agentTicketRefund.taxes")),1),t("span",null,n(i.$t("app.agentTicketRefund.taxAmount")),1)]),r(f,{"max-height":"120px",always:!0},{default:c(()=>{var G,L;return[(s(!0),g(_e,null,Re((L=(G=i.packageData)==null?void 0:G.formInfo)==null?void 0:L.taxs,ae=>{var ie,Ne;return s(),g("div",{key:ae.name,class:"u-tax-item"},[t("span",null,n(ae.name),1),t("span",null,n(ae.value)+" "+n((Ne=(ie=i.packageData)==null?void 0:ie.formInfo)==null?void 0:Ne.currency),1)])}),128))]}),_:1})],64)):(s(),g("div",lg,[t("span",null,n(i.$t("app.agentTicketRefund.noData")),1)]))])]}),_:1},8,["title"]))])]),t("div",rg,[t("div",cg,n(i.$t("app.agentTicketRefund.actualSettlementAmount")),1),t("div",ug,[t("div",dg,n((Q=(H=i.packageData)==null?void 0:H.formInfo)==null?void 0:Q.currency)+" "+n((V=(q=i.packageData)==null?void 0:q.formInfo)==null?void 0:V.netRefund),1)])])],2)]},!0),Yt(i.$slots,"taxes",{},void 0,!0),t("div",pg,[t("div",fg,[t("div",mg,n(i.$t("app.agentTicketRefund.totalCharge")),1),t("div",gg,n((_=(o=i.packageData)==null?void 0:o.formInfo)==null?void 0:_.currency)+" "+n((y=(h=i.packageData)==null?void 0:h.formInfo)==null?void 0:y.otherDeduction),1)]),t("div",kg,[t("div",yg,n(i.$t("app.agentTicketRefund.actualAmount")),1),t("div",vg,n((b=(T=i.packageData)==null?void 0:T.formInfo)==null?void 0:b.currency)+" "+n((k=(S=i.packageData)==null?void 0:S.formInfo)==null?void 0:k.netRefund),1)])])],2)])}}});const ns=dt(hg,[["__scopeId","data-v-ec135b40"]]),_g={class:"agent-refund-btn flex pt-[10px] border-t-[1px] border-[var(--bkc-color-gray-5)] border-dashed w-full mt-[10px]"},bg=Le({__name:"TicketRefundBtn",props:{refundType:{},ticketType:{},isReset:{}},emits:["submit","modify","cancel","reset"],setup(a,{emit:i}){const u=a,p=i,f=I("ONLY_REFUND");return Ct(()=>u.isReset,()=>{f.value="ONLY_REFUND"}),(d,o)=>{const _=Dt,h=Et,y=et;return s(),g("div",_g,[d.refundType==="Manual"||d.ticketType==="I"?(s(),re(h,{key:0,modelValue:f.value,"onUpdate:modelValue":o[0]||(o[0]=T=>f.value=T)},{default:c(()=>[r(_,{label:"ONLY_REFUND"},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.onlyRt")),1)]),_:1}),r(_,{label:"PNR_CANCEL"},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.rtCancelPnr")),1)]),_:1}),r(_,{label:"PASSENGER_DELETE"},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.rtDeletePass")),1)]),_:1})]),_:1},8,["modelValue"])):ee("",!0),r(y,{type:"primary",onClick:o[1]||(o[1]=T=>p("submit",f.value))},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.submit")),1)]),_:1}),d.refundType!=="Auto"?(s(),re(y,{key:1,type:"primary",onClick:o[2]||(o[2]=T=>p("reset"))},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.reset")),1)]),_:1})):(s(),re(y,{key:2,type:"primary",onClick:o[3]||(o[3]=T=>p("modify"))},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.edit")),1)]),_:1})),r(y,{onClick:o[4]||(o[4]=T=>p("cancel"))},{default:c(()=>[J(n(d.$t("app.agentTicketRefund.cancel")),1)]),_:1})])}}});const as=dt(bg,[["__scopeId","data-v-dd74d716"]]),da={salesTicketManagementRefundedAig:"ticket-management-refunded-aig",salesTicketManagementDelRefundTicketAig:"ticket-management-del-refund-ticket-aig",salesTicketManagementEditRefundAig:"ticket-management-edit-refund-aig"},xg=a=>{const i=(f,d,o)=>{const _=[],h={appendData:JSON.stringify({segments:(a.formInfo.segment??[]).map(y=>({segmentIndex:y.rph,segmentStatus:"REFUNDED"}))}),description:JSON.stringify({airline:a.formInfo.airline,amount:{commision:(a.formInfo.rate==="0"||a.formInfo.rate==="")&&a.formInfo.commision||"0.0",commisionRate:a.formInfo.rate==="1"&&a.formInfo.commisionRate||"0.0",netRefund:a.formInfo.netRefund,otherDeduction:a.formInfo.otherDeduction||"0.0",totalAmount:a.formInfo.totalAmount||"0.0",totalTaxs:a.formInfo.totalTaxs},createLevel:"WHITE_SCREEN_CMD",creditCardNo:"",currency:a.formInfo.currency,detail:{conjunction:a.formInfo.conjunction,iata:o,office:a.office,printNo:a.prntNo,refundType:a.volunteer},etTag:a.formInfo.etTag,international:a.formInfo.tktType==="I"?"INTL":"DOM",operator:a.createUser,passenger:{name:a.formInfo.name,type:a.formInfo.psgType},payType:a.formInfo.payType,refundNo:d,refundTime:Date.parse(new Date(f).toString()).toString(),segments:(a.formInfo.segment??[]).map(y=>({arriveCode:y.arriveCode,departureCode:y.departureCode,e8Rph:y.e8Rph,isAble:y.isAble,isCheck:y.isAble==="1"?"1":"0",segmentIndex:y.rph,tktTag:y.tktTag})),ticketNo:a.formInfo.ticketNo,tktType:a.formInfo.tktType}),itemDetailNo:0,itemType:"REFUND"};return _.push(h),_},u=()=>{var o;const f=[],d={orderItems:[{actualAmount:(o=a==null?void 0:a.formInfo)!=null&&o.netRefund?Number(a.formInfo.netRefund):0,currency:a.formInfo.currency,itemDetailNo:0,payStatus:"REFUNDED",status:"COMPLETE"}]};return f.push(d),f};return{buildRefundAigStore:(f,d,o)=>{var h;return{actualAmount:(h=a==null?void 0:a.formInfo)!=null&&h.netRefund?Number(a.formInfo.netRefund):0,fromSystem:"SGUI",payStatus:"REFUNDED",status:"COMPLETE",supplyChainOrder:f??"",itemDetails:i(ot(d.slice(0,19)).format("YYYY-MM-DD HH:mm:ss"),f,o),orders:u()}}}},Tg=(a,i)=>{const{t:u}=Ze(),p=Rt(),f=De(!1),{postToAIGPersonalization:d}=la(da.salesTicketManagementRefundedAig),o=()=>{i("update:volunteerFlag","AUTO"),i("update:modelValue",!1),i("cleanData")},_=()=>({commision:["0",""].includes(a.formInfo.rate)?a.formInfo.commision||"0":"",commisionRate:a.formInfo.rate==="1"?a.formInfo.commisionRate||"0":"",netRefund:a.formInfo.netRefund,otherDeduction:a.formInfo.otherDeduction||"0",taxs:a.formInfo.taxs,totalAmount:a.formInfo.totalAmount||"0",totalTaxs:a.formInfo.totalTaxs}),h=()=>({airline:a.formInfo.airline,crsPnrNo:a.formInfo.crsPnrNo,currency:a.formInfo.currency,etTag:a.formInfo.etTag,marketAirline:a.formInfo.marketAirline,name:Ft.encode(a.formInfo.name),payType:a.formInfo.payType,pnr:a.formInfo.pnr,psgType:a.formInfo.psgType,ticketNo:a.formInfo.ticketNo,tktType:a.formInfo.tktType}),y=k=>({modificationType:k,prntNo:a.prntNo,resultpre:{amount:_(),conjunction:a.formInfo.conjunction,creditCard:a.formInfo.creditCard,isCoupon:a.formInfo.isCoupon,office:a.office,operator:a.createUser,remark:a.formInfo.remark,ticket:h(),volunteer:a.volunteer}}),T=(k,$)=>{var A,l,P;const m=((A=p.state.user)==null?void 0:A[k])??"";return m||(((P=(((l=p.state.user)==null?void 0:l[$])??"").split(";"))==null?void 0:P[0])??"")};return{fullscreenLoading:f,submitAutoRefund:async k=>{var A;if((a.formInfo.segment??[]).filter(l=>l.isAble==="1").length===0){lt({type:"warning",message:u("app.agentTicketRefund.selSeg")});return}f.value=!0;const{buildRefundAigStore:$}=xg(a);let m;try{const l=we("09200132");m=(await Oo(y(k),l)).data.value}finally{f.value=!1}(m==null?void 0:m.code)==="200"&&Je.alert(m==null?void 0:m.data.msg,{icon:Ce("em",{class:"iconfont icon-check-circle"}),customClass:"agent-refund-msg-box",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,callback:()=>{o()}});try{const l=$(((A=m==null?void 0:m.data.refundNo)==null?void 0:A[0])??"",(m==null?void 0:m.time)||"",T("defaultSellingGuiIataNum","sellingGuiIataNum"));await d(l)}catch(l){console.error("自动退票入库异常:",l.message)}},closeRefundInfo:o,modify:()=>{i("update:modelValue",!0),i("update:volunteerFlag","MANUAL")}}},Ng={class:"flex flex-col"},$g=Le({__name:"TicketRefundAuto",props:{packageData:{}},emits:["update:volunteerFlag","update:modelValue","cleanData"],setup(a,{emit:i}){const u=a,p=i,{fullscreenLoading:f,submitAutoRefund:d,closeRefundInfo:o,modify:_}=Tg(u.packageData,p);return(h,y)=>{var b,S;const T=ht;return Ue((s(),g("div",Ng,[r(ua,{"package-data":h.packageData},null,8,["package-data"]),r(ts,{"package-data":h.packageData},null,8,["package-data"]),r(ns,{"package-data":h.packageData},null,8,["package-data"]),r(as,{"ticket-type":((S=(b=h.packageData)==null?void 0:b.formInfo)==null?void 0:S.tktType)??"D","refund-type":"Auto",onSubmit:e(d),onModify:e(_),onCancel:e(o)},null,8,["ticket-type","onSubmit","onModify","onCancel"])])),[[T,e(f),void 0,{fullscreen:!0,lock:!0}]])}}}),Rg=(a,i)=>{const{t:u}=Ze(),p=je(()=>it(JSON.parse(JSON.stringify(a.params.taxs)))),f=I([]),d={taxValue:[{required:!0,message:u("app.agentTicketRefund.taxFeesNotEmpty"),trigger:"change"},{pattern:Ks,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxName:[{required:!0,message:u("app.agentTicketRefund.taxNotEmpty"),trigger:"change"}]},o=m=>{const A=m<=9?`0${m}`:`${m}`;return`${u("app.agentTicketRefund.taxes")} ${A}`},_=m=>{m&&f.value.push(m)},h=()=>new Promise(m=>{const A=[];f.value.forEach(l=>{A.push(l.validate(P=>P))}),m(A)}).then(m=>Promise.all(m)).then(m=>Promise.resolve(m.every(A=>A)));return{renderTax:p,taxRule:d,bindFormRef:_,formatTaxName:o,handleConfirm:()=>{h().then(m=>{m&&i("confirm",p.value)})},handleClose:()=>{i("update:modelValue",!1)},handleDelete:m=>{p.value.splice(m,1)},handleAdd:()=>{p.value.push({name:"",value:""})},handleTaxItemInput:(m,A)=>{p.value[A].name=m==null?void 0:m.toUpperCase()},changeVal:(m,A)=>{m[A].value=Math.floor(Number(m[A].value)).toFixed(1)}}},ss=a=>(wt("data-v-703e8cc9"),a=a(),St(),a),Cg={class:"tax-view"},wg={class:"text-gray-1 text-lg font-bold leading-normal"},Sg={class:"py-1.5 px-1.5 mr-2.5 mb-4 rounded bg-[var(--bkc-color-special-yellow-3)] text-[var(--bkc-color-special-yellow-1)]"},Pg=ss(()=>t("em",{class:"iconfont icon-warning-circle-fill mr-2"},null,-1)),Ag={class:"content pl-[75px]"},Dg={class:"tax-input rounded-t-sm border border-solid border-[var(--bkc-el-border-color-lighter)]"},Eg=ss(()=>t("span",{class:"tax-input-line border border-solid border-l-[var(--bkc-el-border-color-lighter)]"},null,-1)),Og=["onClick"],Fg=Le({__name:"TaxDialog",props:{params:{}},emits:["update:modelValue","confirm","close"],setup(a,{emit:i}){const u=a,p=i,{renderTax:f,taxRule:d,bindFormRef:o,formatTaxName:_,handleConfirm:h,handleClose:y,handleDelete:T,handleAdd:b,handleTaxItemInput:S,changeVal:k}=Rg(u,p);return($,m)=>{const A=vt,l=ct,P=nt,j=ut,X=et,ce=rt;return s(),g("div",Cg,[r(ce,{"model-value":u.params.on,"before-close":e(y),class:"tax-dialog","close-on-click-modal":!1},{header:c(()=>[t("div",wg,n($.$t("app.agentTicketRefund.taxEdit")),1)]),footer:c(()=>[t("span",null,[r(X,{type:"primary",onClick:e(h)},{default:c(()=>[J(n($.$t("app.agentTicketRefund.confirm")),1)]),_:1},8,["onClick"]),r(X,{onClick:e(y)},{default:c(()=>[J(n($.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])]),default:c(()=>[t("div",Sg,[Pg,t("span",null,n($.$t("app.agentTicketRefund.taxExemptTips")),1)]),t("div",Ag,[(s(!0),g(_e,null,Re(e(f),(M,w)=>(s(),re(j,{key:w,ref_for:!0,ref:e(o),model:M,class:"all-tax-input ml-[50px]"},{default:c(()=>[r(l,{label:e(_)(w+1)},{default:c(()=>[t("div",Dg,[r(l,{class:"tax-input-one w-[80px]",prop:"name",rules:e(d).taxName},{default:c(()=>[r(A,{modelValue:M.name,"onUpdate:modelValue":W=>M.name=W,placeholder:$.$t("app.agentTicketRefund.taxes"),onInput:W=>e(S)(M.name,w)},null,8,["modelValue","onUpdate:modelValue","placeholder","onInput"])]),_:2},1032,["rules"]),Eg,r(l,{class:"tax-input-two w-[190px]",prop:"value",rules:e(d).taxValue},{default:c(()=>[r(A,{modelValue:M.value,"onUpdate:modelValue":W=>M.value=W,modelModifiers:{trim:!0},placeholder:$.$t("app.agentTicketRefund.taxFees"),onChange:W=>e(k)(e(f),w)},null,8,["modelValue","onUpdate:modelValue","placeholder","onChange"])]),_:2},1032,["rules"])]),e(f).length>1?(s(),g("span",{key:0,onClick:W=>e(T)(w)},[r(P,{size:20,class:"delete-icon text-brand-2 ml-2.5 cursor-pointer"},{default:c(()=>[r(e(Ds))]),_:1})],8,Og)):ee("",!0)]),_:2},1032,["label"])]),_:2},1032,["model"]))),128)),e(f).length<27?(s(),g("div",{key:0,class:"add-tax text-xs mb-2.5 w-[80px] text-[var(--bkc-el-color-primary)] ml-[194px] cursor-pointer",onClick:m[0]||(m[0]=(...M)=>e(b)&&e(b)(...M))},[t("span",null,n($.$t("app.agentTicketRefund.taxAdd")),1)])):ee("",!0)])]),_:1},8,["model-value","before-close"])])}}});const Vg=dt(Fg,[["__scopeId","data-v-703e8cc9"]]),Mg=(a,i,u)=>{const p=()=>{let _=[];const h={};return[...a.segment??[],...a.checkSeg].forEach(T=>{h[T.rph]?h[T.rph].isCheck="1":(h[T.rph]=T,h[T.rph].isCheck="0")}),_=Object.values(h),_},f=(_,h,y,T)=>{const b=[],S={appendData:JSON.stringify({segments:p().map(k=>({segmentIndex:k.rph,segmentStatus:"REFUNDED"}))}),description:JSON.stringify({airline:a.airline,amount:{commision:(a.rate==="0"||a.rate==="")&&a.commision||"0.0",commisionRate:a.rate==="1"&&a.commisionRate||"0.0",netRefund:a.netRefund,otherDeduction:a.otherDeduction||"0.0",totalAmount:a.totalAmount||"0.0",totalTaxs:a.totalTaxs},createLevel:"WHITE_SCREEN_CMD",creditCardNo:"",currency:a.currency,detail:{conjunction:a.conjunction,iata:y,office:i.office,printNo:T,refundType:i.volunteer},etTag:a.etTag,international:a.tktType==="I"?"INTL":"DOM",operator:i.createUser,passenger:{name:a.name,type:a.psgType},payType:a.payType,refundNo:h,refundTime:Date.parse(new Date(_).toString()).toString(),segments:p().map(k=>({arriveCode:k.arriveCode,departureCode:k.departureCode,e8Rph:k.e8Rph,isAble:k.isAble,isCheck:k.isCheck,segmentIndex:k.rph,tktTag:k.tktTag})),ticketNo:a.tktType==="I"?a.ticketNo:u.split("-").join(""),tktType:a.tktType}),itemDetailNo:0,itemType:"REFUND"};return b.push(S),b},d=()=>{const _=[],h={orderItems:[{actualAmount:a!=null&&a.netRefund?Number(a.netRefund):0,currency:a.currency,itemDetailNo:0,payStatus:"REFUNDED",status:"COMPLETE"}]};return _.push(h),_};return{getSegmentInfo:p,buildRefundAigStore:(_,h,y,T)=>({actualAmount:a!=null&&a.netRefund?Number(a.netRefund):0,fromSystem:"SGUI",payStatus:"REFUNDED",status:"COMPLETE",supplyChainOrder:_??"",itemDetails:f(ot(h.slice(0,19)).format("YYYY-MM-DD HH:mm:ss"),_,y,T),orders:d()})}},Lg=(a,i)=>{var te,D,E,pe;const{t:u}=Ze(),p=Rt(),f=it({...a.packageData.formInfo,segmentShow:Ga((D=(te=a.packageData)==null?void 0:te.formInfo)==null?void 0:D.ticketNo,((pe=(E=a.packageData)==null?void 0:E.formInfo)==null?void 0:pe.segment)??[])}),d=De(a.queryForm.tktNo.split("-").join("")),o=je(()=>f.commisionRate&&gt.test(f.commisionRate)&&Number(f.commisionRate)===0),_=je(()=>{var ue;return f.commision&&((ue=f.commision)==null?void 0:ue.toString())==="0"}),h=De(Math.random()),y=I(),T=it({taxs:[],on:!1}),{postToAIGPersonalization:b}=la(da.salesTicketManagementRefundedAig),S=De(!1),k=(ue,ge,se)=>{ge===""?se():ge.slice(0,2)==="IC"?Fa.test(ge)?se():se(u("app.agentTicketRefund.remarkIC")):_n.test(ge)?se():se(u("app.agentTicketRefund.remarkHint"))},$=(ue,ge,se)=>{f.payType==="TC"&&(ge.length===0?se(u("app.agentTicketRefund.creditCardNotEmpty")):!a.isDragonBoatOffice&&!Zn.test(ge)?se(u("app.agentTicketRefund.creditCardInput")):a.isDragonBoatOffice&&!ea.test(ge)&&se(u("app.agentTicketRefund.dragonBoatOfficeInput"))),se()},m=it({etTag:[{required:!0,message:u("app.agentTicketRefund.electronicSel"),trigger:"change"}],currency:[{required:!0,message:u("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:u("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:Jn,message:u("app.agentTicketRefund.paymentInput"),trigger:"change"}],remark:[{validator:k,trigger:"change"}],creditCard:[{validator:$,required:!0,trigger:"change"}],price:[{pattern:gt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],rate:[{pattern:gt,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],taxValue:[{required:!0,message:u("app.agentTicketRefund.taxFeesNotEmpty"),trigger:"change"},{pattern:gt,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxName:[{required:!0,message:u("app.agentTicketRefund.taxNotEmpty"),trigger:"change"}],commision:[{pattern:Ws,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}]}),A=()=>{var ue;((ue=f.payType)==null?void 0:ue.toUpperCase())==="TC"&&(f.creditCard=a.isDragonBoatOffice?na:"")},l=ue=>{ue.target.value!==""&&!wn.some(ge=>ge.label===ue.target.value)&&(f.payType=ue.target.value)},P=()=>{const{totalAmount:ue,totalTaxs:ge,otherDeduction:se,commision:de,commisionRate:H,rate:Q}=f;if(Q==="1"){const q=Lt(jt(Number(ue),Number(H)),100).toString(),V=q.includes(".")?q.substring(0,q.indexOf(".")+3):q;f.netRefund=`${At(Qe(Number(ue),Number(ge)),Qe(Number(se),Number(V)))}`}else f.netRefund=`${At(Qe(Number(ue),Number(ge)),Qe(Number(se),Number(de)))}`},j=()=>{const{commision:ue,commisionRate:ge}=f;f.rate=ge?"1":f.rate=ue?"0":"",P()},X=()=>{let ue=new dn(0);f.taxs.forEach(ge=>{ue=ue.add(new dn(ge.value))}),f.totalTaxs=Number(ue).toFixed(2),P()},ce=ue=>{T.on=!T.on,f.taxs=ue,X()},M=()=>{T.taxs=[],T.on=!1},w=()=>{y.value&&(y.value.resetFields(),f!=null&&f.commision&&(f.commision=Math.round(Number(f.commision)).toString()),f.taxs=a.packageData.formInfo.taxs,f.checkSeg=a.packageData.formInfo.checkSeg,T.taxs=[],P(),h.value=Math.random())},W=(ue,ge)=>{var de,H,Q;const se=((de=p.state.user)==null?void 0:de[ue])??"";return se||(((Q=(((H=p.state.user)==null?void 0:H[ge])??"").split(";"))==null?void 0:Q[0])??"")},C=(ue,ge)=>({modificationType:ue,prntNo:a.queryForm.prntNo,resultpre:{amount:{commision:f.rate==="0"||f.rate===""?f.commision||"0":"",commisionRate:f.rate==="1"?f.commisionRate||"0":"",netRefund:f.netRefund,otherDeduction:f.otherDeduction||"0",taxs:f.taxs,totalAmount:f.totalAmount||"0",totalTaxs:f.totalTaxs},conjunction:f.conjunction,creditCard:f.creditCard,isCoupon:f.isCoupon,office:a.packageData.office,operator:a.packageData.createUser,remark:f.remark,segList:f.segList,ticket:{airline:f.airline,crsPnrNo:f.crsPnrNo,currency:f.currency,etTag:f.etTag,marketAirline:f.marketAirline,name:Ft.encode(f.name),payType:f.payType.toUpperCase(),pnr:f.pnr,psgType:f.psgType,segment:ge.map(de=>({arriveCode:de.arriveCode,departureCode:de.departureCode,e8Rph:de.e8Rph,isAble:de.isAble,rph:de.rph,tktTag:de.tktTag,isCheck:de.isCheck})),ticketNo:f.tktType==="I"?f.ticketNo:d.value.split("-").join(""),tktType:f.tktType},volunteer:a.packageData.volunteer}}),R=async ue=>{var Q;S.value=!0;const{getSegmentInfo:ge,buildRefundAigStore:se}=Mg(f,a.packageData,d.value),de=ge();let H;try{const q=we("09200114");H=(await await Fo(C(ue,de),q)).data.value}finally{S.value=!1}Je.alert(H==null?void 0:H.data.msg,{icon:Ce("em",{class:"iconfont icon-check-circle"}),customClass:"u-msg-box-icon",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,callback:()=>{i("cancel")}});try{const q=se(((Q=H==null?void 0:H.data.refundNo)==null?void 0:Q[0])??"",(H==null?void 0:H.time)||"",W("defaultSellingGuiIataNum","sellingGuiIataNum"),a.queryForm.prntNo);await b(q)}catch(q){console.error("手工退票入库异常:",q.message)}},N=async ue=>{f.taxs.find(se=>se.name==="XT")?Je.confirm(u("app.agentTicketRefund.xtTaxsMsg"),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"u-msg-box-icon",confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1,showCancelButton:!1}):R(ue)},K=async ue=>{y.value&&await y.value.validate(ge=>{if(ge){if((f.checkSeg??[]).length<1){lt.error(u("app.agentTicketRefund.selSeg"));return}N(ue)}})},oe=()=>{T.taxs=f.taxs,T.on=!T.on};return mt(()=>{f!=null&&f.commision&&(f.commision=Math.round(Number(f.commision)).toString()),f!=null&&f.payType&&(f!=null&&f.payType.startsWith("CASH")?f.payType="CASH":f!=null&&f.payType.startsWith("CC")||f!=null&&f.payType.startsWith("TC")?f.payType="TC":f.payType="")}),{fullscreenLoading:S,isReset:h,formRef:y,taxForm:T,judgeCommissionRate:o,judgeCommission:_,editData:f,tktNoTag:d,setCreditCard:A,rules:m,bindPaymentValue:l,handleNetRefund:P,handleRate:j,handleTaxConfirm:ce,handleTaxClose:M,reset:w,submit:K,openTax:oe}},pn=a=>(wt("data-v-42b33ae9"),a=a(),St(),a),jg={class:"flex-col justify-start items-start gap-1 inline-flex w-full"},Bg={class:"w-full self-stretch justify-start items-start inline-flex"},Ig={class:"text-[var(--bkc-color-gray-1)] text-base font-bold leading-normal"},Ug={class:"w-full self-stretch p-2.5 rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-start gap-2.5 flex"},Qg={class:"self-stretch justify-start items-start gap-2.5 inline-flex h-8 border-dashed border-b border-gray-5"},qg={class:"h-[22px] pr-10 justify-start items-start gap-3.5 flex"},zg={class:"justify-start items-center gap-1 flex h-4"},Gg={class:"text-[var(--bkc-el-color-primary)] text-sm font-normal leading-snug"},Hg=pn(()=>t("em",{class:"iconfont icon-loginuser"},null,-1)),Yg={class:"justify-start items-start flex"},Kg={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},Wg={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},Xg={class:"justify-start items-start flex"},Jg={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},Zg={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},ek={class:"justify-start items-start flex"},tk={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},nk={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},ak={class:"justify-start items-start flex"},sk={class:"text-[var(--bkc-color-gray-3)] text-sm font-normal leading-snug"},ok={class:"text-[var(--bkc-color-gray-1)] text-sm font-normal leading-snug"},ik={key:0,class:"justify-start items-start flex"},lk=pn(()=>t("div",{class:"text-[var(--bkc-color-special-red-1)] text-sm font-normal leading-snug"},"RECEIPT PRINTED",-1)),rk=[lk],ck={class:"self-stretch flex-col justify-start items-start"},uk={class:"w-full flex gap-x-[20px]"},dk={class:"w-[20%] electric"},pk={class:"w-[20%]"},fk={class:"w-[20%] pay-type"},mk={class:"w-[20%]"},gk={class:"w-[20%]"},kk={class:"remark-label text-[var(--bkc-color-gray-3)]"},yk=pn(()=>t("span",{class:"iconfont icon-info-circle-line text-[var(--bkc-color-gray-4)]"},null,-1)),vk={key:0,class:"w-[20%]"},hk={class:"ticket"},_k={key:0},bk={class:"self-stretch justify-start items-start gap-1 inline-flex"},xk={class:"text-[var(--bkc-color-gray-2)] text-xs font-bold leading-tight"},Tk={class:"self-stretch flex-col justify-start items-start gap-2.5 flex mt-1"},Nk={class:"justify-start items-start gap-5 inline-flex"},$k={class:"justify-start items-center gap-1 flex"},Rk={class:"text-[var(--bkc-color-gray-2)] text-xs font-normal leading-tight"},Ck={class:"self-stretch justify-start items-start gap-1 inline-flex"},wk={class:"text-[var(--bkc-color-gray-2)] text-xs font-bold leading-tight"},Sk={class:"self-stretch flex-col justify-start items-start gap-2.5 flex mt-1"},Pk={class:"justify-start items-start gap-5 inline-flex"},Ak={class:"justify-start items-center gap-1 flex"},Dk={class:"text-[var(--bkc-color-gray-2)] text-xs font-normal leading-tight"},Ek={class:"w-full flex-col justify-start items-start gap-1 inline-flex refund-amount mt-[14px]"},Ok={class:"self-stretch flex-col justify-start items-start gap-2.5 flex"},Fk={class:"justify-between items-start inline-flex"},Vk={class:"text-[var(--bkc-color-gray-1)] text-base font-bold leading-normal"},Mk={class:"self-stretch py-2.5 bg-[var(--bkc-color-blackout-input-bg)] rounded border border-[var(--bkc-theme-2)] flex-col justify-start items-start gap-2.5 flex"},Lk={class:"self-stretch justify-start items-start gap-2.5 inline-flex h-5"},jk={class:"px-3 justify-start items-start gap-3.5 flex"},Bk={class:"justify-start items-center gap-1 flex h-4"},Ik={class:"text-[var(--bkc-el-color-primary)] text-sm font-normal leading-snug"},Uk=pn(()=>t("em",{class:"iconfont icon-loginuser"},null,-1)),Qk={class:"h-[52px] self-stretch px-2.5 justify-start items-start inline-flex border-solid border-[var(--bkc-theme-2)] border-b gap-x-[20px]"},qk={class:"w-[20%]"},zk={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},Gk={class:"w-[20%]"},Hk={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},Yk={class:"w-[20%] rate"},Kk={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},Wk=pn(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[6px]"},"%",-1)),Xk={key:0,class:"text-[12px] relative bottom-[15px] left-[72px] text-[var(--bkc-color-special-yellow-1)]"},Jk={class:"w-[20%]"},Zk={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},ey={key:0,class:"text-[11px] relative bottom-[15px] left-[72px] text-[var(--bkc-color-special-yellow-1)]"},ty={class:"w-[20%] total-tax"},ny={class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight"},ay={class:"self-stretch px-2.5 justify-start items-start gap-5 inline-flex"},sy={class:"justify-center items-center gap-0.5 flex"},oy={class:"text-[var(--bkc-color-gray-1)] text-base font-normal leading-normal"},iy={class:"text-[var(--bkc-color-special-red-1)] text-base font-bold leading-normal"},ly={class:"text-[var(--bkc-color-gray-4)] rotate-180"},ry=pn(()=>t("span",{class:"iconfont icon-info-circle-line"},null,-1)),cy={class:"my-2.5 flex-col justify-start items-start gap-1 inline-flex w-full"},uy=Le({__name:"RefundEdit",props:{packageData:{},queryForm:{},isDragonBoatOffice:{type:Boolean}},emits:["update:volunteerFlag","update:modelValue","cleanData","cancel"],setup(a,{emit:i}){const u=a,p=i,{rules:f,fullscreenLoading:d,isReset:o,formRef:_,taxForm:h,judgeCommissionRate:y,judgeCommission:T,editData:b,tktNoTag:S,setCreditCard:k,bindPaymentValue:$,handleNetRefund:m,handleRate:A,handleTaxConfirm:l,handleTaxClose:P,reset:j,submit:X,openTax:ce}=Lg(u,p);return(M,w)=>{const W=Dt,C=Et,R=ct,N=vt,K=Zt,oe=en,te=_t,D=tn,E=sa,pe=nt,ue=ut,ge=ht;return s(),g(_e,null,[r(ue,{ref_key:"formRef",ref:_,class:"manual-refund-edit-form mt-[14px]",model:e(b),"label-position":"left","require-asterisk-position":"right"},{default:c(()=>{var se,de,H,Q,q,V,B,v,G,L,ae,ie,Ne,Ae,z,ke;return[Ue((s(),g("div",jg,[t("div",Bg,[t("div",Ig,n(M.$t("app.agentTicketRefund.rtPassenger")),1)]),t("div",Ug,[t("div",Qg,[t("div",qg,[t("div",zg,[t("div",Gg,[Hg,J(n((de=(se=M.packageData)==null?void 0:se.formInfo)==null?void 0:de.name),1)])]),t("div",Yg,[t("div",Kg,n(M.$t("app.agentTicketRefund.ticketNum")),1),t("div",Wg,n((Q=(H=M.packageData)==null?void 0:H.formInfo)==null?void 0:Q.ticketNo),1)]),t("div",Xg,[t("div",Jg,n(M.$t("app.agentTicketRefund.airlineNum")),1),t("div",Zg,n((V=(q=M.packageData)==null?void 0:q.formInfo)==null?void 0:V.airline),1)]),t("div",ek,[t("div",tk,n(M.$t("app.agentTicketRefund.ticketType")),1),t("div",nk,n((v=(B=M.packageData)==null?void 0:B.formInfo)==null?void 0:v.tktType),1)]),t("div",ak,[t("div",sk,n(M.$t("app.agentTicketRefund.conjunction")),1),t("div",ok,n((L=(G=M.packageData)==null?void 0:G.formInfo)==null?void 0:L.conjunction),1)]),((ie=(ae=M.packageData)==null?void 0:ae.formInfo)==null?void 0:ie.receiptPrinted)==="1"?(s(),g("div",ik,rk)):ee("",!0)])]),t("div",ck,[t("div",uk,[t("div",dk,[r(R,{label:M.$t("app.agentTicketRefund.electronic"),prop:"etTag",rules:e(f).etTag},{default:c(()=>[r(C,{modelValue:e(b).etTag,"onUpdate:modelValue":w[0]||(w[0]=x=>e(b).etTag=x),class:"u-radio"},{default:c(()=>[r(W,{label:"1",border:""},{default:c(()=>[J(n(M.$t("app.agentTicketRefund.yes")),1)]),_:1}),r(W,{label:"0",border:""},{default:c(()=>[J(n(M.$t("app.agentTicketRefund.no")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",pk,[r(R,{label:M.$t("app.agentTicketRefund.currency"),prop:"currency",rules:e(f).currency},{default:c(()=>[r(N,{modelValue:e(b).currency,"onUpdate:modelValue":w[1]||(w[1]=x=>e(b).currency=x),clearable:"",placeholder:M.$t("app.agentTicketRefund.currency"),onInput:w[2]||(w[2]=x=>e(b).currency=e(b).currency.toLocaleUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"])]),t("div",fk,[r(R,{label:M.$t("app.agentTicketRefund.payment"),prop:"payType",rules:e(f).payType},{default:c(()=>[r(oe,{modelValue:e(b).payType,"onUpdate:modelValue":w[3]||(w[3]=x=>e(b).payType=x),"popper-class":"popper-payType",class:"select-input",filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:M.$t("app.agentTicketRefund.paymentSel"),clearable:"",onChange:e(k),onBlur:e($)},{default:c(()=>[(s(!0),g(_e,null,Re(e(wn),(x,O)=>(s(),re(K,{key:O,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),Ue(t("div",mk,[r(R,{label:M.$t("app.agentTicketRefund.creditCard"),prop:"creditCard",rules:e(f).creditCard},{default:c(()=>[r(N,{modelValue:e(b).creditCard,"onUpdate:modelValue":w[4]||(w[4]=x=>e(b).creditCard=x),clearable:"",placeholder:M.$t("app.agentTicketRefund.creditCard")},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"])],512),[[kn,e(b).payType==="TC"]]),t("div",gk,[r(R,{label:M.$t("app.agentTicketRefund.remark"),prop:"remark",rules:e(f).remark},{label:c(({label:x})=>[t("span",kk,n(x),1),r(te,{class:"box-item",effect:"dark",content:M.$t("app.agentTicketRefund.remarkTool"),placement:"top"},{default:c(()=>[yk]),_:1},8,["content"])]),default:c(()=>[r(N,{modelValue:e(b).remark,"onUpdate:modelValue":w[5]||(w[5]=x=>e(b).remark=x),maxlength:"16",clearable:"",placeholder:M.$t("app.agentTicketRefund.remarkPlaceholder"),onInput:w[6]||(w[6]=x=>e(b).remark=e(b).remark.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label","rules"])]),e(b).payType!=="TC"?(s(),g("div",vk)):ee("",!0)]),t("div",hk,[e(b).tktType==="D"?(s(!0),g(_e,{key:0},Re(e(b).segmentShow,(x,O)=>(s(),g(_e,{key:O},[e(S)===x[0].tktTag?(s(),g("div",_k,[t("div",bk,[t("div",xk,n(M.$t("app.agentTicketRefund.couponNo",{a:e(zn)(O)})),1)]),t("div",Tk,[t("div",Nk,[r(R,{prop:`segment[${O}]`},{default:c(()=>[r(E,{modelValue:e(b).checkSeg,"onUpdate:modelValue":w[7]||(w[7]=Y=>e(b).checkSeg=Y)},{default:c(()=>[(s(!0),g(_e,null,Re(x,(Y,fe)=>(s(),g("div",{key:fe+"segmet",class:"justify-start items-center gap-1 flex"},[t("div",$k,[r(D,{disabled:Y.isAble==="0",label:Y},{default:c(()=>[t("div",Rk,n(Y.departureCode)+"-"+n(Y.arriveCode),1)]),_:2},1032,["disabled","label"])]),t("div",{class:Pe([{"icon-grey":Y.isAble==="0","icon-blue":Y.isAble==="1"},"text-xs justify-center items-center w-[14px] h-[14px] border-[1px] rounded-full flex"])},n(fe+1),3)]))),128))]),_:2},1032,["modelValue"])]),_:2},1032,["prop"])])])])):ee("",!0)],64))),128)):(s(!0),g(_e,{key:1},Re(e(b).segmentShow,(x,O)=>(s(),g(_e,{key:O},[t("div",Ck,[t("div",wk,n(M.$t("app.agentTicketRefund.couponNo",{a:e(zn)(O)})),1)]),t("div",Sk,[t("div",Pk,[r(R,{prop:`segment[${O}]`},{default:c(()=>[r(E,{modelValue:e(b).checkSeg,"onUpdate:modelValue":w[8]||(w[8]=Y=>e(b).checkSeg=Y)},{default:c(()=>[(s(!0),g(_e,null,Re(x,(Y,fe)=>(s(),g("div",{key:fe+"segmet",class:"justify-start items-center gap-1 flex"},[t("div",Ak,[r(D,{disabled:"",label:Y},{default:c(()=>[t("div",Dk,n(Y.departureCode)+"-"+n(Y.arriveCode),1)]),_:2},1032,["label"])]),t("div",{class:Pe([{"icon-grey":Y.isAble==="0","icon-blue":Y.isAble==="1"},"text-xs justify-center items-center w-[14px] h-[14px] border-[1px] rounded-full flex"])},n(fe+1),3)]))),128))]),_:2},1032,["modelValue"])]),_:2},1032,["prop"])])])],64))),128))])])])])),[[ge,e(d),void 0,{fullscreen:!0,lock:!0}]]),t("div",Ek,[t("div",Ok,[t("div",Fk,[t("div",Vk,n(M.$t("app.agentTicketRefund.amount")),1)]),t("div",Mk,[t("div",Lk,[t("div",jk,[t("div",Bk,[t("div",Ik,[Uk,J(n((Ae=(Ne=M.packageData)==null?void 0:Ne.formInfo)==null?void 0:Ae.name),1)])])])]),t("div",Qk,[t("div",qk,[t("div",zk,[r(R,{label:M.$t("app.agentTicketRefund.totalAmount"),prop:"totalAmount",rules:e(f).price},{default:c(()=>[r(N,{modelValue:e(b).totalAmount,"onUpdate:modelValue":w[9]||(w[9]=x=>e(b).totalAmount=x),modelModifiers:{trim:!0},clearable:"",onChange:w[10]||(w[10]=x=>e(m)())},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",Gk,[t("div",Hk,[r(R,{label:M.$t("app.agentTicketRefund.charge"),prop:"otherDeduction",rules:e(f).price},{default:c(()=>[r(N,{modelValue:e(b).otherDeduction,"onUpdate:modelValue":w[11]||(w[11]=x=>e(b).otherDeduction=x),modelModifiers:{trim:!0},clearable:"",onChange:w[12]||(w[12]=x=>e(m)())},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",Yk,[t("div",Kk,[r(R,{label:M.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(f).rate},{default:c(()=>[r(N,{modelValue:e(b).commisionRate,"onUpdate:modelValue":w[13]||(w[13]=x=>e(b).commisionRate=x),modelModifiers:{trim:!0},disabled:e(b).rate==="0",placeholder:M.$t("app.agentTicketRefund.rate"),clearable:"",class:Pe([e(y)?"warning-input":""]),onChange:w[14]||(w[14]=x=>e(A)())},null,8,["modelValue","disabled","placeholder","class"]),Wk]),_:1},8,["label","rules"]),e(y)?(s(),g("span",Xk,n(M.$t("app.agentTicketRefund.commissionRateTip")),1)):ee("",!0)])]),t("div",Jk,[t("div",Zk,[r(R,{label:M.$t("app.agentTicketRefund.commission"),prop:"commision",rules:e(f).commision},{default:c(()=>[r(N,{modelValue:e(b).commision,"onUpdate:modelValue":w[15]||(w[15]=x=>e(b).commision=x),modelModifiers:{trim:!0},disabled:e(b).rate==="1",placeholder:M.$t("app.agentTicketRefund.commission"),clearable:"",class:Pe([e(T)?"warning-input":""]),onChange:w[16]||(w[16]=x=>e(A)())},null,8,["modelValue","disabled","placeholder","class"])]),_:1},8,["label","rules"]),e(T)?(s(),g("span",ey,n(M.$t("app.agentTicketRefund.commissionTip")),1)):ee("",!0)])]),t("div",ty,[t("div",ny,[r(R,{label:M.$t("app.agentTicketRefund.totalTax"),prop:"totalTaxs",rules:e(f).price},{default:c(()=>[r(N,{modelValue:e(b).totalTaxs,"onUpdate:modelValue":w[17]||(w[17]=x=>e(b).totalTaxs=x),modelModifiers:{trim:!0},disabled:"",placeholder:M.$t("app.agentTicketRefund.taxEditBox"),clearable:""},null,8,["modelValue","placeholder"]),t("div",{class:"whitespace-nowrap text-[var(--bkc-el-color-primary)] cursor-pointer text-xs font-normal leading-tight ml-[6px]",onClick:w[18]||(w[18]=(...x)=>e(ce)&&e(ce)(...x))},[r(pe,null,{default:c(()=>[r(e(Es))]),_:1}),J(n(M.$t("app.agentTicketRefund.taxEdit")),1)])]),_:1},8,["label","rules"])])])]),t("div",ay,[t("div",sy,[t("div",oy,n(M.$t("app.agentTicketRefund.actualSettlementAmount")),1),t("div",iy,n(e(b).currency)+" "+n(Number(e(b).netRefund).toFixed(2)),1),t("div",ly,[r(te,{class:"box-item",effect:"dark",content:M.$t("app.agentTicketRefund.amountReference"),placement:"top"},{default:c(()=>[ry]),_:1},8,["content"])])])])])])]),t("div",cy,[r(as,{"ticket-type":((ke=(z=M.packageData)==null?void 0:z.formInfo)==null?void 0:ke.tktType)??"D","is-reset":e(o),"refund-type":"Manual",onReset:e(j),onCancel:w[19]||(w[19]=x=>p("cancel")),onSubmit:e(X)},null,8,["ticket-type","is-reset","onReset","onSubmit"])])]}),_:1},8,["model"]),e(h).on?(s(),re(Vg,{key:0,modelValue:e(h).on,"onUpdate:modelValue":w[20]||(w[20]=se=>e(h).on=se),params:e(h),onConfirm:e(l),onClose:e(P)},null,8,["modelValue","params","onConfirm","onClose"])):ee("",!0)],64)}}});const dy=dt(uy,[["__scopeId","data-v-42b33ae9"]]),py={class:"flex flex-col"},fy={class:"w-full h-[36px] px-[10px] mt-[10px] bg-yellow-3 rounded border border-yellow-2 flex items-center bg-[var(--bkc-color-special-yellow-3)] text-[var(--bkc-color-special-yellow-1)]"},my=t("em",{class:"iconfont icon-warning-circle-fill mr-2"},null,-1),gy={class:"text-yellow-1 text-xs font-normal leading-tight"},ky=Le({__name:"TicketRefundManual",props:{packageData:{},queryForm:{},isDragonBoatOffice:{type:Boolean}},emits:["update:volunteerFlag","update:modelValue","cleanData","cancel"],setup(a,{emit:i}){const u=i,p=()=>{u("update:volunteerFlag","AUTO"),u("update:modelValue",!1),u("cleanData")};return(f,d)=>(s(),g("div",py,[t("div",fy,[my,t("span",gy,n(f.$t("app.agentTicketRefund.manualNotice")),1)]),r(ua,{"package-data":f.packageData},null,8,["package-data"]),r(dy,{"package-data":f.packageData,"query-form":f.queryForm,"is-dragon-boat-office":f.isDragonBoatOffice,onCancel:p},null,8,["package-data","query-form","is-dragon-boat-office"])]))}}),yy=a=>{var q;const{t:i}=Ze(),u=Rt(),{defaultOffice:p,office:f,defaultRoleWithPid:d}=u.state.user,_=(d?p:((q=f==null?void 0:f.split(";"))==null?void 0:q[0])??"")===qa,h=it({tktNo:a.tktNo,prntNo:"",volunteer:"VOLUNTEER_AUTO"}),y=De(!1),T=De("AUTO"),b=De(!1),S=I({}),k=I({}),$=I({}),m={ticketSucc:!1},A=De(!1),l=De(!1),P=I({}),j=()=>{S.value={},k.value={},$.value={},T.value="AUTO"},X=async V=>{try{const B=we("091Q0101"),v=(await qn(V,B)).data.value;return(v==null?void 0:v.code)!=="200"?(m.ticketSucc=!1,Promise.reject(new Error(v==null?void 0:v.msg))):(m.ticketSucc=!0,v.data)}catch{return Promise.reject({type:"BACK"})}},ce=async(V,B)=>{const v=(await Mo(V,B)).data.value;return(v==null?void 0:v.code)!=="200"?Promise.reject(new Error(v==null?void 0:v.msg)):v.data},M=async()=>{S.value.ticket.isAirportCntl==="1"&&(y.value=!1,await Je.confirm(i("app.agentTicketRefund.apc"),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:i("app.agentTicketRefund.continue"),cancelButtonText:i("app.agentTicketRefund.cancel"),showClose:!1}).catch(()=>Promise.reject({type:"BACK"})))},w=async()=>{h.tktNo.length>14&&S.value.ticket.tktType==="D"&&(y.value=!1,await Je.alert(i("app.agentTicketRefund.singleDomestic"),{icon:Ce("em",{class:"iconfont icon-close-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:i("app.agentTicketRefund.sure"),showClose:!1,draggable:!0}).then(()=>Promise.reject({type:"BACK"})))},W=async V=>{await Je.confirm(V,{icon:Ce("em",{class:"iconfont icon-close-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:i("app.agentTicketRefund.sure"),cancelButtonText:i("app.agentTicketRefund.cancel"),showClose:!1,draggable:!0})},C=V=>{const B=S.value.ticket.segment;return(B??[]).forEach(v=>{V.forEach(G=>{v.rph===G.rph&&(v.isAble=G.isAble)})}),B},R=V=>(V==null?void 0:V.startsWith("CC"))||(V==null?void 0:V.startsWith("TC")),N=()=>{$.value.formInfo.totalTaxs=k.value.msg?S.value.ticket.totalTaxs??"0":k.value.amount.totalTaxs??"0",$.value.formInfo.taxs=k.value.msg?S.value.ticket.taxs??[]:k.value.amount.taxs??[],$.value.formInfo.remark=k.value.remark??"",$.value.formInfo.totalAmount=k.value.amount.totalAmount??"0",$.value.formInfo.commision=k.value.amount.commision,$.value.formInfo.commisionRate=k.value.amount.commisionRate,$.value.formInfo.rate=k.value.amount.commisionRate?"1":"0",k.value.amount.commisionRate==="0"&&k.value.amount.commision==="0"&&($.value.formInfo.commisionRate="",$.value.formInfo.rate="0"),$.value.formInfo.otherDeduction=h.volunteer==="NON_VOLUNTEER_MANUAL"?"0":k.value.amount.otherDeduction??"0",$.value.formInfo.segList=k.value.segList??[],$.value.formInfo.netRefund=k.value.amount.netRefund??"0",$.value.formInfo.payType=S.value.ticket.payType??k.value.payType,$.value.formInfo.creditCard=_&&R($.value.formInfo.payType)?na:k.value.creditCard??"",$.value.formInfo.segment=k.value.segList?C(k.value.segList??[]):S.value.ticket.segment,$.value.formInfo.currency=S.value.ticket.currency??k.value.currency},K=V=>{const{totalAmount:B,totalTaxs:v,otherDeduction:G,commision:L,commisionRate:ae,rate:ie}=V;if(ie==="1"){const Ae=Lt(jt(Number(B),Number(ae)),100).toString(),z=Ae.includes(".")?Ae.substring(0,Ae.indexOf(".")+3):Ae;return At(Qe(Number(B),Number(v)),Qe(Number(G),Number(z))).toFixed(2)}return At(Qe(Number(B),Number(v)),Qe(Number(G),Number(L))).toFixed(2)},oe=V=>{if($.value={office:S.value.office,volunteer:V==="modify"?"VOLUNTEER_MANUAL":h.volunteer,createUser:S.value.operator,prntNo:h.prntNo,formInfo:{name:S.value.ticket.name,marketAirline:S.value.ticket.marketAirline,ticketNo:S.value.ticket.ticketNo,airline:S.value.ticket.airline,tktType:S.value.ticket.tktType,etTag:S.value.ticket.etTag,currency:S.value.ticket.currency,payType:S.value.ticket.payType,segment:S.value.ticket.segment,checkSeg:[],psgType:S.value.ticket.psgType,totalTaxs:S.value.ticket.totalTaxs??"0",taxs:S.value.ticket.taxs??[],receiptPrinted:S.value.receiptPrinted,conjunction:S.value.conjunction,remark:"",creditCard:"",totalAmount:"0",commision:"0",commisionRate:"",rate:"0",otherDeduction:"0",segList:[],netRefund:"0",crsPnrNo:S.value.ticket.crsPnrNo,pnr:S.value.ticket.pnr,isCoupon:S.value.ticket.isCoupon}},k.value.amount&&N(),$.value.formInfo.netRefund=K($.value.formInfo),T.value==="AUTO")$.value.formInfo.checkSeg=($.value.formInfo.segment??[]).filter(B=>B.isAble==="1").map(B=>({...B}));else{const B=h.tktNo.split("-").join("");$.value.formInfo.checkSeg=$.value.formInfo.tktType==="D"?($.value.formInfo.segment??[]).filter(v=>v.isAble==="1"&&v.tktTag===B):($.value.formInfo.segment??[]).filter(v=>v.isAble==="1")}},te=()=>{b.value=!0,T.value="AUTO",oe()},D=V=>{T.value="MANUAL",b.value=!0,oe(V)},E=async V=>{b.value=!1,j();try{const B=we("09200130");if(S.value=await X({tktNo:V.tktNo}),await M(),y.value=!0,k.value=await ce({tktNo:V.tktNo,prntNo:V.prntNo,tktType:S.value.ticket.tktType},B),y.value=!1,k.value.msg){if(k.value.msg===i("app.agentTicketRefund.noSeatWithdRawal")){A.value=!0;return}await W(k.value.msg).then(async()=>{await w(),D("modify")}).catch(()=>Promise.reject({type:"BACK"}))}else te()}catch(B){if(y.value=!1,!m.ticketSucc||(B==null?void 0:B.type)==="BACK")return;h.tktNo.length>14&&S.value.ticket.tktType==="D"?w():await W(i("app.agentTicketRefund.goManualRefund")).then(()=>{D("modify")})}finally{y.value=!1}},pe=async V=>{b.value=!1,j();try{const B=we("09200131");S.value=await X({tktNo:V.tktNo}),await w(),await M(),y.value=!0,k.value=await ce({tktNo:V.tktNo,prntNo:V.prntNo,tktType:S.value.ticket.tktType},B),y.value=!1,D()}catch(B){if(y.value=!1,!m.ticketSucc||(B==null?void 0:B.type)==="BACK")return;D()}finally{y.value=!1}},ue=()=>{A.value=!1},ge=async()=>{var V,B;try{y.value=!0;const v=we("09200133"),G=(await Vo((B=(V=S.value)==null?void 0:V.ticket)==null?void 0:B.crsPnrNo,v)).data.value;y.value=!1,G?lt({message:i("app.agentTicketRefund.xeSuccess"),type:"success"}):lt({message:i("app.agentTicketRefund.xeFaild"),type:"error"})}finally{y.value=!1}};return{isDragonBoatOffice:_,fullscreenLoading:y,showRefundInfo:b,queryForm:h,volunteerFlag:T,showRefundSeatConfirm:A,packageData:$,closeRefundSeatDialog:ue,handleManualRefund:async()=>{ue(),await w(),D("modify")},handleXePnr:()=>{ue(),Je.confirm(i("app.agentTicketRefund.confirmXePnr"),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box",confirmButtonText:i("app.agentTicketRefund.sure"),cancelButtonText:i("app.agentTicketRefund.cancel"),showClose:!1}).then(()=>{ge()}).catch(()=>{A.value=!0})},refund:async V=>{switch(y.value=!0,V.volunteer){case"VOLUNTEER_AUTO":E(V);break;default:pe(V);break}},cleanData:j,showPreview:l,openPreview:async V=>{const B={tktNo:h.tktNo},v=Mt.service({fullscreen:!0});try{const G=we("091T0109"),{data:L}=await Qa(B,G);P.value=L.value,l.value=V}finally{v.close()}},previewInfo:P}},vy=a=>{const i=I({});return(()=>{i.value={office:a.previewInfo.refundTicketOrder.office,prntNo:"",volunteer:"",createUser:a.previewInfo.refundTicketOrder.operator,formInfo:{name:a.previewInfo.refundTicketOrder.ticket.name,marketAirline:a.previewInfo.refundTicketOrder.ticket.marketAirline,ticketNo:a.previewInfo.refundTicketOrder.ticket.ticketNo,airline:a.previewInfo.refundTicketOrder.ticket.airline,tktType:a.previewInfo.refundTicketOrder.ticket.tktType,etTag:a.previewInfo.refundTicketOrder.ticket.etTag,currency:a.previewInfo.refundTicketOrder.ticket.currency??a.previewInfo.refundTicketOrder.ticket.currency,payType:a.previewInfo.refundTicketOrder.ticket.payType??a.previewInfo.refundTicketOrder.ticket.payType,segment:a.previewInfo.refundTicketOrder.ticket.segment,psgType:a.previewInfo.refundTicketOrder.ticket.psgType,totalTaxs:a.previewInfo.refundCompute.amount.totalTaxs?Number(a.previewInfo.refundCompute.amount.totalTaxs).toFixed(2):"",taxs:a.previewInfo.refundCompute.amount.taxs??[],receiptPrinted:a.previewInfo.refundTicketOrder.receiptPrinted,conjunction:a.previewInfo.refundTicketOrder.conjunction,remark:a.previewInfo.refundCompute.remark,creditCard:a.previewInfo.refundCompute.creditCard,totalAmount:a.previewInfo.refundCompute.amount.totalAmount?Number(a.previewInfo.refundCompute.amount.totalAmount).toFixed(2):"",commision:a.previewInfo.refundCompute.amount.commision?Number(a.previewInfo.refundCompute.amount.commision).toFixed(2):"",commisionRate:a.previewInfo.refundCompute.amount.commisionRate?Number(a.previewInfo.refundCompute.amount.commisionRate).toFixed(2):"",rate:a.previewInfo.refundCompute.amount.commisionRate?"1":"0",otherDeduction:a.previewInfo.refundCompute.amount.otherDeduction?Number(a.previewInfo.refundCompute.amount.otherDeduction).toFixed(2):"",netRefund:a.previewInfo.refundCompute.amount.netRefund?Number(a.previewInfo.refundCompute.amount.netRefund).toFixed(2):"",crsPnrNo:"",pnr:"",isCoupon:a.previewInfo.refundTicketOrder.ticket.isCoupon}}})(),{renderData:i}},hy={class:"w-full"},_y={class:"self-stretch justify-start items-start gap-5 inline-flex border-solid border-brand-3 w-full"},by={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},xy={class:"text-gray-4 text-xs font-normal leading-tight"},Ty={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Ny={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},$y={class:"text-gray-4 text-xs font-normal leading-tight truncate"},Ry={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Cy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},wy={class:"text-gray-4 text-xs font-normal leading-tight truncate"},Sy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Py={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},Ay={class:"text-gray-4 text-xs font-normal leading-tight truncate"},Dy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Ey={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},Oy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},Fy={class:"text-gray-4 text-xs font-normal leading-tight truncate"},Vy={class:"text-gray-1 text-xs font-normal leading-tight mt-1 truncate"},My={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},Ly={class:"text-gray-3 text-xs font-normal leading-tight truncate"},jy={class:"justify-center items-center gap-0.5 inline-flex"},By={class:"text-red-1 text-xs font-normal leading-tight mt-1 truncate"},Iy={class:"u-tax-cont"},Uy={class:"u-tax-title"},Qy={key:1,class:"u-tax-item"},qy={class:"flex-1 shrink basis-0 flex-col justify-start items-start inline-flex"},zy={class:"text-gray-3 text-xs font-normal leading-tight truncate"},Gy={class:"justify-start items-center gap-1 inline-flex"},Hy={class:"text-red-1 text-xs font-normal leading-tight mt-1 truncate"},Yy={class:"px-2.5 py-1.5 bg-brand-8 rounded justify-start items-start w-full flex text-gray-3 text-xs"},Ky={class:"mx-[5px]"},Wy=Le({__name:"PreviewDialog",props:{previewInfo:{}},setup(a){const i=a,{renderData:u}=vy(i);return(p,f)=>{const d=_t,o=nt,_=ia,h=Cn,y=rt;return s(),re(y,{title:p.$t("app.agentTicketRefund.preview"),"close-on-press-escape":!1,class:"preview-dialog tc-refund-preview-dialog",width:"1040px"},{default:c(()=>[t("div",hy,[r(ua,{"package-data":e(u),"title-normal":!0,"hide-refund-type":!0},null,8,["package-data"]),r(ts,{"package-data":e(u),"title-normal":!0,"hide-check":!0},null,8,["package-data"]),r(ns,{"package-data":e(u),"title-normal":!0},{"refund-amount":c(()=>{var T,b,S,k,$,m,A,l,P,j,X,ce,M,w,W,C,R,N,K,oe,te,D,E,pe,ue,ge,se,de;return[t("div",_y,[t("div",by,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.passName"),placement:"top"},{default:c(()=>[t("div",xy,n(p.$t("app.agentTicketRefund.passName")),1)]),_:1},8,["content"]),r(d,{effect:"dark",content:(b=(T=e(u))==null?void 0:T.formInfo)==null?void 0:b.name,placement:"top"},{default:c(()=>{var H,Q;return[t("div",Ty,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.name),1)]}),_:1},8,["content"])]),t("div",Ny,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.passType"),placement:"top"},{default:c(()=>[t("div",$y,n(p.$t("app.agentTicketRefund.passType")),1)]),_:1},8,["content"]),r(d,{effect:"dark",content:e(Gn)((k=(S=e(u))==null?void 0:S.formInfo)==null?void 0:k.psgType),placement:"top"},{default:c(()=>{var H,Q;return[t("div",Ry,n(e(Gn)((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.psgType)),1)]}),_:1},8,["content"])]),t("div",Cy,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.totalAmount"),placement:"top"},{default:c(()=>[t("div",wy,n(p.$t("app.agentTicketRefund.totalAmount")),1)]),_:1},8,["content"]),r(d,{effect:"dark",content:`${(m=($=e(u))==null?void 0:$.formInfo)==null?void 0:m.currency} ${(l=(A=e(u))==null?void 0:A.formInfo)==null?void 0:l.totalAmount}`,placement:"top"},{default:c(()=>{var H,Q,q,V;return[t("div",Sy,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.currency)+" "+n((V=(q=e(u))==null?void 0:q.formInfo)==null?void 0:V.totalAmount),1)]}),_:1},8,["content"])]),t("div",Py,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.commission"),placement:"top"},{default:c(()=>[t("div",Ay,n(p.$t("app.agentTicketRefund.commission")),1)]),_:1},8,["content"]),((j=(P=e(u))==null?void 0:P.formInfo)==null?void 0:j.rate)==="1"?(s(),re(d,{key:0,effect:"dark",content:`${(ce=(X=e(u))==null?void 0:X.formInfo)==null?void 0:ce.commisionRate}%`,placement:"top"},{default:c(()=>{var H,Q;return[t("div",Dy,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.commisionRate)+"%",1)]}),_:1},8,["content"])):(s(),re(d,{key:1,effect:"dark",content:`${(w=(M=e(u))==null?void 0:M.formInfo)==null?void 0:w.currency} ${(C=(W=e(u))==null?void 0:W.formInfo)==null?void 0:C.commision}`,placement:"top"},{default:c(()=>{var H,Q,q,V;return[t("div",Ey,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.currency)+" "+n((V=(q=e(u))==null?void 0:q.formInfo)==null?void 0:V.commision),1)]}),_:1},8,["content"]))]),t("div",Oy,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.charge"),placement:"top"},{default:c(()=>[t("div",Fy,n(p.$t("app.agentTicketRefund.charge")),1)]),_:1},8,["content"]),r(d,{effect:"dark",content:`${(N=(R=e(u))==null?void 0:R.formInfo)==null?void 0:N.currency} ${(oe=(K=e(u))==null?void 0:K.formInfo)==null?void 0:oe.otherDeduction}`,placement:"top"},{default:c(()=>{var H,Q,q,V;return[t("div",Vy,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.currency)+" "+n((V=(q=e(u))==null?void 0:q.formInfo)==null?void 0:V.otherDeduction),1)]}),_:1},8,["content"])]),t("div",My,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.totalTax"),placement:"top"},{default:c(()=>[t("div",Ly,n(p.$t("app.agentTicketRefund.totalTax")),1)]),_:1},8,["content"]),t("div",jy,[r(d,{effect:"dark",content:`${(D=(te=e(u))==null?void 0:te.formInfo)==null?void 0:D.currency} ${(pe=(E=e(u))==null?void 0:E.formInfo)==null?void 0:pe.totalTaxs}`,placement:"top"},{default:c(()=>{var H,Q,q,V;return[t("div",By,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.currency)+" "+n((V=(q=e(u))==null?void 0:q.formInfo)==null?void 0:V.totalTaxs),1)]}),_:1},8,["content"]),r(h,{placement:"top",title:p.$t("app.agentTicketRefund.taxDetails"),width:176,trigger:"hover","popper-class":"u-detail-tax-popper-crs"},{reference:c(()=>[r(o,{class:"tooltip-icon"},{default:c(()=>[r(e(on))]),_:1})]),default:c(()=>{var H,Q;return[t("div",Iy,[((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.taxs.length)>0?(s(),g(_e,{key:0},[t("div",Uy,[t("span",null,n(p.$t("app.agentTicketRefund.taxes")),1),t("span",null,n(p.$t("app.agentTicketRefund.taxAmount")),1)]),r(_,{"max-height":"120px",always:!0},{default:c(()=>{var q,V;return[(s(!0),g(_e,null,Re((V=(q=e(u))==null?void 0:q.formInfo)==null?void 0:V.taxs,B=>{var v,G;return s(),g("div",{key:B.name,class:"u-tax-item"},[t("span",null,n(B.name),1),t("span",null,n(Number(B.value).toFixed(2))+" "+n((G=(v=e(u))==null?void 0:v.formInfo)==null?void 0:G.currency),1)])}),128))]}),_:1})],64)):(s(),g("div",Qy,[t("span",null,n(p.$t("app.agentTicketRefund.noData")),1)]))])]}),_:1},8,["title"])])]),t("div",qy,[r(d,{effect:"dark",content:p.$t("app.agentTicketRefund.actualSettlementAmount"),placement:"top"},{default:c(()=>[t("div",zy,n(p.$t("app.agentTicketRefund.actualSettlementAmount")),1)]),_:1},8,["content"]),r(d,{effect:"dark",content:`${(ge=(ue=e(u))==null?void 0:ue.formInfo)==null?void 0:ge.currency} ${(de=(se=e(u))==null?void 0:se.formInfo)==null?void 0:de.netRefund}`,placement:"top"},{default:c(()=>{var H,Q,q,V;return[t("div",Gy,[t("div",Hy,n((Q=(H=e(u))==null?void 0:H.formInfo)==null?void 0:Q.currency)+" "+n((V=(q=e(u))==null?void 0:q.formInfo)==null?void 0:V.netRefund),1)])]}),_:1},8,["content"])])]),t("div",Yy,[(s(!0),g(_e,null,Re(e(u).formInfo.taxs,(H,Q)=>(s(),g("div",{key:Q,class:"mr-[20px]"},[t("span",null,n(p.$t("app.agentTicketRefund.taxes"))+n(H.name),1),t("span",Ky,n(e(u).formInfo.currency),1),t("span",null,n(Number(H.value).toFixed(2)),1)]))),128))])]}),_:1},8,["package-data"])])]),_:1},8,["title"])}}});const Xy={class:"bg-[var(--bkc-el-bg-color)] p-[10px] rounded-lg mt-[6px] min-h-[calc(100vh_-_33vh)] shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},Jy=Le({__name:"TicketRefund",props:{tktNo:{}},setup(a){const i=a,{isDragonBoatOffice:u,volunteerFlag:p,fullscreenLoading:f,showRefundInfo:d,queryForm:o,showRefundSeatConfirm:_,packageData:h,closeRefundSeatDialog:y,handleManualRefund:T,handleXePnr:b,refund:S,cleanData:k,showPreview:$,openPreview:m,previewInfo:A}=yy(i);return(l,P)=>{const j=ht;return Ue((s(),g("div",Xy,[r(hf,{"query-form":e(o),onRefund:e(S),onOpenPreview:e(m)},null,8,["query-form","onRefund","onOpenPreview"]),e(d)&&e(p)==="AUTO"?(s(),re($g,{key:0,modelValue:e(d),"onUpdate:modelValue":P[0]||(P[0]=X=>Me(d)?d.value=X:null),volunteerFlag:e(p),"onUpdate:volunteerFlag":P[1]||(P[1]=X=>Me(p)?p.value=X:null),"package-data":e(h),onCleanData:e(k)},null,8,["modelValue","volunteerFlag","package-data","onCleanData"])):ee("",!0),e(d)&&e(p)==="MANUAL"?(s(),re(ky,{key:1,modelValue:e(d),"onUpdate:modelValue":P[2]||(P[2]=X=>Me(d)?d.value=X:null),"package-data":e(h),"is-dragon-boat-office":e(u),"query-form":e(o)},null,8,["modelValue","package-data","is-dragon-boat-office","query-form"])):ee("",!0),e(_)?(s(),re($f,{key:2,modelValue:e(_),"onUpdate:modelValue":P[3]||(P[3]=X=>Me(_)?_.value=X:null),onHandleManualRefund:e(T),onHandleCancel:e(y),onHandleXepnr:e(b)},null,8,["modelValue","onHandleManualRefund","onHandleCancel","onHandleXepnr"])):ee("",!0),e($)?(s(),re(Wy,{key:3,modelValue:e($),"onUpdate:modelValue":P[4]||(P[4]=X=>Me($)?$.value=X:null),"preview-info":e(A)},null,8,["modelValue","preview-info"])):ee("",!0)])),[[j,e(f),void 0,{fullscreen:!0,lock:!0}]])}}});const Zy=(a,i)=>{const{t:u}=Ze(),{copy:p,isSupported:f}=Aa({legacy:!0}),d=I({}),o=De(!1),_=I(!1),h=I(""),y=I(!1),T=I([]),b=I([]),S=Z=>be=>{be&&(T.value[Z]={el:be.$el||be})},k=Z=>be=>{be&&(b.value[Z]=be)},$=I([]),m=Z=>{$.value[Z]=!0},A=Z=>{$.value[Z]=!1},l=I(!1),P=je(()=>w.value.tssType=="Suspend"?u("app.ticketStatus.suspendFlightConfirm"):u("app.ticketStatus.unsuspendFlightConfirm")),j=je(()=>w.value.tssType=="Suspend"?u("app.ticketStatus.suspendFlightCheck"):u("app.ticketStatus.unsuspendFlightCheck")),X=I({pnrTss:!1,issueDate:""}),ce=I(),M=I(""),w=I({tssType:"",ticketNo:"",pnrNo:""}),W=De({render(){return Ce("em",{class:"iconfont icon-calendar"})}}),C={issueDate:[{validator:(Z,be,qe)=>le(!!X.value.pnrTss,Z,be,qe),trigger:"blur"}]},R=De(!1),N=I(!1),K=De(!1),oe=De(!1),te=I(""),D=I(""),E=I(""),pe=I(""),ue=I({}),ge={segId:0,couponStatus:"",ticketNumber:"",printNo:"",ticketManagementOrganizationCode:""},se=I({}),de=De(""),H=De(""),Q=Z=>Z==null?void 0:Z.some(be=>be==="OPEN FOR USE"),q=Z=>Z==null?void 0:Z.some(be=>Ka.includes(be)),V=Z=>Z==null?void 0:Z.every(be=>be==="OPEN FOR USE"),B=Z=>Z==null?void 0:Z.some(be=>["REFUNDED","USED/FLOWN"].includes(be)),v=Z=>Z==null?void 0:Z.every(be=>be==="REFUNDED"),G=Z=>Z==null?void 0:Z.every(be=>["EXCHANGED","FIM EXCH"].includes(be)),L=Z=>Z==null?void 0:Z.some(be=>be==="SUSPENDED"),ae=Z=>Z==null?void 0:Z.every(be=>be==="VOID"),ie=Z=>Z.includes("CHECKED IN")||Z.includes("LIFT/BOARDED"),Ne=Z=>(Z==null?void 0:Z.some(be=>be==="OPEN FOR USE"))&&(Z==null?void 0:Z.every(be=>!["EXCHANGED","REFUNDED"].includes(be))),Ae=Z=>(Z==null?void 0:Z.some(be=>["OPEN FOR USE","AIRPORT CNTL"].includes(be)))&&(Z==null?void 0:Z.every(be=>!["EXCHANGED","REFUNDED"].includes(be))),z=Z=>["OPEN FOR USE","EXCHANGED"].every(be=>Z.includes(be))||["OPEN FOR USE","FIM EXCH"].every(be=>Z.includes(be)),ke=Z=>q(Z),x=Z=>V(Z)||v(Z)||G(Z)||Q(Z),O=Z=>V(Z)||z(Z)||Ne(Z),Y=Z=>Z==null?void 0:Z.every(be=>["VOID","REFUNDED","USED/FLOWN","EXCHANGED","SUSPENDED"].includes(be)),fe=Os({ticketType:"",ticketNo:"",ticketManagementOrganizationCode:"",secondFactor:{secondFactorCode:"",secondFactorValue:""}}),ye=I([]),Se=I([]),Fe=it({pnrNo:"",ticketTypeCode:"",etNumber:"",secondFactor:{secondFactorCode:"",secondFactorValue:""}}),Be=De(!1),Ke=Z=>(Z||"").replace(/\sINF\((.+?)\)/g,""),ve=(Z,be)=>{var qe;switch(M.value=we("091P0101"),Fe.etNumber=Z.etNumber,Fe.ticketTypeCode=Z.ticketTypeCode??"D",Fe.secondFactor=Z.secondFactor,Fe.pnrNo=((qe=Z.airSeg.find(Ie=>Ie.crsPnrNo&&Ie.flightNo!=="ARNK"))==null?void 0:qe.crsPnrNo)??"",be){case"repealTicket":N.value=!0;break}},Ye=I(!1),le=(Z,be,qe,Ie)=>{if(!qe&&Z){Ie(new Error(u("app.agentTicketRefund.must")));return}Ie()},pt=Z=>{pe.value=Z,Ye.value=!0},at=Z=>{const be={title:`${u("app.agentTicketRefund.refund")}${Z}`,name:`refund?type=ticketNo&sign=${Z}`,content:Jt(Jy)};i("addNewTab",be)},xe=(Z,be)=>{const qe={title:`${u("app.agentTicketRefund.newRefund")}${Z}`,name:`newRefund?type=ticketNo&sign=${Z}`,content:Jt(xn)};i("addNewTab",qe,"",be)},F=(Z,be)=>{w.value.tssType=be,w.value.ticketNo=Z.etNumber??"",w.value.pnrNo=Z.airSegCrsPnr??"",X.value.issueDate=Z.issueTicketDate?ot(Z.issueTicketDate).format("YYYY-MM-DD"):"",l.value=!0},Te=async()=>{const Z=we("091T0202");a.queryType==="2"&&X.value.pnrTss?ce.value.validate(async be=>{be&&(await Xt(vo({pnrNo:w.value.pnrNo,tssType:w.value.tssType,issueDate:ot(X.value.issueDate).format("YYYY-MM-DD")},Z)),await Ge(),l.value=!1)}):(await Xt(ho({ticketNumber:w.value.ticketNo,tssType:w.value.tssType},Z)),await Ge(),l.value=!1)},$e=async(Z,be)=>{var Ie,kt;const qe=Mt.service({fullscreen:!0});try{const ft=await To(Z,be);return fe.ticketManagementOrganizationCode=((kt=(Ie=ft==null?void 0:ft.data)==null?void 0:Ie.value)==null?void 0:kt.data)??"",!0}catch{return!0}finally{qe.close()}},Ee=async(Z,be)=>{te.value="changeTicketStatus",ge.ticketNumber=Z,ge.segId=be.segmentIndex,ge.couponStatus=be.ticketStatus==="AIRPORT CNTL"?"OPEN FOR USE":be.ticketStatus;const qe=we("091T0204");if(!await $e(Z,qe))return;const Ie=be.ticketStatus==="REFUNDED"?'<span class="status-open"> OPEN FOR USE </span>':'<span class="status-refunded"> REFUNDED </span>',kt=be.ticketStatus==="OPEN FOR USE"||be.ticketStatus==="AIRPORT CNTL"?`${u("app.ticketStatus.generate")}`:`${u("app.ticketStatus.delete")}`;await Je.confirm(`<div class="message-header w-[508px]">
        <em class="iconfont icon-warning-circle-fill"></em>
        <span>${u("app.ticketStatus.tip")}${kt}。</span>
      </div>
     <div class="message-cotent mb-[18px]">
      <em class="iconfont icon-info-circle-line"></em>
      <span style="color: var(--bkc-color-gray-1);font-size: 18px !important;">${u("app.ticketStatus.changeStatus")}<span/>
      <span>${Ie}${u("app.ticketStatus.status")}?</span>
     </div>`,{customClass:"ticket-status-dialog crs-btn-ui crs-btn-message-ui",confirmButtonText:u("app.ticketStatus.confirmBtn"),cancelButtonText:u("app.ticketStatus.cancelBtn"),dangerouslyUseHTMLString:!0,showClose:!1}).then(()=>{R.value=!0})},Oe=async(Z,be,qe,Ie)=>{var ft,Qt;D.value=be??"",E.value=qe??"",fe.ticketManagementOrganizationCode=Ie;const kt=Mt.service({fullscreen:!0});if(te.value==="ticketRefundForm"){const xt={ticketNo:fe.ticketNo,ticketType:qe||fe.ticketType,printerNo:be,ticketManagementOrganizationCode:Ie,secondFactor:fe.secondFactor};try{if(Be.value){const{data:Tt}=await _o(xt,Z);se.value=(ft=Tt.value)==null?void 0:ft.data,oe.value=!0}else{const{data:Tt}=await ln(xt,Z);se.value=(Qt=Tt.value)==null?void 0:Qt.data,se.value.ticketManagementOrganizationCode=Ie,y.value=!0}}finally{kt.close()}}else{ge.printNo=D.value,ge.ticketManagementOrganizationCode=Ie??"";try{const xt=we("091T0201");(await bo(ge,xt)).data.value==="OK"&&Ge()}finally{kt.close()}}},Xe=(Z,be)=>{const qe=we("091T0104");st(Z,be,qe)},tt=(Z,be)=>{const qe=we("091T0103");st(Z,be,qe)},st=async(Z,be,qe)=>{const Ie=Z&&be.ticketTypeCode==="I"&&be.conjunctiveTicket?be.conjunctiveTicket.slice(0,14):be.etNumber;!Z&&!await $e(Ie,qe)||(fe.ticketNo=Ie,fe.ticketType=be.ticketTypeCode??"",fe.secondFactor=be.secondFactor,Be.value=Z,R.value=!Z,te.value="ticketRefundForm",Z&&Oe(qe))},Ge=()=>{i("reQueryTicket")},We=async Z=>{const be=we("091T0109"),{etNumber:qe,secondFactor:Ie}=Z,kt=Mt.service({fullscreen:!0});try{const{data:ft}=await Qa({tktNo:qe,secondFactor:Ie},be);d.value=ft.value,o.value=!0}finally{kt.close()}},It=Z=>{const be=`/v2/crs/pnrManagement?pnrNo=${Z}`;yn.setLink(be)},Ut=(Z,be,qe)=>{oe.value=!1,de.value=Z,H.value=be,fe.ticketManagementOrganizationCode=qe,K.value=!0},bt=async(Z,be,qe)=>{var ft;Be.value=!1,te.value="ticketRefundForm";const Ie=Mt.service({fullscreen:!0}),kt={ticketNo:fe.ticketNo,ticketType:E.value?E.value:fe.ticketType,printerNo:be,ticketManagementOrganizationCode:qe,refundNo:Z,secondFactor:fe.secondFactor};try{const Qt=we("091T0104"),{data:xt}=await ln(kt,Qt);se.value=(ft=xt.value)==null?void 0:ft.data,se.value.ticketManagementOrganizationCode=fe.ticketManagementOrganizationCode,y.value=!0}finally{Ie.close()}},an=async(Z,be,qe)=>{const Ie=`<p class="text-gray-1 text-lg font-normal">${u("app.agentTicketQuery.pullControlTip")}<span class="text-green-1 text-lg font-bold pl-1">OPEN FOR USE</span></p>
      <p class="text-gray-1 text-lg font-normal">${u("app.agentTicketQuery.pullControlConfirmTip")}</p>`;await Je.confirm(Ie,{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:u("app.ticketStatus.confirmBtn"),cancelButtonText:u("app.ticketStatus.cancelBtn"),dangerouslyUseHTMLString:!0,showClose:!1});const kt={couponNo:[Z+1],remoteAirline:be??"",ticketNo:qe??""},ft=we("091T0203");await Xt(xo(kt,ft)),await Ge()},Pn=(Z,be,qe)=>{if(be==="INF"){Je.confirm(u("app.agentTicketRefund.notAllowInfantOnlyChange"),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",showCancelButton:!1,confirmButtonText:u("app.agentTicketRefund.sure"),showClose:!1});return}_.value=!0,h.value=Z,ue.value=qe},An=Z=>{f&&(p(Z),lt({message:u("app.batchRefund.copySuccess"),type:"success"}))},Dn=()=>{var Z;(Z=ce.value)==null||Z.resetFields(),w.value.tssType="",w.value.ticketNo="",w.value.pnrNo="",X.value.issueDate="",X.value.pnrTss=!1,l.value=!1},En=Z=>{ye.value[Z].showTktPopover=!1};return Ct(()=>a.queryTicketRes,()=>{a.queryType!=="4"?(ye.value=yt(a.queryTicketRes),(ye.value??[]).forEach((Z,be)=>{var ft,Qt;const qe=[...new Set((ft=Z.airSeg)==null?void 0:ft.filter(xt=>xt.flightNo!=="ARNK").map(xt=>{var Tt;return(Tt=xt.ticketStatus)==null?void 0:Tt.trim()}))],Ie=[...new Set((Qt=Z.airSeg)==null?void 0:Qt.map(xt=>{var Tt;return(Tt=xt.ticketStatus)==null?void 0:Tt.trim()}))];Z.key=pf(),Z.isOpen=V(Ie),Z.showTktPopover=a.queryType==="1"&&be===0,ae(Ie)||ie(Ie)||(Z.canRefund=ke(Ie),Z.canRebook=O(Ie),Z.canVoid=V(Ie)||Ae(Ie),Z.canTktAuth=x(qe),Z.canSuspended=V(Ie)||Ne(Ie),Z.canUnSuspended=L(Ie),Z.canQueryAndApplyRefund=B(Ie),Z.canSupplementaryRefundApply=B(Ie),Z.canChange=!Y(Ie))})):Se.value=yt(a.queryTicketRes)},{immediate:!0,deep:!0}),{showSupplementRefundDialog:oe,showPreview:o,previewInfo:d,openPreview:We,openChange:Pn,ticketList:ye,ticketListByName:Se,getPassengerName:Ke,ticketOperation:ve,printerType:E,isShowRepealTicketDialog:N,ticketOperationCondition:Fe,refundOperationCondition:fe,goToRefund:at,goToNewRefund:xe,reQueryTicket:Ge,goToPnrManage:It,authTicketClick:pt,authTicketShow:Ye,stssChangeTicketStatusClick:F,changeStatus:Ee,printNoDialog:R,openDialog:Oe,showTicketRefundFormDialog:y,viewRefundFormWithGid:Xe,viewSupplementRefundWithGid:tt,printerNo:D,isSupplementRefund:Be,refundTicketData:se,showSupplementSuccessDialog:K,openSupplementSuccessDialog:Ut,invoiceNumber:de,openRefundDialog:bt,tktNumber:pe,pullControlPower:an,changeDialogShow:_,changeTicketNo:h,copyInfo:An,changeFactor:ue,tssForm:X,tssFormRef:ce,datePrefix:W,showTssDialog:l,closeDialog:Dn,TSS_FORM_RULES:C,confirmTss:Te,tssTip:P,tssCheckLabel:j,refundPrintNo:H,invalidatedTicketQueryGid:M,realRefs:T,setRealRefs:S,setPopoverRefs:k,showTicketOriginalContainerList:$,show:m,hide:A,closePopover:En}},ev=a=>{const{t:i}=Ze(),u=I(),p=I(),f=k=>u.value=(k==null?void 0:k.$el)||k,d=I(!1),o=()=>{d.value=!0},_=()=>{d.value=!1},h=I(""),y=I(!1),T=I(""),b=(k="")=>{var $,m,A;k&&(y.value=($=k==null?void 0:k.toUpperCase())==null?void 0:$.startsWith("CC"),h.value=`${((m=k.split("/"))==null?void 0:m[1])??""}${((A=k.split("/"))==null?void 0:A[2])??""}`)},S=(k="")=>{var $;($=k==null?void 0:k.toUpperCase())!=null&&$.startsWith("CC")?(T.value=i("app.agentTicketRefund.tcCredit"),b(k)):T.value=za(k)};return mt(()=>{var k;S((k=a.previewInfo.refundTicketOrder)==null?void 0:k.ticket.payType)}),{creditCardNo:h,isCreditCard:y,payMethod:T,realRef:u,setRealRef:f,popoverRef:p,showTicketOriginalContainer:d,show:o,hide:_}},tv={class:"w-full p-2.5 bg-brand-4 flex-wrap flex justify-between"},nv={class:"main-title"},av={class:"mb-1 flex items-center"},sv={key:0,class:"text-brand-2 text-base font-bold leading-normal"},ov={key:1},iv={key:2,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] ml-2.5"},lv={key:3,class:"iconfont icon-inf mr-[4px] text-gray-4 ml-5"},rv={key:4,class:"iconfont icon-user-fill mr-[4px] text-gray-4 ml-5"},cv={class:"text-base font-bold text-gray-1"},uv={class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs ml-[4px]"},dv={class:"text-sm text-gray-3"},pv={class:"text-gray-1 text-sm mr-3.5"},fv={class:"text-sm text-gray-3"},mv={class:"text-gray-1 text-sm mr-3.5"},gv={class:"text-sm text-gray-3"},kv={class:"text-gray-1 text-sm mr-3.5"},yv={key:0},vv={class:"text-sm text-gray-3"},hv={class:"text-sm text-gray-1"},_v=Le({__name:"RefundInfo",props:{previewInfo:{}},setup(a){const i=a,{creditCardNo:u,isCreditCard:p,payMethod:f}=ev(i);return(d,o)=>{var _,h,y,T,b,S,k,$,m,A,l;return s(),g("div",tv,[t("div",nv,[t("span",av,[Number(d.previewInfo.refundTicketOrder.conjunction)>1?(s(),g("span",sv,n(e(Gt)((_=d.previewInfo.refundTicketOrder)==null?void 0:_.ticket.ticketNo)),1)):(s(),g("div",ov,[r(un,{"tkt-index":(h=d.previewInfo.refundTicketOrder)==null?void 0:h.ticket.ticketNo,"ticket-number":(y=d.previewInfo.refundTicketOrder)==null?void 0:y.ticket.ticketNo,"second-factor":d.previewInfo.secondFactor,"refund-class-type":"0"},null,8,["tkt-index","ticket-number","second-factor"])])),(T=d.previewInfo.refundTicketOrder)!=null&&T.ticket.governmentPurchase?(s(),g("div",iv,"GP")):ee("",!0),((S=(b=d.previewInfo.refundTicketOrder)==null?void 0:b.ticket)==null?void 0:S.specialPassengerType)==="INF"?(s(),g("em",lv)):(s(),g("em",rv)),t("span",cv,n((k=d.previewInfo.refundTicketOrder)==null?void 0:k.ticket.passengerNameSuffix),1),t("span",uv,n(e($t)(((m=($=d.previewInfo.refundTicketOrder)==null?void 0:$.ticket)==null?void 0:m.specialPassengerType)??"ADT")),1)])]),t("div",{class:Pe([e(p)?"w-full":""])},[t("span",dv,n(d.$t("app.agentTicketRefund.oldTicketNo"))+"：",1),t("span",pv,n(e(Gt)(((A=d.previewInfo.refundTicketOrder.ticket)==null?void 0:A.exchangeTktNo)??"")||"-"),1),t("span",fv,n(d.$t("app.agentTicketRefund.electronic"))+"：",1),t("span",mv,n(((l=d.previewInfo.refundTicketOrder)==null?void 0:l.ticket.etTag)==="1"?d.$t("app.agentTicketRefund.yes"):d.$t("app.agentTicketRefund.no")),1),t("span",gv,n(d.$t("app.agentTicketRefund.payment"))+"：",1),t("span",kv,n(e(f)),1),e(p)?(s(),g("span",yv,[t("span",vv,n(`${d.$t("app.agentTicketRefund.creditCard")}：`),1),t("span",hv,n(e(u)),1)])):ee("",!0)],2)])}}}),os=a=>(wt("data-v-df3bf26a"),a=a(),St(),a),bv=os(()=>t("i",{class:"iconfont icon-close"},null,-1)),xv=[bv],Tv={class:"w-full"},Nv={class:"mt-2.5 mb-1 text-sm font-bold text-gray-1 leading-[22px]"},$v={class:"border rounded border-brand-3 mb-2.5"},Rv={key:0,class:"w-full h-5 justify-start items-center gap-2.5 inline-flex align-c"},Cv=os(()=>t("div",{class:"w-full border-t-[1px] border-dashed border-gray-6"},null,-1)),wv={class:"w-[22%] flex items-center"},Sv={key:0,class:"mr-2.5 text-gray-1"},Pv={key:1},Av={class:"mr-2.5 text-gray-1"},Dv={key:2,class:"text-yellow-1 w-[42px] px-1 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Ev={class:"text-gray-1"},Ov={class:"w-[22%]"},Fv={key:0},Vv={class:"mr-5 text-gray-1"},Mv={class:"text-gray-3"},Lv={key:1,class:"text-yellow-1 w-[42px] px-1 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},jv={class:"w-[28%] text-gray-1"},Bv={class:"mr-2.5"},Iv={class:"w-[28%] items-center"},Uv={class:"p-2.5 flex justify-between h-12 items-center border-t border-dashed border-brand-6"},Qv={class:"flex items-center text-sm"},qv={class:"text-gray-3 mr-3.5"},zv={class:"text-gray-1"},Gv={class:"mr-1 text-gray-3"},Hv={class:"text-gray-1 cursor-pointer border-b-[1.5px] border-dashed tax-detail"},Yv={class:"text-gray-1 mr-2.5"},Kv={class:"text-red-1"},Wv={class:"text-gray-1 mr-2.5"},Xv={class:"text-red-1"},Jv={class:"text-gray-1 mr-2.5"},Zv={class:"text-red-1"},eh={class:"text-gray-1 mr-2.5"},th={class:"text-red-1"},nh={class:"text-sm font-bold text-gray-1"},ah={class:"text-base text-red-1 font-bold mr-2.5"},sh={class:"text-sm font-bold text-gray-1"},oh={class:"text-base font-bold text-red-1"},ih={class:"mt-5 text-center crs-btn-dialog-ui"},lh=Le({__name:"PreviewDialog",props:{previewInfo:{}},emits:["update:modelValue"],setup(a){const i=p=>{const f=[];return p.forEach(d=>{const o=f.findIndex(_=>_.find(h=>h.tktTag===d.tktTag));o>-1?f[o].push(d):f.push([d])}),f},u=p=>{const f=p.trim()??"";return Bt[f]&&Bt[f].color||""};return(p,f)=>{const d=et,o=rt;return s(),re(o,{title:p.$t("app.agentTicketRefund.preview"),class:"preview-dialog tc-refund-preview-dialog",width:"1040px","align-center":"true","close-on-click-modal":!1,"show-close":!1,onClose:f[2]||(f[2]=_=>p.$emit("update:modelValue",!1))},{default:c(()=>{var _;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:f[0]||(f[0]=h=>p.$emit("update:modelValue",!1))},xv),t("div",Tv,[t("div",Nv,n(p.$t("app.refundForm.refundPassenger")),1),t("div",$v,[r(_v,{"preview-info":p.previewInfo},null,8,["preview-info"]),(s(!0),g(_e,null,Re(i(((_=p.previewInfo.refundTicketOrder)==null?void 0:_.ticket.segment)??[]),(h,y)=>(s(),g("div",{key:y+"seg",class:"px-2.5 pt-2"},[Number(p.previewInfo.refundTicketOrder.conjunction)>1?(s(),g("div",Rv,[r(un,{"tkt-index":h[0].tktTag,"ticket-number":h[0].tktTag,"second-factor":p.previewInfo.secondFactor,"refund-class-type":"1"},null,8,["tkt-index","ticket-number","second-factor"]),Cv])):ee("",!0),(s(!0),g(_e,null,Re(h,(T,b)=>(s(),g("div",{key:b,class:"flex py-1"},[t("div",wv,[T.segmentType==="3"&&T.airline?(s(),g("div",Sv,n(T.airline),1)):ee("",!0),T.segmentType==="2"?(s(),g("div",Pv,[t("span",Av,n(T.flightNo),1)])):(s(),g("div",Dv,n(T.segmentType==="3"?"OPEN":"ARNK"),1)),t("div",Ev,n(T.cabinCode),1)]),t("div",Ov,[T.segmentType==="2"?(s(),g("div",Fv,[t("span",Vv,n(T.departureDate),1),t("span",Mv,n(T.departureTime),1)])):(s(),g("div",Lv,n(T.segmentType==="3"?"OPEN":"ARNK"),1))]),t("div",jv,[t("span",Bv,n(`${T.departureCode}-${T.arriveCode}`),1)]),t("div",Iv,[t("span",{class:Pe(["font-bold mr-2.5",u(T.ticketStatus??"")])},n(T.ticketStatus),3)])]))),128))]))),128)),t("div",Uv,[t("div",Qv,[t("div",qv,[J(n(p.$t("app.agentTicketRefund.totalTicketAmount")),1),t("span",zv,n(p.previewInfo.refundTicketOrder.ticket.currency)+" "+n(p.previewInfo.refundTicketOrder.ticket.totalAmount),1)]),t("div",Gv,[J(n(p.$t("app.agentTicketRefund.totalTaxAmount"))+" ",1),r(Hn,{class:"!mt-0",ticket:p.previewInfo.refundTicketOrder.ticket,index:0},{"tax-deatil":c(()=>[t("span",Hv,n(p.previewInfo.refundTicketOrder.ticket.currency)+" "+n(p.previewInfo.refundTicketOrder.ticket.totalTaxs),1)]),_:1},8,["ticket"])])]),t("div",null,[t("span",Yv,[J(n(p.$t("app.agentTicketRefund.commission"))+" ",1),t("span",Kv,n(p.previewInfo.refundCompute.amount.commision?`${p.previewInfo.refundTicketOrder.ticket.currency} ${p.previewInfo.refundCompute.amount.commision}`:"--"),1)]),t("span",Wv,[J(n(p.$t("app.agentTicketRefund.commissionRate"))+" ",1),t("span",Xv,n(p.previewInfo.refundCompute.amount.commisionRate?`${p.previewInfo.refundCompute.amount.commisionRate}%`:"--"),1)]),t("span",Jv,[J(n(p.$t("app.agentTicketRefund.charge"))+" ",1),t("span",Zv,n(p.previewInfo.refundTicketOrder.ticket.currency)+" "+n(p.previewInfo.refundCompute.amount.otherDeduction),1)]),t("span",eh,[J(n(p.$t("app.agentTicketRefund.totalRefund"))+" ",1),t("span",th,n(p.previewInfo.refundTicketOrder.ticket.currency)+" "+n(p.previewInfo.refundCompute.amount.netRefund),1)])])])]),t("span",nh,n(p.$t("app.agentTicketRefund.charge")),1),t("span",ah,n(p.previewInfo.refundTicketOrder.ticket.currency)+" "+n(p.previewInfo.refundCompute.amount.otherDeduction),1),t("span",sh,n(p.$t("app.agentTicketRefund.totalRefund")),1),t("span",oh,n(p.previewInfo.refundTicketOrder.ticket.currency)+" "+n(p.previewInfo.refundCompute.amount.netRefund),1),t("div",ih,[r(d,{onClick:f[1]||(f[1]=h=>p.$emit("update:modelValue",!1))},{default:c(()=>[J(n(p.$t("app.agentTicketRefund.close")),1)]),_:1})])])]}),_:1},8,["title"])}}});const rh=dt(lh,[["__scopeId","data-v-df3bf26a"]]),ch=(a,i)=>{const u=()=>{i("update:modelValue",!1)};return{queryTkt:()=>{i("toQueryTicket"),u()},closeDialog:u,queryTSL:()=>{yn.setLink("/v2/crs/salesDaily"),u()},checkingReturnTkt:()=>{i("openRefundDialog",a.invoiceNumber,a.refundPrinterNo,a.ticketManagementOrganizationCode),u()}}},uh=t("i",{class:"iconfont icon-close"},null,-1),dh=[uh],ph={class:"flex text-[18px] items-center"},fh={class:"text-[var(--bkc-color-gray-1)] leading-6"},mh={class:"text-[14px] text-[var(--bkc-color-gray-1)]"},gh={class:"flex items-center pl-[30px]"},kh={class:"flex justify-end"},yh=Le({__name:"SupplementSuccess",props:{invoiceNumber:{},refundPrinterNo:{},ticketManagementOrganizationCode:{}},emits:["openRefundDialog","toQueryTicket","update:modelValue"],setup(a,{emit:i}){const u=i,p=a,{closeDialog:f,queryTkt:d,queryTSL:o,checkingReturnTkt:_}=ch(p,u);return(h,y)=>{const T=nt,b=et,S=rt;return s(),re(S,{"close-on-press-escape":!1,"close-on-click-modal":!1,width:"500px","show-close":!1,class:"supplement-success","align-center":"",onClose:e(f)},{header:c(()=>[t("div",ph,[r(T,{size:"36px",class:"success-color mr-2.5"},{default:c(()=>[r(e(Kn))]),_:1}),t("span",fh,n(h.$t("app.refundForm.supplementaryRefundSuccess")),1)])]),default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:y[0]||(y[0]=(...k)=>e(f)&&e(f)(...k))},dh),t("div",mh,[t("div",gh,[t("span",null,n(h.$t("app.refundForm.invoiceNumber")),1),t("span",{class:"text-[var(--bkc-el-color-primary)] cursor-pointer mr-2.5",onClick:y[1]||(y[1]=(...k)=>e(_)&&e(_)(...k))},n(p.invoiceNumber),1),r(b,{onClick:e(o)},{default:c(()=>[J(n(h.$t("app.refundForm.checkTslSettlement")),1)]),_:1},8,["onClick"])]),t("div",kh,[r(b,{type:"primary",onClick:y[2]||(y[2]=k=>e(d)())},{default:c(()=>[J(n(h.$t("app.refundForm.confirmBtn")),1)]),_:1})])])]),_:1},8,["onClose"])}}});const vh=(a,i)=>{const{t:u}=Ze(),p=De(!1),f=I(),d=I({}),o=I([]),_=I([]),h=()=>{i("update:modelValue",!1)},y=(C,R,N)=>{const K=C.split("");return K[R]=N,K.join("")},T=C=>{const R={};C.forEach(oe=>{R[oe.conjunctionIndex]?R[oe.conjunctionIndex].push(oe):R[oe.conjunctionIndex]=[oe]});const N=["0000","0000","0000","0000"];let K=0;for(const oe in R)R[oe].forEach(te=>{N[K]=y(N[K],Number(te.etSegIndex)-1,te.etSegIndex)}),K++;return N},b=C=>({refund:C.etTagNew?"Y":"N",currency:C.currency,payMethod:C.payType,remark:C.remarkInfo??"",creditCard:C.creditCard?C.creditCard:"",couponNos:T(C.checkedSeg??[]),name:Ft.encode(C.name.trim()),airlineCode:C.airline}),S=C=>{const R=[];return C.forEach(N=>{if(N.taxAmount&&N.taxCode){const K={taxCode:N.taxCode,taxAmount:Number(N.taxAmount)};R.push(K)}}),R},k=C=>({commission:C.commision&&Number(C.commision)>0?C.commision.toString():"0",commissionRate:C.commisionRate.toString()??"",grossRefund:C.totalAmount.toString(),deduction:C.otherDeduction.toString(),netRefund:C.netRefund.toString(),taxInfos:S(C.taxs)}),$=()=>a.refundTicketData.ticketType==="I"&&a.refundTicketData.ticketNo!==a.refundTicketData.ticketNoEnd?`${a.refundTicketData.ticketNo}-${a.refundTicketData.ticketNoEnd}`:a.refundTicketData.ticketNo,m=C=>({ticketNo:$(),ticketType:C.tktType,printerNo:C.prntNo,ticketManagementOrganizationCode:C.ticketManagementOrganizationCode??"",refundFormPassengerItem:b(C),refundFormPriceItem:k(C)}),A=async C=>{var K,oe,te;p.value=!0;const R=m(C);let N;try{N=(await No(R,"091T0108")).data.value;let D=((K=N==null?void 0:N.data)==null?void 0:K.refundNumber)??"";C.ticketManagementOrganizationCode!=="ARL"&&(D=(oe=N==null?void 0:N.data)!=null&&oe.refundNumber?(te=N==null?void 0:N.data)==null?void 0:te.refundNumber.substring(3):""),i("openSupplementSuccessDialog",D,C.prntNo??"",C.ticketManagementOrganizationCode??"")}finally{p.value=!1}},l=async()=>{var N,K;const C=(N=f.value)==null?void 0:N.getFormDate();if((C.checkedSeg??[]).length<1){lt({type:"warning",message:u("app.agentTicketRefund.selSeg")});return}await((K=f.value)==null?void 0:K.validate())&&A(C)},P=()=>{var C;(C=f.value)==null||C.resetForm()},j=C=>{const R=[];for(const N in C)C[N].forEach(K=>{if(K.ticketStatus==="REFUNDED"){const oe={etSegIndex:K.etSegIndex,deptCity:K.deptCity,arrivalCity:K.arrivalCity,ticketStatus:K.ticketStatus,select:K.select,etNo:K.etNo,conjunctionIndex:K.conjunctionIndex};R.push(oe)}});return R},X=(C,R)=>Nt(C)?R.toString().endsWith(".00")?R.toString().slice(0,-3):R:R.toFixed(2),ce=(C,R)=>{let N=[];return N=C.map(K=>({taxCode:K.taxCode,taxAmount:X(R,Number(K.taxAmount))})),N.length<10?N.concat(new Array(10-N.length).fill({taxCode:"",taxAmount:""})).map(K=>({...K})):N},M=C=>{let R=C;return(C.startsWith("CC")||C.startsWith("TC"))&&(R="TC"),R},w=C=>{_.value=ce(C.taxInfos,C.currency),d.value={refundNo:"",refundType:u("app.refundForm.manualRefundType"),iata:C.iataNo,agent:C.agent,office:C.office,refundDate:ot(new Date).format("DDMMMYY/HHmm").toUpperCase(),volunteer:"supplement",createUser:C.operator,prntNo:a.printerNo,name:C.passengerName,psgType:C.passengerType,remark:C.remark?C.remark.substring(2):"",remarkCode:C.remark?C.remark.substring(0,2):"",creditCard:C.creditCard,conjunction:C.conjunction,airline:C.airlineCode,tktType:C.ticketType,ticketManagementOrganizationCode:C.ticketManagementOrganizationCode,payType:M(C.payMethod),ticketNo:C.ticketNo!==C.ticketNoEnd&&C.ticketType==="I"?`${C.ticketNo}-${C.ticketNoEnd.slice(-2)}`:C.ticketNo,ticketNoView:C.ticketNoView,totalAmount:X(C.currency,Number(C.grossRefund)),commision:X(C.currency,Number(C.commission)),commisionRate:Number(C.commissionRate)>0?X(C.currency,Number(C.commissionRate)):"",otherDeduction:X(C.currency,Number(C.deduction)),netRefund:X(C.currency,Number(C.netRefund)),totalTaxs:X(C.currency,Number(C.totalTaxs)),taxs:ce(C.taxInfos,C.currency),rate:C.commission>0||C.commissionRate<=0?"0":"1",currency:C.currency,etTagNew:!1,checkedSeg:j(a.refundTicketData.segmentInfos),international:"",couponNos:[]}},W=C=>{o.value=[];for(const R in C){const N=[];C[R].forEach(K=>{const oe={etSegIndex:K.etSegIndex,deptCity:K.deptCity,arrivalCity:K.arrivalCity,ticketStatus:K.ticketStatus,select:K.select,etNo:K.etNo,conjunctionIndex:K.conjunctionIndex};N.push(oe)}),o.value.push(N)}};return mt(()=>{w(a.refundTicketData),W(a.refundTicketData.segmentInfos)}),{taxsHistory:_,supplementRefundData:d,supplementSegmentData:o,refundFormRef:f,fullscreenLoading:p,handleCommit:l,closeSupplementDialog:h,initFormData:P}},hh=t("i",{class:"iconfont icon-close"},null,-1),_h=[hh],bh={class:"mt-[10px] pt-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},xh=Le({__name:"SupplementRefund",props:{printerNo:{},refundTicketData:{}},emits:["update:modelValue","openSupplementSuccessDialog"],setup(a,{emit:i}){const u=a,p=i,{taxsHistory:f,supplementRefundData:d,supplementSegmentData:o,refundFormRef:_,fullscreenLoading:h,closeSupplementDialog:y,handleCommit:T,initFormData:b}=vh(u,p);return(S,k)=>{const $=et,m=rt,A=ht;return s(),re(m,{title:S.$t("app.refundForm.supplementaryRefundInfo"),"show-close":!1,width:"1040px",class:"refund-form-dialog","close-on-click-modal":!1,onClose:e(y)},{default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:k[0]||(k[0]=(...l)=>e(y)&&e(y)(...l))},_h),r(Io,{ref_key:"refundFormRef",ref:_,data:e(d),"taxs-history":e(f),"refund-tickets":e(o),"is-supplement-refund":!0},null,8,["data","taxs-history","refund-tickets"]),t("div",bh,[Ue((s(),re($,{type:"primary",onClick:e(T)},{default:c(()=>[J(n(S.$t("app.agentTicketRefund.submit")),1)]),_:1},8,["onClick"])),[[A,e(h),void 0,{fullscreen:!0,lock:!0}]]),r($,{onClick:e(b)},{default:c(()=>[J(n(S.$t("app.agentTicketRefund.reset")),1)]),_:1},8,["onClick"]),r($,{onClick:e(y)},{default:c(()=>[J(n(S.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])]),_:1},8,["title","onClose"])}}}),Th=(a,i)=>{const u=I(""),p=I(!1),f=I(!1),d=()=>{const y=[];return(a.flightList??[]).forEach(T=>{T.airSeg.forEach(b=>{b.isSelected&&y.push(b)})}),y},o=()=>{i("getChosenSegment",d()),i("queryPassenger")},_=()=>{const y=a.flightList.map(T=>T.etNumber);i("queryRtkt",y)},h=y=>y.some(T=>T.flightNo==="OPEN"&&!["VOID","REFUNDED","USED/FLOWN","EXCHANGED"].includes(T.ticketStatus));return mt(()=>{var y,T;if((a.flightList??[]).length>1){const b=a.flightList.length-1,S=a.flightList[b].etNumber.slice(-2);u.value=`${a.flightList[0].etNumber}-${S}`}else u.value=((T=(y=a.flightList)==null?void 0:y[0])==null?void 0:T.etNumber)??"";p.value=a.flightList.some(b=>b.governmentPurchase),f.value=a.flightList.some(b=>h(b.airSeg))}),{ticketNo:u,isGovernmentPurchase:p,isShowRtktBtn:f,changeSelected:o,handleQueryRtkt:_}},Nh={class:"w-[1012px] pb-2 rounded border border-brand-3 flex-col justify-start items-start gap-2 inline-flex change-flight-info"},$h={class:"self-stretch p-2.5 bg-brand-4 rounded-tl rounded-tr border border-brand-3 justify-between items-center inline-flex"},Rh={class:"justify-start items-center flex"},Ch={class:"justify-start items-start gap-2 flex mr-2.5"},wh={class:"text-brand-2 text-base font-bold leading-normal"},Sh={key:0,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] mr-2.5"},Ph={class:"justify-start items-center gap-2.5 flex ml-2.5"},Ah={class:"justify-start items-center gap-1 flex"},Dh={key:0,class:"iconfont icon-inf mr-[4px] text-gray-4"},Eh={key:1,class:"iconfont icon-user-fill mr-[4px] text-gray-4"},Oh={class:"text-gray-1 text-base font-bold leading-normal"},Fh={class:"px-1 bg-gray-7 rounded-sm justify-center items-center gap-2.5 flex h-5"},Vh={class:"text-center text-gray-3 text-xs font-normal leading-tight"},Mh={key:0,class:"self-stretch px-2.5 justify-start items-center gap-2.5 inline-flex"},Lh={class:"text-brand-2 text-xs font-bold leading-tight"},jh=t("div",{class:"grow shrink basis-0 h-[0px] border border-dotted border-gray-6"},null,-1),Bh={class:"self-stretch px-2.5 flex-col justify-start items-start flex"},Ih={class:"w-[200px] self-stretch justify-start items-center gap-5 flex"},Uh={class:"justify-start items-center gap-1 flex"},Qh={class:"justify-start items-center gap-2.5 flex"},qh={class:"w-[110px] justify-start items-center flex"},zh={key:0,class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Gh={class:"text-gray-1 text-sm font-normal leading-snug"},Hh={class:"w-[172px] justify-start items-center flex"},Yh={class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Kh={class:"text-gray-3 text-sm font-normal leading-snug"},Wh={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-sm"},Xh={class:"w-[280px] self-stretch justify-start items-center gap-2.5 flex"},Jh={class:"w-24 text-gray-1 text-sm font-normal leading-snug"},Zh={key:0,class:"px-1 bg-yellow-2 rounded-sm justify-start items-start gap-2.5 flex"},e_={class:"text-center text-yellow-1 text-xs font-normal leading-tight"},t_={key:1,class:"w-7 h-5 relative"},n_={class:"justify-start items-start gap-0.5 flex"},a_={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},s_={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},o_={key:2,class:"text-brand-2 text-sm font-normal leading-snug"},i_={class:"w-[300px] self-stretch px-1 justify-start items-center gap-3 flex"},l_=Le({__name:"FlightInfo",props:{flightList:{}},emits:["queryPassenger","getChosenSegment","queryRtkt"],setup(a,{emit:i}){const u=i,p=a,{ticketNo:f,isGovernmentPurchase:d,isShowRtktBtn:o,changeSelected:_,handleQueryRtkt:h}=Th(p,u);return(y,T)=>{const b=et,S=tn;return s(),g("div",Nh,[t("div",$h,[t("div",Rh,[t("div",Ch,[t("div",wh,n(e(f)),1)]),e(d)?(s(),g("div",Sh,"GP")):ee("",!0),t("div",Ph,[t("div",Ah,[y.flightList[0].specialPassengerType==="INF"?(s(),g("em",Dh)):(s(),g("em",Eh)),t("div",Oh,n(y.flightList[0].passengerNameSuffix),1),t("div",Fh,[t("div",Vh,n(e($t)(y.flightList[0].specialPassengerType)),1)])])])]),e(o)?(s(),re(b,{key:0,"data-gid":"091V0801",onClick:T[0]||(T[0]=k=>e(h)())},{default:c(()=>[J("RTKT")]),_:1})):ee("",!0)]),(s(!0),g(_e,null,Re(y.flightList,(k,$)=>(s(),g(_e,{key:$},[y.flightList.length>1?(s(),g("div",Mh,[t("div",Lh,n(k.etNumber),1),jh])):ee("",!0),t("div",Bh,[(s(!0),g(_e,null,Re(k.airSeg,m=>{var A,l;return s(),g("div",{key:m.segmentIndex,class:"self-stretch h-9 rounded justify-between items-center inline-flex"},[t("div",Ih,[t("div",Uh,[t("div",Qh,[r(S,{modelValue:m.isSelected,"onUpdate:modelValue":P=>m.isSelected=P,value:!0,disabled:["VOID","REFUNDED","USED/FLOWN","EXCHANGED"].includes(m.ticketStatus)||m.flightNo==="ARNK",size:"large",onChange:e(_)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),t("div",qh,[m.flightNo==="OPEN"&&m.airline?(s(),g("div",zh,n(m.airline),1)):ee("",!0),t("div",{class:Pe(["text-gray-1 text-sm font-normal leading-snug mr-2.5",["OPEN","ARNK"].includes(m.flightNo)?"text-yellow-1 rounded-sm bg-yellow-2 px-1.5 py-0.5":""])},n(m.flightNo),3),t("div",Gh,n(m.cabin),1)])]),t("div",Hh,[m.depTime?(s(),g(_e,{key:0},[t("div",Yh,n(m.departureDate?e(ot)(m.departureDate).format("YYYY-MM-DD"):""),1),t("div",Kh,n(m.depTime?e(ot)(m.depTime).format("HH:mm"):""),1)],64)):(s(),g("span",Wh,n(m.flightNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",Xh,[t("div",Jh,n(`${m.depAirportCode}-${m.arrAirportCode}`),1),m.changeReason?(s(),g("div",Zh,[t("div",e_,n(m.changeReason),1)])):(s(),g("div",t_)),t("div",n_,[m.pnrNo?(s(),g("div",a_,n(m.pnrNo),1)):ee("",!0),m.pnrNo?(s(),g("div",s_,"/")):ee("",!0),m.crsPnrNo?(s(),g("div",o_,n(m.crsPnrNo),1)):ee("",!0)])]),t("div",i_,[t("div",{class:Pe([e(Bt)[(A=m.ticketStatus)==null?void 0:A.trim()]?e(Bt)[(l=m.ticketStatus)==null?void 0:l.trim()].color:"","text-sm font-bold leading-snug"])},n(m.ticketStatus),3)])])}),128))])],64))),128))])}}}),r_=(a,i)=>{const{t:u}=Ze(),p=Fs(),{orderInfo:f}=Vs(p),d=Nn(),o=I(!1),_=I(),h=I([]),y=I(!0),T=De({}),b=De({}),S=De([]),k=I([]),$=I("voluntary"),m=I("new"),A=I(!1),l=I(!1),P=I(!1),j=I(!1),X=I(!1),ce=I(),M=I(""),w=I(""),W=I(0),C=I(!1),R=je(()=>{var F;return k.value.length===0?!1:m.value==="exist"&&!!((F=D.pnrNo)!=null&&F.trim().length)&&C.value}),N=I(!1),K=je(()=>{var F;return k.value.length===0?!1:m.value==="exist"&&!!((F=D.pnrNo)!=null&&F.trim().length)&&N.value}),oe=()=>k.value.every(F=>!F.crsPnrNo&&F.flightNo!=="ANRK"),te=je(()=>{const F=k.value.length===0,Te=m.value==="exist"&&!D.pnrNo,$e=m.value==="exist"&&D.pnrNo!==w.value&&oe(),Ee=m.value==="exist"&&X.value;return F||Te||$e||Ee}),D=it({pnrNo:""}),E={},pe=it({flight:[],pnrNo:"",issueStatus:"",passengers:[],remarkArea:{remarks:[],ckins:[],clids:[],remarkOsis:[],others:[],ssrContents:[]},originalLineContents:[],historyLineContents:[],basicArea:{},pnrCanceled:!1,international:!1,key:"",rightComponentName:"",government:!1}),ue={pnrNo:[{required:!0,message:u("app.pnrManagement.validate.required")},{pattern:gn,message:u("app.change.inputCorrectPnr"),trigger:["blur"]}]},ge=()=>({ticketNo:a.tktNo,detrType:"GET_ARR_TIME_TICKET",secondFactor:a.factor}),se=async F=>{try{o.value=!0;const{data:Te}=await La(ge(),F);h.value=Te.value??[]}finally{o.value=!1}},de=async()=>{if(S.value=[],h.value.forEach(F=>{F.airSeg.forEach(Te=>{Te.isSelected&&Te.crsPnrNo&&!S.value.includes(Te.crsPnrNo)&&S.value.push(Te.crsPnrNo)})}),S.value.length!=1){T.value.passengers=[];return}if(!(T.value.passengers&&T.value.passengers.length))try{o.value=!0;const F={pnrNo:S.value[0]},Te=we("09200104"),{data:$e}=await Na(F,Te);T.value=($e==null?void 0:$e.value)??{passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},b.value=T.value}catch{T.value={passengers:[],canceled:!1,nonExistent:!1,basicArea:{}}}finally{o.value=!1}},H=F=>{D.pnrNo=M.value,y.value=!0,F==="involuntary"&&(m.value="exist"),P.value=F==="involuntary",j.value=X.value=m.value==="exist"&&oe()},Q=F=>{var Ee;if(C.value=!1,N.value=!1,F!=null&&F.canceled||F!=null&&F.nonExistent){j.value=!0,X.value=!0,N.value=!0;return}const Te=(F==null?void 0:F.passengers)??[];le(Te,h.value)?(j.value=!1,X.value=!1):(j.value=!0,X.value=!0,(Ee=D.pnrNo)!=null&&Ee.trim()&&(C.value=!0))},q=F=>{D.pnrNo=M.value,y.value=!0,F==="exist"&&Q((b==null?void 0:b.value)??{})},V=F=>k.value=F,B=F=>{var Te;return((Te=E[F])==null?void 0:Te.airportCnName)??""},v=async()=>{const F=await Da("searchLocalData");(F?JSON.parse(F.localData):[]).forEach($e=>{E[$e.airportCode]=$e})},G=(F,Te)=>F==="ARNK"?F:F==="VOID"?"ARNK":(F??"").includes(Te)?F.slice(2):F,L=F=>({airCN:"",airCode:F.airline,airService:[],alliance:"",aviationDepartmentGeneral:{},flightNo:G(F.flightNo,F.airline),isShared:"",planeType:"",ocFlightNumber:"",ocAirline:F.operationAirline}),ae=F=>({cabinName:F.cabin,state:""}),ie=(F,Te)=>({tktNum:Te,airlines:L(F),arrDays:"",arrivalAirportCN:B(F.arrAirportCode),arrivalAirportCode:F.arrAirportCode,arrivalDate:F.arrTime?ot(F.arrTime).format("YYYY-MM-DD HH:mm:ss"):"",arrivalTerminal:F.arrAirportTerminal,arrivalTime:ot(F.arrTime).isValid()?ot(F.arrTime).format("HH:mm"):"",asr:"",cabins:[ae(F)],connectLevel:"",departureAirportCN:B(F.depAirportCode),departureAirportCode:F.depAirportCode,departureDate:F.depTime?ot(F.depTime).format("YYYY-MM-DD HH:mm:ss"):"",departureTerminal:F.depAirportTerminal,departureTime:ot(F.depTime).isValid()?ot(F.depTime).format("HH:mm"):"",flightDistance:"",flightTime:"",stopCity:"",marriedSegmentNumber:"",commonMeal:"",ticketStatus:F.ticketStatus,pnrNo:F.pnrNo,segmentType:"0",crsPnrNo:F.crsPnrNo}),Ne=(F,Te)=>Te==="arrive"?{code:F.arrAirportCode,name:B(F.arrAirportCode)}:{code:F.depAirportCode,name:B(F.depAirportCode)},Ae=F=>{const Te=[];return h.value.forEach($e=>{$e.airSeg.forEach(Ee=>{const Oe={arriveCity:Ne(Ee,"arrive"),departureCity:Ne(Ee,"departure"),date:{departureDate:Ee.departureDate},segments:[ie(Ee,$e.etNumber)],tktType:$e.ticketTypeCode??"",openFlag:F?Ee.flightNo==="OPEN":!Ee.crsPnrNo&&Ee.flightNo!=="ANRK",isSelected:!!Ee.isSelected};Oe.key=Ms(Oe),Te.push(Oe)})}),Te},z=F=>{let Te="";return(F??"").includes("NI")?Te=F?`${F}_I`:"":Te=F?`PP_${F}`:"",Te},ke=(F,Te)=>{const $e={docsName:(F==null?void 0:F.docsName)??"",holder:(F==null?void 0:F.holder)??"",documentType:z(F==null?void 0:F.documentType),ssrType:(F==null?void 0:F.ssrType)??"",idCardNumber:(F==null?void 0:F.idCardNumber)??"",visaIssueCountry:(F==null?void 0:F.visaIssueCountry)??"CN",passengerNationality:(F==null?void 0:F.passengerNationality)??"CN",visaExpiryDate:F==null?void 0:F.visaExpiryDate,birthday:(F==null?void 0:F.birthday)??"",gender:(F==null?void 0:F.gender)??"M"};return Te==="PP"?$e:{...$e,docsName:(F==null?void 0:F.docsName)??""}},x=(F,Te)=>`${ot(new Date).format("YYYYMMDDHHmmssSSS")}-${F}-${Te}`,O=(F,Te,$e)=>{var Ee,Oe;return{id:x(Te,$e),birthday:F.birthday??"",chineseName:F.chineseName??"",fullName:F.fullName,nameSuffix:F.nameSuffix??"",lastName:F.lastName??"",firstName:F.firstName??"",segments:[],passengerId:F.passengerId,document:ke(F.document,""),documentPP:ke(F.documentPP,"PP"),niForDocs:((Ee=F.document)==null?void 0:Ee.niForDocs)??"",ppForDocs:((Oe=F.documentPP)==null?void 0:Oe.ppForDocs)??"",docaInfoR:F.docaInfoR??{},docaInfoD:F.docaInfoD??{},inftSuffix:F.inftSuffix??""}},Y=(F,Te,$e)=>{var Ee,Oe;return{id:x(Te,F.passengerType??""),airlineType:$e?"I":"D",foreign:!1,chineseName:F.chineseName??"",birthday:F.birthday??"",passengerType:F.passengerType??"",specialPassengerType:F.specialPassengerType??"",fullName:F.passengerNameInPnr??"",passengerNameInPnr:F.passengerNameInPnr??"",firstName:F.firstName??"",lastName:F.lastName??"",unMinor:!!F.unMinor,unMinorAge:F.unMinor?F.unMinorAge??0:0,nameSuffix:F.nameSuffix??"",vipType:"",vipText:"",osiCtcm:(F==null?void 0:F.osiCtcm)??"",ssrCtcm:(F==null?void 0:F.ssrCtcm)??"",passengerId:F.passengerId,segments:[],document:ke(F.document??{},""),documentPP:ke(F.documentPP??{},"PP"),niForDocs:((Ee=F.document)==null?void 0:Ee.niForDocs)??"",ppForDocs:((Oe=F.documentPP)==null?void 0:Oe.ppForDocs)??"",infantDetail:F.infantDetail?O(F.infantDetail,Te,F.passengerType??""):null,childSsrTypeTextInPnr:[],docaInfoR:F.docaInfoR??{},docaInfoD:F.docaInfoD??{},docsName:F.chineseName?"":F.fullName??""}},fe=(F,Te)=>F.map(($e,Ee)=>Y($e,Ee,Te)),ye=()=>{var F,Te,$e,Ee,Oe,Xe;return{changeModel:m.value,changeType:$.value,pnrNo:D.pnrNo,ticketNumberJoin:(($e=(Te=(F=_.value)==null?void 0:F.choosePassengers)==null?void 0:Te[0])==null?void 0:$e.ticketNumberJoin)??"",ticketNumbersForTN:((Xe=(Oe=(Ee=_.value)==null?void 0:Ee.choosePassengers)==null?void 0:Oe[0])==null?void 0:Xe.ticketNumbersForTN)??[]}},Se=()=>({orderInfo:pe,...ye()}),Fe=()=>{var F,Te,$e,Ee,Oe,Xe,tt,st;return{cts:((Te=(F=T.value)==null?void 0:F.basicArea)==null?void 0:Te.cts)??[],contact:((Ee=($e=T.value)==null?void 0:$e.basicArea)==null?void 0:Ee.contact)??[],contactorEmail:((Xe=(Oe=T.value)==null?void 0:Oe.basicArea)==null?void 0:Xe.contactorEmail)??"",responsibilityGroup:"",issueLimitCrs:"",airlinePnr:"",issueLimitIcs:"",officeName:"",officePhone:"",officeAddress:"",airline:"",contactPhones:((st=(tt=T.value)==null?void 0:tt.basicArea)==null?void 0:st.contactPhones)??[],authorizes:[],firstDepartureTime:"",issueWarn:!1,issueAbnormal:!1,issueLimitOffice:""}},Be=async()=>{var Ee;pe.basicArea=await Fe();const F=await Ae();pe.flight=F,pe.government=h.value.some(Oe=>Oe.governmentPurchase);const Te=await $a(F.filter(Oe=>Oe.isSelected));$a(F.filter(Oe=>Oe.isSelected));const $e=await fe(((Ee=_.value)==null?void 0:Ee.choosePassengers)??[],Te);pe.passengers=$e,pe.changeModelInOrder={...ye()}},Ke=()=>{var F;return(((F=_.value)==null?void 0:F.choosePassengers)??[]).map(Te=>({paxId:Te.passengerId,unMinor:!!Te.unMinor,unMinorAge:Te.unMinor?Te.unMinorAge??0:0,fullName:Te.passengerNameInPnr}))},ve=()=>{if(f.value.size<1)return!1;const F=Array.from(f.value.keys());return F==null?void 0:F.find($e=>{var Ee,Oe;return(Oe=(Ee=f.value.get($e))==null?void 0:Ee.specialContent)==null?void 0:Oe.includes(u("app.basic.occupy"))})},Ye=async()=>{var F;try{if(o.value=!0,m.value==="exist")(F=ce.value)==null||F.validate(async Te=>{var Ee;if(!Te)return;await Be();let $e=!0;T.value.passengers.length>0&&T.value.passengers.length!==(((Ee=_.value)==null?void 0:Ee.choosePassengers)??[]).length&&await Je.confirm(u("app.change.splitPassengerTips"),{icon:Ce("em",{class:"iconfont icon-info-circle-line"}),confirmButtonText:u("app.intlPassengerForm.del.confirm"),cancelButtonText:u("app.intlPassengerForm.del.cancel"),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",showClose:!1}).then(async()=>{var st,Ge;const Oe={travellers:Ke(),count:0,orderId:"",passengerRecordLocator:D.pnrNo},Xe=we("091V0802"),tt=await ta(Oe,Xe);D.pnrNo=((Ge=(st=tt.data.value)==null?void 0:st.splitedOrder)==null?void 0:Ge.passengerRecordLocator)??"",$e=!0}).catch(()=>{$e=!1}),$e&&(await d.push({name:"PnrManagement",query:{pnrNo:D.pnrNo,time:ot().unix(),type:$.value}}),await p.setCurrentRebookInfo(Se()),await i("update:modelValue",!1))});else{await Be(),Reflect.set(pe,"rebookInfos",yt(Se())),await p.setCurrentRebookInfo(Se());const Te=yt(pe),$e=["VOID","REFUNDED","USED/FLOWN","EXCHANGED"];Te.flight=(Ae(!0)??[]).filter(Oe=>{var Xe,tt,st;return((Xe=Oe==null?void 0:Oe.segments)==null?void 0:Xe[0].airlines.flightNo)!=="ARNK"&&!$e.includes(((st=(tt=Oe==null?void 0:Oe.segments)==null?void 0:tt[0])==null?void 0:st.ticketStatus)??"")}),ve()||await p.pushOrderInfo("2",u("app.basic.ungeneted"),Te),await d.push({name:"PnrManagement",query:{time:ot().unix(),type:$.value}}),await i("update:modelValue",!1)}}finally{o.value=!1}},le=(F,Te)=>F.some($e=>_.value.isMatchMainPassenger($e,Te)),pt=()=>{var F;(F=ce.value)==null||F.validate(async Te=>{var $e,Ee,Oe,Xe;if(Te)try{o.value=!0;const tt={pnrNo:D.pnrNo};_.value.selectOnlyMainPassenger();const st=we("09200105"),{data:Ge}=await Na(tt,st);if(Q((Ge==null?void 0:Ge.value)??{}),!(($e=Ge==null?void 0:Ge.value)!=null&&$e.canceled)&&(y.value=le(((Ee=Ge==null?void 0:Ge.value)==null?void 0:Ee.passengers)??[],h.value),!y.value)||(T.value=(Ge==null?void 0:Ge.value)??{passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},(Oe=Ge==null?void 0:Ge.value)!=null&&Oe.canceled||(Xe=Ge==null?void 0:Ge.value)!=null&&Xe.nonExistent))return;w.value=D.pnrNo}catch{T.value={passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},y.value=!1}finally{o.value=!1}})};Ct(()=>k.value,()=>{if(W.value===k.value.length)return;w.value="";const F=k.value.every($e=>$e.changeReason.trim()==="IRR");F||($.value="voluntary"),A.value=!F,P.value=$.value==="involuntary",j.value=oe(),X.value=oe()&&m.value==="exist";const Te=S.value.length<=1;l.value=!Te,Te||(A.value=!0,P.value=!1,$.value="voluntary",m.value="new"),M.value=S.value[0],D.pnrNo=S.value[0],W.value=k.value.length},{deep:!0});const at=(F,Te)=>{var Ee;const $e=(Ee=F.passenger)==null?void 0:Ee.segments;h.value.forEach(Oe=>{Oe.etNumber===Te&&$e.length===Oe.airSeg.length&&Oe.airSeg.forEach((Xe,tt)=>{var st,Ge,We,It,Ut;Xe.flightNo==="OPEN"&&$e[tt].departureDateTime.replace(/\s/g,"")&&(Xe.departureDate=((st=$e==null?void 0:$e[tt])==null?void 0:st.departureDateTime)??"",Xe.depTime=((Ge=$e==null?void 0:$e[tt])==null?void 0:Ge.departureDateTime)??"",Xe.arrTime=((We=$e==null?void 0:$e[tt])==null?void 0:We.arrivalDateTime)??"",Xe.flightNo=((It=$e==null?void 0:$e[tt])==null?void 0:It.flightNo)??"",Xe.cabin=((Ut=$e==null?void 0:$e[tt])==null?void 0:Ut.cabin)??"")})})},xe=async F=>{try{const Te=we("091V0801");o.value=!0,F.forEach(async $e=>{var Oe;const Ee=((Oe=(await ja($e,Te)).data.value)==null?void 0:Oe.data)??{};at(Ee,$e)}),await cn()}finally{o.value=!1}};return mt(async()=>{const F=we("091T0102");await se(F),await v()}),{loading:o,flightList:h,changeType:$,changeModel:m,disableInvoluntarily:A,disableExist:l,disableNew:P,changeInfoForm:D,FORM_RULES:ue,changeModelFormRef:ce,isAllowChangePnr:j,pnrNoList:S,passengerResult:T,checkSegmentList:k,passengerRef:_,orderInfoTemplate:pe,isShowAccompanyPassenger:X,isDisableConfirmButton:te,searchAccompanyPassenger:pt,buildFlightData:Ae,getChosenSegment:V,queryPassenger:de,handleChangeType:H,handleChangeModel:q,confirmChange:Ye,queryRtkt:xe,isShowTicketNotInPNRTip:R,isShowCanceledOrNoPNRTip:K}},c_=a=>{const i=I([]),u=De([]),p=b=>{const S=b.replace(/\s+/g,""),k=S.indexOf("("),$=S.indexOf("*");if(k>-1&&$>-1){const m=Math.min(k,$);return(S==null?void 0:S.substring(0,m))??""}else{if(k===-1&&$>-1)return(S==null?void 0:S.substring(0,$))??"";if(k>-1&&$===-1)return(S==null?void 0:S.substring(0,k))??""}return S},f=(b,S)=>{var l;const k=(S??[]).map(P=>{var j;return(j=P.etNumber)==null?void 0:j.replace(/-/g,"")}),m=b.ticketNumbersForTN.map(P=>P==null?void 0:P.replace(/-/g,"")).some(P=>k.includes(P)),A=p(b.passengerNameInPnr)===p(((l=S[0])==null?void 0:l.passengerNameSuffix)??"");return m||A},d=()=>{u.value=[],i.value=[],a.passengerList.forEach(b=>{f(b,a.flightList)&&u.value.unshift(b)})},o=()=>{u.value=[],a.passengerList.forEach(b=>{f(b,a.flightList)&&u.value.unshift(b)}),i.value.forEach(b=>{f(a.passengerList[b],a.flightList)||u.value.push(a.passengerList[b])})},_=()=>{var b,S;if((a.flightList??[]).length>1){const k=a.flightList.length-1,$=a.flightList[k].etNumber.slice(-2);return`${a.flightList[0].etNumber}-${$}`}else return((S=(b=a.flightList)==null?void 0:b[0])==null?void 0:S.etNumber)??""},h=b=>{const S=b.indexOf("(");return S!==-1?b.substring(0,S).trim():b},y=()=>({birthday:"",chineseName:"",firstName:"",lastName:"",nameSuffix:a.flightList[0].nameSuffix,unMinor:!1,unMinorAge:0,passengerId:1,passengerPhone:"",fullName:a.flightList[0].fullName,passengerNameInPnr:a.flightList[0].passengerNameSuffix,passengerType:a.flightList[0].passengerType,specialPassengerType:a.flightList[0].specialPassengerType,ticketNumberJoin:_(),ticketNumbersForTN:a.flightList.map(S=>S.etNumber),segments:[]}),T=()=>{if(u.value=[],a.passengerList.length===0){u.value.push(y());return}a.passengerList.forEach((b,S)=>{f(b,a.flightList)&&b.infantDetail&&!i.value.includes(S)&&i.value.push(S),f(b,a.flightList)&&u.value.unshift(b)})};return mt(()=>{T()}),Ct(()=>a.passengerList,()=>{T()}),{choosePassengerIndex:i,choosePassengers:u,selectPassenger:o,selectOnlyMainPassenger:d,getInftName:h,isMatchMainPassenger:f}},u_={key:0,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2"},d_={class:"cancel-tip ml-[10px]"},p_=t("em",{class:"iconfont icon-warning-circle-fill"},null,-1),f_={key:1,class:"texv-if=t-base text-gray-1 font-bold mt-[15px]"},m_={class:"flex"},g_={class:"bg-gray-7 ml-[5px]"},k_={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},y_={key:0,class:"iconfont icon-connect ml-[5px] mr-[5px]"},v_={key:1,class:"bg-gray-7 w-[34px] ml-[5px]"},h_={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},__={class:"flex"},b_={class:"bg-gray-7 w-[34px] ml-[5px]"},x_={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},T_=Le({__name:"Passenger",props:{mainTicketNo:{},flightList:{},passengerList:{},pnrNoList:{}},setup(a,{expose:i}){const u=a,{choosePassengerIndex:p,choosePassengers:f,selectPassenger:d,selectOnlyMainPassenger:o,isMatchMainPassenger:_}=c_(u);return i({choosePassengers:f,isMatchMainPassenger:_,selectOnlyMainPassenger:o}),(h,y)=>{const T=_t,b=tn,S=sa;return s(),g(_e,null,[u.pnrNoList.length>1?(s(),g("div",u_,[t("span",d_,[p_,J(n(h.$t("app.agentTicketQuery.differentTip")),1)])])):ee("",!0),u.passengerList.length>1||u.passengerList.length&&u.passengerList[0].infantDetail?(s(),g("div",f_,n(h.$t("app.agentTicketQuery.peersPassenger")),1)):ee("",!0),r(S,{modelValue:e(p),"onUpdate:modelValue":y[0]||(y[0]=k=>Me(p)?p.value=k:null),class:"flex flex-wrap"},{default:c(()=>[(s(!0),g(_e,null,Re(u.passengerList,(k,$)=>(s(),g("div",{key:k.passengerNameInPnr},[t("label",null,[e(_)(k,h.flightList)?ee("",!0):(s(),g("div",{key:0,class:Pe([{"w-[390px]":k.infantDetail&&!e(_)(k,h.flightList),"bg-brand-4 border-brand-2":e(p).includes($),"border-inherit":!e(p).includes($)},"pr-[4px] mr-[10px] mt-[10px] rounded border"])},[r(b,{label:$,class:"ml-[8px]",onChange:e(d)},{default:c(()=>[t("div",m_,[r(T,{placement:"top",trigger:"hover",content:(k==null?void 0:k.passengerNameInPnr)??"",teleported:!1},{default:c(()=>[t("div",{class:Pe([{"w-[107px]":k.passengerNameInPnr.length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(k.passengerNameInPnr),3)]),_:2},1032,["content"]),t("div",g_,[t("div",k_,n(e($t)(k.specialPassengerType??"ADT")),1)]),k.infantDetail?(s(),g("div",y_)):ee("",!0),r(T,{placement:"top",trigger:"hover",content:(k==null?void 0:k.passengerNameInPnr)??"",teleported:!1},{default:c(()=>{var m;return[t("div",{class:Pe([{"w-[120px]":(k==null?void 0:k.infantDetail)&&k.infantDetail.passengerNameInPnr.length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(((m=k.infantDetail)==null?void 0:m.passengerNameInPnr)??""),3)]}),_:2},1032,["content"]),k.infantDetail?(s(),g("div",v_,[t("div",h_,n(h.$t("app.agentTicketQuery.passengerEN.type_INF")),1)])):ee("",!0)])]),_:2},1032,["label","onChange"])],2))]),e(_)(k,h.flightList)&&k.infantDetail?(s(),g("div",{key:0,class:Pe([{"w-[390px]":k.infantDetail&&!e(_)(k,h.flightList),"bg-brand-4 border-brand-2":e(p).includes($),"border-inherit":!e(p).includes($)},"main-baby w-[190px] mr-[10px] mt-[10px] rounded border"])},[r(b,{modelValue:e(p)[$],"onUpdate:modelValue":m=>e(p)[$]=m,label:$,class:"ml-[8px]",disabled:"",onChange:e(d)},{default:c(()=>{var m;return[r(T,{placement:"top",trigger:"hover",content:((m=k.infantDetail)==null?void 0:m.passengerNameInPnr)??"",teleported:!1},{default:c(()=>{var A;return[t("div",__,[t("div",{class:Pe([{"w-[102px]":(k==null?void 0:k.infantDetail)&&(k.infantDetail.passengerNameInPnr??"").length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(((A=k.infantDetail)==null?void 0:A.passengerNameInPnr)??""),3),t("div",b_,[t("div",x_,n(h.$t("app.agentTicketQuery.passengerEN.type_INF")),1)])])]}),_:2},1032,["content"])]}),_:2},1032,["modelValue","onUpdate:modelValue","label","onChange"])],2)):ee("",!0)]))),128))]),_:1},8,["modelValue"])],64)}}});const pa=a=>(wt("data-v-bb2e6f56"),a=a(),St(),a),N_=pa(()=>t("i",{class:"iconfont icon-close"},null,-1)),$_=[N_],R_={class:"mt-[15px]"},C_={key:1,class:"mt-[10px]"},w_={class:"flex"},S_={key:2,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2 mt-[7px] mb-[20px]"},P_={class:"cancel-tip ml-[10px]"},A_=pa(()=>t("em",{class:"iconfont icon-warning-circle-fill"},null,-1)),D_={key:3,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2 mt-[7px] mb-[20px]"},E_={class:"cancel-tip ml-[10px]"},O_=pa(()=>t("em",{class:"iconfont icon-warning-circle-fill"},null,-1)),F_={class:"flex justify-center crs-btn-dialog-ui"},V_={key:1,class:"h-[300px] w-full flex-col justify-center items-center gap-2.5 inline-flex"},M_={class:"flex-col justify-center items-center gap-[19px] flex"},L_=["alt"],j_={class:"flex-col justify-center items-center gap-2.5 flex"},B_={class:"text-center text-gray-2 text-lg font-bold leading-normal"},I_=Le({__name:"ChangeDialog",props:{tktNo:{},factor:{}},emits:["update:modelValue"],setup(a,{emit:i}){const u=a,p=i,{loading:f,flightList:d,changeType:o,changeModel:_,disableNew:h,disableExist:y,disableInvoluntarily:T,changeInfoForm:b,FORM_RULES:S,changeModelFormRef:k,isAllowChangePnr:$,passengerResult:m,pnrNoList:A,checkSegmentList:l,passengerRef:P,isShowAccompanyPassenger:j,isDisableConfirmButton:X,searchAccompanyPassenger:ce,getChosenSegment:M,queryPassenger:w,handleChangeType:W,handleChangeModel:C,confirmChange:R,queryRtkt:N,isShowTicketNotInPNRTip:K,isShowCanceledOrNoPNRTip:oe}=r_(u,p);return(te,D)=>{const E=Dt,pe=Et,ue=vt,ge=ct,se=et,de=ut,H=rt,Q=ht;return s(),re(H,{title:te.$t("app.change.select"),width:"1040px","close-on-click-modal":!1,"show-close":!1,"align-center":"true",tabindex:"-1","custom-class":"change-dialog",onClose:D[5]||(D[5]=q=>te.$emit("update:modelValue",!1))},{default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:D[0]||(D[0]=q=>te.$emit("update:modelValue",!1))},$_),Ue((s(),g("div",null,[e(d).length>0?(s(),g(_e,{key:0},[e(d).length>0?(s(),re(l_,{key:0,"flight-list":e(d),onQueryPassenger:e(w),onGetChosenSegment:e(M),onQueryRtkt:e(N)},null,8,["flight-list","onQueryPassenger","onGetChosenSegment","onQueryRtkt"])):ee("",!0),t("div",R_,[e(m).passengers?(s(),re(T_,{key:0,ref_key:"passengerRef",ref:P,"pnr-no-list":e(A),"flight-list":e(d),"passenger-list":e(m).passengers,"main-ticket-no":e(d)[0].etNumber},null,8,["pnr-no-list","flight-list","passenger-list","main-ticket-no"])):ee("",!0)]),e(l).length>0?(s(),g("div",C_,[t("div",null,[r(pe,{modelValue:e(o),"onUpdate:modelValue":D[1]||(D[1]=q=>Me(o)?o.value=q:null),onChange:e(W)},{default:c(()=>[r(E,{label:"voluntary"},{default:c(()=>[J(n(te.$t("app.change.voluntarilyChange")),1)]),_:1}),r(E,{disabled:e(T),label:"involuntary"},{default:c(()=>[J(n(te.$t("app.change.involuntarilyChange")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","onChange"])]),t("div",w_,[r(pe,{modelValue:e(_),"onUpdate:modelValue":D[2]||(D[2]=q=>Me(_)?_.value=q:null),onChange:e(C)},{default:c(()=>[r(E,{disabled:e(h),label:"new"},{default:c(()=>[J(n(te.$t("app.change.newChange")),1)]),_:1},8,["disabled"]),r(E,{disabled:e(y),label:"exist"},{default:c(()=>[J(n(te.$t("app.change.existChange")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","onChange"]),e(_)==="exist"?(s(),re(de,{key:0,ref_key:"changeModelFormRef",ref:k,rules:e(S),"hide-required-asterisk":!0,model:e(b),inline:!0,class:"form-style inline-block h-[32px] w-[200px]"},{default:c(()=>[r(ge,{prop:"pnrNo",class:"w-[66px]"},{default:c(()=>[r(ue,{modelValue:e(b).pnrNo,"onUpdate:modelValue":D[3]||(D[3]=q=>e(b).pnrNo=q),disabled:!e($),onInput:D[4]||(D[4]=q=>{var V,B;e(b).pnrNo=(V=e(b).pnrNo)==null?void 0:V.trim(),e(b).pnrNo=(B=e(b).pnrNo)==null?void 0:B.toLocaleUpperCase()})},null,8,["modelValue","disabled"])]),_:1}),e(j)?(s(),re(ge,{key:0},{default:c(()=>[r(se,{onClick:e(ce)},{default:c(()=>[J(n(te.$t("app.change.search")),1)]),_:1},8,["onClick"])]),_:1})):ee("",!0)]),_:1},8,["rules","model"])):ee("",!0)])])):ee("",!0),e(K)&&!e(oe)?(s(),g("div",S_,[t("span",P_,[A_,J(" "+n(te.$t("app.agentTicketQuery.ticketNotInPNR")),1)])])):ee("",!0),e(oe)?(s(),g("div",D_,[t("span",E_,[O_,J(" "+n(te.$t("app.agentTicketQuery.changePnrTip")),1)])])):ee("",!0),t("div",F_,[r(se,{type:"primary",disabled:e(X),onClick:e(R)},{default:c(()=>[J(n(te.$t("app.button.ensure")),1)]),_:1},8,["disabled","onClick"])])],64)):(s(),g("div",V_,[t("div",M_,[t("img",{src:ra,alt:te.$t("app.emptyTip")},null,8,L_),t("div",j_,[t("div",B_,n(te.$t("app.change.notRtData")),1)])])]))])),[[Q,e(f)]])]),_:1},8,["title"])}}});const U_=dt(I_,[["__scopeId","data-v-bb2e6f56"]]),Q_=a=>(wt("data-v-ff302fc6"),a=a(),St(),a),q_={class:"ticket-card mt-[10px] py-2.5 px-5 min-h-58 bg-gray-0 rounded-lg"},z_={class:"flex items-center justify-between relative"},G_={class:"flex items-center justify-start"},H_={key:0,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] mr-2.5"},Y_={key:1,class:"iconfont icon-inf ml-2.5 mr-[4px] text-gray-4"},K_={key:2,class:"iconfont icon-user-fill ml-2.5 mr-[4px] text-gray-4"},W_={class:"mr-[4px] text-base font-bold text-gray-1"},X_={key:4,class:"mr-[4px] text-base font-bold text-gray-1"},J_={key:5,class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs"},Z_={key:6,class:"text-yellow-1 mr-2.5 cursor-pointer text-xs"},e1={class:"text-xs text-right"},t1={key:2,class:"ml-[12px]"},n1={class:"mt-[10px] text-sm"},a1={class:"w-[22%]"},s1={key:0,class:"mr-2.5"},o1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},i1={key:2,class:"text-gray-1 mr-2.5"},l1={class:"text-gray-1"},r1={class:"w-[22%]"},c1={class:"text-gray-1 mr-2.5"},u1={class:"text-gray-3"},d1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},p1={class:"w-[28%] text-gray-1"},f1={class:"mr-2.5"},m1={key:0,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 text-xs"},g1={key:1,class:"text-gray-1"},k1={key:2,class:"text-gray-1 mx-[2px]"},y1=["onClick"],v1={class:"w-[28%] flex items-center"},h1={key:0},_1={class:"ticket-card mt-[10px] py-2.5 px-5 min-h-58 bg-gray-0 rounded-lg"},b1={class:"flex items-center justify-between relative"},x1={class:"flex items-center justify-start"},T1={class:"w-[170px] mr-2.5 font-bold text-brand-2"},N1={key:0,class:"iconfont icon-inf ml-2.5 mr-[4px] text-gray-4"},$1={key:1,class:"iconfont icon-user-fill ml-2.5 mr-[4px] text-gray-4"},R1={class:"mr-[4px] text-base font-bold text-gray-1"},C1={key:3,class:"mr-[4px] text-base font-bold text-gray-1"},w1={key:4,class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs"},S1={class:"mt-[10px] text-sm"},P1={class:"w-[6%]"},A1={key:0,class:"mr-2.5"},D1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},E1={key:2,class:"text-gray-1 mr-2.5"},O1={class:"w-[11%]"},F1={key:0,class:"text-gray-1 mr-2.5"},V1={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},M1={class:"w-[15%] text-gray-1"},L1={class:"w-[24%] flex items-center"},j1={class:"h-9 justify-start items-center gap-4 mb-2.5 inline-flex"},B1=Q_(()=>t("em",{class:"iconfont icon-info-circle-line text-brand-2 !text-[36px] w-9 h-9 flex items-center"},null,-1)),I1={class:"grow shrink basis-0 text-gray-1 text-lg font-normal leading-normal"},U1=Le({__name:"TicketQueryResult",props:{queryTicketRes:{},refundTicketData:{},queryType:{}},emits:["addNewTab","reQueryTicket"],setup(a,{emit:i}){const u=i,p=a,{showSupplementRefundDialog:f,authTicketShow:d,showSupplementSuccessDialog:o,ticketList:_,ticketListByName:h,isShowRepealTicketDialog:y,ticketOperationCondition:T,printerNo:b,isSupplementRefund:S,refundOperationCondition:k,refundTicketData:$,printNoDialog:m,tktNumber:A,invoiceNumber:l,showTicketRefundFormDialog:P,changeDialogShow:j,changeTicketNo:X,printerType:ce,ticketOperation:M,goToPnrManage:w,stssChangeTicketStatusClick:W,changeStatus:C,viewRefundFormWithGid:R,viewSupplementRefundWithGid:N,authTicketClick:K,openSupplementSuccessDialog:oe,openRefundDialog:te,openDialog:D,goToNewRefund:E,reQueryTicket:pe,showPreview:ue,previewInfo:ge,openPreview:se,openChange:de,pullControlPower:H,copyInfo:Q,changeFactor:q,tssForm:V,tssFormRef:B,datePrefix:v,showTssDialog:G,closeDialog:L,TSS_FORM_RULES:ae,confirmTss:ie,tssTip:Ne,tssCheckLabel:Ae,refundPrintNo:z,invalidatedTicketQueryGid:ke,closePopover:x}=Zy(p,u);return(O,Y)=>{const fe=_t,ye=et,Se=tn,Fe=ct,Be=zo,Ke=ut,ve=rt,Ye=$n("permission");return s(),g(_e,null,[O.queryType!=="4"&&e(_).length>0?(s(!0),g(_e,{key:0},Re(e(_),(le,pt)=>{var at;return s(),g("div",{key:le.etNumber+pt},[t("div",q_,[t("div",z_,[t("div",G_,[r(un,{"tkt-info":le,"ticket-number":le==null?void 0:le.etNumber,"second-factor":le==null?void 0:le.secondFactor,"tkt-index":pt,onClosePopover:e(x)},null,8,["tkt-info","ticket-number","second-factor","tkt-index","onClosePopover"]),le.governmentPurchase?(s(),g("div",H_,"GP")):ee("",!0),le.specialPassengerType==="INF"?(s(),g("em",Y_)):(s(),g("em",K_)),le.passengerNameSuffix&&((at=le.passengerNameSuffix)==null?void 0:at.length)>30?(s(),re(fe,{key:3,class:"item",effect:"dark",content:le.passengerNameSuffix,placement:"top-start"},{default:c(()=>[t("span",W_,n(le.passengerNameSuffix),1)]),_:2},1032,["content"])):(s(),g("span",X_,n(le.passengerNameSuffix),1)),le.passengerType?(s(),g("span",J_,n(e($t)(le.specialPassengerType)),1)):ee("",!0),le.receiptPrinted?(s(),g("span",Z_,"ReceiptPrinted")):ee("",!0)]),t("div",e1,[le!=null&&le.canQueryAndApplyRefund?(s(),re(ye,{key:0,onClick:xe=>e(R)(!1,le)},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.queryRefundBtn")),1)]),_:2},1032,["onClick"])):ee("",!0),r(fe,{class:"box-item",effect:"dark",content:O.$t("app.agentTicketQuery.supplementaryRefundApplyTip"),placement:"top-start","popper-class":"ticket-conditon-popper"},{content:c(()=>[t("div",null,n(O.$t("app.agentTicketQuery.supplementaryRefundApplyTip")),1)]),default:c(()=>[le!=null&&le.canSupplementaryRefundApply?(s(),g("span",{key:0,class:Pe({"button-spacing":le==null?void 0:le.canSupplementaryRefundApply})},[Ue((s(),re(ye,{onClick:xe=>e(N)(!0,le)},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.supplementaryRefundApplyBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-supplement-button"]])],2)):ee("",!0)]),_:2},1032,["content"]),le!=null&&le.canChange?(s(),g("span",{key:1,class:Pe({"button-spacing":le==null?void 0:le.canChange})},[Ue((s(),re(ye,{"data-gid":"091T0102",onClick:xe=>e(de)(le.etNumber,le.specialPassengerType,le.secondFactor)},{default:c(()=>[J(n(O.$t("app.agentTicketRefund.change")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-ticketChange-button"]])],2)):ee("",!0),le!=null&&le.canRefund?(s(),g("span",t1,[r(ye,{onClick:xe=>e(se)(le)},{default:c(()=>[J(n(O.$t("app.agentTicketRefund.preview")),1)]),_:2},1032,["onClick"])])):ee("",!0),le!=null&&le.canRefund?(s(),g("span",{key:3,class:Pe({"button-spacing":le==null?void 0:le.canRefund})},[Ue((s(),re(ye,{"data-gid":"091Q0101",onClick:xe=>e(E)(le.etNumber,le.secondFactor)},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.refundBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-refundTicket-button"]])],2)):ee("",!0),le!=null&&le.canVoid?(s(),g("span",{key:4,class:Pe({"button-spacing":le==null?void 0:le.canVoid})},[Ue((s(),re(ye,{onClick:xe=>e(M)(le,"repealTicket")},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.invalidBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-repealTicket-button"]])],2)):ee("",!0),le!=null&&le.canSuspended?(s(),g("span",{key:5,class:Pe({"button-spacing":le==null?void 0:le.canSuspended})},[Ue((s(),re(ye,{onClick:xe=>e(W)(le,"Suspend")},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.suspendBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-suspendOrUnSuspend-button"]])],2)):ee("",!0),le!=null&&le.canUnSuspended?(s(),g("span",{key:6,class:Pe({"button-spacing":le==null?void 0:le.canUnSuspended})},[Ue((s(),re(ye,{onClick:xe=>e(W)(le,"Resume")},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.unsuspendBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-suspendOrUnSuspend-button"]])],2)):ee("",!0),le!=null&&le.canTktAuth?(s(),g("span",{key:7,class:Pe({"button-spacing":le==null?void 0:le.canTktAuth})},[Ue((s(),re(ye,{"data-gid":"091N0203",onClick:xe=>e(K)(le.etNumber)},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.ticketAuthBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-ticketAuth-button"]])],2)):ee("",!0)])]),t("div",n1,[(s(!0),g(_e,null,Re(le.airSeg??[],(xe,F)=>{var Te,$e,Ee,Oe;return s(),g("div",{key:`${le.etNumber}${xe.depAirportCode}`,class:"flex justify-between seg-info"},[t("div",a1,[xe.flightNo==="OPEN"&&xe.airline?(s(),g("span",s1,n(xe.airline),1)):ee("",!0),["OPEN","ARNK"].includes(xe.flightNo)||(Te=xe.flightNo)!=null&&Te.includes("VOID")?(s(),g("span",o1,n(xe.flightNo==="OPEN"?"OPEN":"ARNK"),1)):(s(),g("span",i1,n(e(ha)((xe==null?void 0:xe.airline)??"",(xe==null?void 0:xe.operationAirline)??"")?`*${xe.flightNo}`:xe.flightNo),1)),t("span",l1,n(xe.cabin),1)]),t("div",r1,[xe.depTime?(s(),g(_e,{key:0},[t("span",c1,n(e(ot)(xe.depTime).format("YYYY-MM-DD")??""),1),t("span",u1,n(e(ot)(xe.depTime).format("HH:mm")??""),1)],64)):(s(),g("span",d1,n(xe.flightNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",p1,[t("span",f1,n(xe.depAirportCode)+"-"+n(xe.arrAirportCode),1),xe.changeReason?(s(),g("span",m1,n(xe.changeReason),1)):ee("",!0),xe.pnrNo?(s(),g("span",g1,n(xe.pnrNo),1)):ee("",!0),xe.pnrNo?(s(),g("span",k1,"/")):ee("",!0),xe.crsPnrNo?(s(),re(ye,{key:3,link:"",type:"primary",onClick:Xe=>e(w)(xe.crsPnrNo)},{default:c(()=>[J(n(xe.crsPnrNo),1)]),_:2},1032,["onClick"])):ee("",!0),xe.crsPnrNo?(s(),g("em",{key:4,class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:Xe=>e(Q)(xe.crsPnrNo)},null,8,y1)):ee("",!0)]),t("div",v1,[t("span",{class:Pe([e(Bt)[($e=xe.ticketStatus)==null?void 0:$e.trim()]?e(Bt)[(Ee=xe.ticketStatus)==null?void 0:Ee.trim()].color:"","font-bold mr-2.5"])},n(xe.ticketStatus),3),["REFUNDED","OPEN FOR USE","AIRPORT CNTL"].includes((Oe=xe.ticketStatus)==null?void 0:Oe.trim())?(s(),re(fe,{key:0,class:"box-item",effect:"dark",content:O.$t("app.agentTicketQuery.changeStatusTips"),placement:"bottom-start"},{default:c(()=>[xe.airline!=="CZ"?(s(),g("span",h1,[Ue((s(),re(ye,{link:"",type:"primary",size:"small",onClick:Xe=>e(C)(le.etNumber,xe)},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.changeStatusBtn")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-updateStatus-button"]])])):ee("",!0)]),_:2},1032,["content"])):ee("",!0),xe.ticketStatus.includes("AIRPORT CNTL")?Ue((s(),re(ye,{key:1,link:"",type:"primary",size:"small",onClick:Xe=>e(H)(F,xe.airline,le.etNumber)},{default:c(()=>[J(n(O.$t("app.agentTicketQuery.pullControlPower")),1)]),_:2},1032,["onClick"])),[[Ye,"crs-tc-ticketOperation-ticketQuery-pullControlPower-button"]]):ee("",!0)])])}),128))])])])}),128)):ee("",!0),O.queryType==="4"&&e(h).length>0?(s(!0),g(_e,{key:1},Re(e(h),(le,pt)=>{var at;return s(),g("div",{key:le.etNumber+pt},[t("div",_1,[t("div",b1,[t("div",x1,[t("span",T1,n(e(Gt)(le.etNumber)),1),le.passengerType&&le.passengerType==="INF"?(s(),g("em",N1)):ee("",!0),le.passengerType&&le.passengerType!=="INF"?(s(),g("em",$1)):ee("",!0),le.passengerName&&((at=le.passengerName)==null?void 0:at.length)>30?(s(),re(fe,{key:2,class:"item",effect:"dark",content:le.passengerName,placement:"top-start"},{default:c(()=>[t("span",R1,n(le.passengerName),1)]),_:2},1032,["content"])):(s(),g("span",C1,n(le.passengerName),1)),le.passengerType?(s(),g("span",w1,n(e($t)(le.passengerType)),1)):ee("",!0)])]),t("div",S1,[(s(!0),g(_e,null,Re(le.airSeg??[],xe=>{var F,Te,$e;return s(),g("div",{key:`${le.etNumber}${xe.depAirportCode}`,class:"flex justify-between seg-info"},[t("div",P1,[xe.fltNo==="OPEN"&&xe.airlineCode?(s(),g("span",A1,n(xe.airlineCode),1)):ee("",!0),["OPEN","ARNK"].includes(xe.fltNo)||(F=xe.fltNo)!=null&&F.includes("VOID")?(s(),g("span",D1,n(xe.fltNo==="OPEN"?"OPEN":"ARNK"),1)):(s(),g("span",E1,n(e(ha)((xe==null?void 0:xe.airlineCode)??"",(xe==null?void 0:xe.operateAirline)??"")?`*${xe.airlineCode}${xe.fltNo}`:`${xe.airlineCode}${xe.fltNo}`),1))]),t("div",O1,[xe.depDate?(s(),g("span",F1,n(e(ot)(xe.depDate).format("YYYY-MM-DD")??""),1)):(s(),g("span",V1,n(xe.fltNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",M1,[t("span",null,n(xe.depAirportCode)+"-"+n(xe.arrAirportCode),1)]),t("div",L1,[t("span",{class:Pe([e(Bt)[(Te=xe.status)==null?void 0:Te.trim()]?e(Bt)[($e=xe.status)==null?void 0:$e.trim()].color:"","font-bold mr-2.5"])},n(xe.status),3)])])}),128))])])])}),128)):ee("",!0),e(y)?(s(),re(Np,{key:2,modelValue:e(y),"onUpdate:modelValue":Y[0]||(Y[0]=le=>Me(y)?y.value=le:null),"invalidated-ticket-query-gid":e(ke),"ticket-operation-condition":e(T),onReQueryTicket:e(pe)},null,8,["modelValue","invalidated-ticket-query-gid","ticket-operation-condition","onReQueryTicket"])):ee("",!0),e(d)?(s(),re(uf,{key:3,modelValue:e(d),"onUpdate:modelValue":Y[1]||(Y[1]=le=>Me(d)?d.value=le:null),"ticket-no":e(A)},null,8,["modelValue","ticket-no"])):ee("",!0),e(m)?(s(),re(Za,{key:4,modelValue:e(m),"onUpdate:modelValue":Y[2]||(Y[2]=le=>Me(m)?m.value=le:null),"ticket-management-organization-code":e(k).ticketManagementOrganizationCode??"",onOpenDialog:e(D)},null,8,["modelValue","ticket-management-organization-code","onOpenDialog"])):ee("",!0),e(P)?(s(),re(aa,{key:5,modelValue:e(P),"onUpdate:modelValue":Y[3]||(Y[3]=le=>Me(P)?P.value=le:null),"printer-no":e(b),"printer-type":e(ce),"is-supplement-refund":e(S),"refund-operation-condition":e(k),"refund-ticket-data":e($),onReQueryTicket:e(pe)},null,8,["modelValue","printer-no","printer-type","is-supplement-refund","refund-operation-condition","refund-ticket-data","onReQueryTicket"])):ee("",!0),e(f)?(s(),re(xh,{key:6,modelValue:e(f),"onUpdate:modelValue":Y[4]||(Y[4]=le=>Me(f)?f.value=le:null),"printer-no":e(b),"refund-ticket-data":e($),onOpenSupplementSuccessDialog:e(oe)},null,8,["modelValue","printer-no","refund-ticket-data","onOpenSupplementSuccessDialog"])):ee("",!0),e(o)?(s(),re(yh,{key:7,modelValue:e(o),"onUpdate:modelValue":Y[5]||(Y[5]=le=>Me(o)?o.value=le:null),"ticket-management-organization-code":e(k).ticketManagementOrganizationCode??"","invoice-number":e(l),"refund-printer-no":e(z),onOpenRefundDialog:e(te),onToQueryTicket:e(pe)},null,8,["modelValue","ticket-management-organization-code","invoice-number","refund-printer-no","onOpenRefundDialog","onToQueryTicket"])):ee("",!0),e(ue)?(s(),re(rh,{key:8,modelValue:e(ue),"onUpdate:modelValue":Y[6]||(Y[6]=le=>Me(ue)?ue.value=le:null),"preview-info":e(ge)},null,8,["modelValue","preview-info"])):ee("",!0),e(j)?(s(),re(U_,{key:9,modelValue:e(j),"onUpdate:modelValue":Y[7]||(Y[7]=le=>Me(j)?j.value=le:null),"tkt-no":e(X),factor:e(q)},null,8,["modelValue","tkt-no","factor"])):ee("",!0),r(ve,{modelValue:e(G),"onUpdate:modelValue":Y[10]||(Y[10]=le=>Me(G)?G.value=le:null),width:"500","close-on-click-modal":!1,"show-close":!1,class:"ticket-pnr-dialog",onClose:e(L)},{footer:c(()=>[r(ye,{type:"primary",onClick:e(ie)},{default:c(()=>[J(n(O.$t("app.agentTicketRefund.sure")),1)]),_:1},8,["onClick"]),r(ye,{onClick:e(L)},{default:c(()=>[J(n(O.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])]),default:c(()=>[t("div",j1,[B1,t("div",I1,n(e(Ne)),1)]),O.queryType==="2"?(s(),re(Ke,{key:0,ref_key:"tssFormRef",ref:B,model:e(V),rules:e(ae),"require-asterisk-position":"right",inline:!0,class:"form"},{default:c(()=>[r(Fe,{prop:"pnrTss",class:"ml-[52px]"},{default:c(()=>[r(Se,{modelValue:e(V).pnrTss,"onUpdate:modelValue":Y[8]||(Y[8]=le=>e(V).pnrTss=le),label:e(Ae)},null,8,["modelValue","label"])]),_:1}),e(V).pnrTss?(s(),re(Fe,{key:0,label:O.$t("app.ticketStatus.issueDate"),prop:"issueDate"},{default:c(()=>[r(Be,{modelValue:e(V).issueDate,"onUpdate:modelValue":Y[9]||(Y[9]=le=>e(V).issueDate=le),class:"date-picker","prefix-icon":e(v),type:"date",format:"YYYY-MM-DD",placeholder:O.$t("app.ticketStatus.issueDate")},null,8,["modelValue","prefix-icon","placeholder"])]),_:1},8,["label"])):ee("",!0)]),_:1},8,["model","rules"])):ee("",!0)]),_:1},8,["modelValue","onClose"])],64)}}});const Q1=dt(U1,[["__scopeId","data-v-ff302fc6"]]),q1=(a,i)=>{const{t:u}=Ze(),{copy:p,isSupported:f}=Aa({legacy:!0});return{ticketList:je(()=>yt(a.batchRefundRes.refundResult)??[]),goTicketQuery:h=>{i("addNewTab",{title:u("app.agentTicketQuery.ticketList"),name:"ticketQuery",content:null},h)},doCopy:h=>{f&&(p(h),lt({message:u("app.batchRefund.copySuccess"),type:"success",duration:2*1e3,grouping:!0,repeatNum:1}))}}},z1={class:"batch-refund-result mt-[10px] py-2.5 px-2.5 min-h-58 bg-gray-0 rounded-lg"},G1={key:0,class:"text-xs"},H1={key:1,class:"text-xs"},Y1={key:2,class:"text-xs"},K1={class:"flex flex-wrap justify-start items-center"},W1={class:"max-w-[400px]"},X1={key:0},J1=["onClick"],Z1=["onClick"],eb=Le({__name:"BatchRefundResult",props:{batchRefundRes:{}},emits:["addNewTab"],setup(a,{emit:i}){const u=i,p=a,{ticketList:f,doCopy:d,goTicketQuery:o}=q1(p,u);return(_,h)=>{const y=nt,T=_t;return s(),g("div",z1,[t("div",{class:Pe(["leading-tight justify-start items-center gap-1 inline-flex border rounded p-2.5 min-w-full",_.batchRefundRes.allSuccess?"text-green-2 bg-green-4 border-green-3":"text-red-1 bg-red-3 border-red-2"])},[r(y,null,{default:c(()=>[_.batchRefundRes.allSuccess?(s(),re(e(Bn),{key:0,class:"text-base leading-tight text-green-2"})):(s(),re(e(In),{key:1,class:"text-base leading-tight text-red-1"}))]),_:1}),_.batchRefundRes.allSuccess?(s(),g("span",G1,n(_.$t("app.batchRefund.successTip")),1)):_.batchRefundRes.allFail?(s(),g("span",H1,n(_.$t("app.batchRefund.failedTip")),1)):(s(),g("span",Y1,n(_.$t("app.batchRefund.partialFailedTip")),1))],2),t("div",K1,[(s(!0),g(_e,null,Re(e(f),(b,S)=>(s(),g("div",{key:S,class:"w-[1/10] flex justify-between items-center mt-2.5 mr-2.5 py-3.5 px-2.5 border rounded border-brand-3"},[b.success?(s(),re(y,{key:0,class:"mr-1.5"},{default:c(()=>[r(e(Bn),{class:"text-base leading-tight text-green-2"})]),_:1})):ee("",!0),b.success?ee("",!0):(s(),re(T,{key:1,placement:"top"},{content:c(()=>[t("div",W1,[t("div",null,n(_.$t("app.batchRefund.errorCode"))+n(b.errorCode||"--"),1),t("div",null,n(_.$t("app.batchRefund.description"))+n(b.description||"--"),1),t("div",null,n(_.$t("app.batchRefund.transactionNo"))+n(b.transactionNo||"--"),1),b.satTransactionNo?(s(),g("div",X1,n(_.$t("app.batchRefund.satTransactionNo"))+n(b.satTransactionNo||"--"),1)):ee("",!0)])]),default:c(()=>[b.success?ee("",!0):(s(),re(y,{key:0,class:"mr-1.5"},{default:c(()=>[r(e(In),{class:"text-base leading-tight text-red-1 cursor-pointer"})]),_:1}))]),_:2},1024)),t("span",{class:"text-brand-2 text-xs leading-tight mr-2.5 pt-[1px] cursor-pointer",onClick:k=>e(o)(b.ticketNo)},n(b.ticketNo),9,J1),t("em",{class:"iconfont icon-copy cursor-pointer leading-none text-brand-2 w-[14px] h-[14px]",onClick:k=>e(d)(b.ticketNo)},null,8,Z1)]))),128))])])}}});const tb=dt(eb,[["__scopeId","data-v-215dd92e"]]),nb=()=>{const{t:a}=Ze(),i=De(!1),u=Ia(),p=Ls(),f=Nn(),d=I("ticketQuery"),o=I([]),_=I(),h=De(""),y=I(),T=De(!1),b=I({}),S=De(!1),k=De(""),$=De(!1),m=De(!1),A=De(!1),l=De(!1),P=De(!1),j=I({}),X=De(""),ce=De(""),M=I({ticketType:"",ticketNo:""}),w=I({}),W=I([{title:a("app.agentTicketQuery.ticketList"),name:"ticketQuery",content:Jt(Q1)}]),C=(v,G,L)=>{var ae;L&&(j.value=L),G?((ae=_.value)==null||ae.queryTktByRoute(G,"1"),d.value="ticketQuery"):(W.value.findIndex(Ne=>Ne.name===v.name)===-1&&W.value.push(v),d.value=v.name,v.name.indexOf("?type=ticketNo&sign=")!==-1&&(h.value=v.name.split("?type=ticketNo&sign=")[1]))},R=(v,G)=>{const L=W.value;let ae;v===-1&&(ae=W.value.findIndex(ie=>ie.name===G)),d.value===G&&W.value.forEach((ie,Ne)=>{ie.name===G&&(d.value=L[Ne-1].name)}),W.value.splice(ae||v,1)},N=v=>{d.value=W.value[v].name},K=()=>{var v;(v=_.value)==null||v.queryTkt()},oe=(v,G)=>{d.value="ticketQuery",o.value=v,k.value=G,_.value.closeLoading()},te=()=>{T.value=!0},D=v=>{var L;T.value=!1;const G={title:`${a("app.batchRefund.batchRefundResult")}`,name:`${a("app.batchRefund.batchRefundResult")}`,content:Jt(tb)};C(G),b.value=v,(L=y.value)==null||L.closeLoading()},E=()=>{S.value=!0},pe=()=>{l.value=!0},ue=()=>{P.value=!0},ge=()=>{S.value=!1,Je.alert(a("app.agentTicketQuery.bopSuccessTips"),{icon:Ce("em",{class:"iconfont icon-check-circle"}),customClass:"u-msg-box-icon",confirmButtonText:a("app.ticketStatus.confirmBtn"),showClose:!1})},se=v=>({ticketNo:M.value.ticketNo,ticketType:ce.value||M.value.ticketType,ticketManagementOrganizationCode:M.value.ticketManagementOrganizationCode,printerNo:X.value,refundNo:v??""}),de=async(v,G)=>{var ae;ce.value=G,X.value=v;const L=se();try{i.value=!0;const ie=we("091T0104"),{data:Ne}=await ln(L,ie);w.value=(ae=Ne.value)==null?void 0:ae.data,A.value=!0}finally{i.value=!1}},H=async v=>{var ie;const{ticketNo:G,ticketType:L,printerNo:ae}=v;M.value={ticketNo:G,ticketType:L},X.value=ae;try{i.value=!0;const Ne=we("091T0104"),{data:Ae}=await ln(v,Ne);w.value=(ie=Ae.value)==null?void 0:ie.data,w.value.ticketManagementOrganizationCode=v.ticketManagementOrganizationCode??"",A.value=!0}finally{i.value=!1}},Q=(v,G,L,ae,ie)=>({ticketNo:v,refundNo:G,ticketType:L?"D":"I",printerNo:ae,ticketManagementOrganizationCode:ie}),q=async(v,G,L,ae,ie)=>{var Ne,Ae,z;try{i.value=!0;const ke=we("091T0107"),{data:x}=await ln(Q(v,G,L,ae,ie),ke),O=L?"D":"I",Y=((Ne=x.value)==null?void 0:Ne.data.ticketNo)??"";M.value={ticketNo:Y,ticketType:O,ticketManagementOrganizationCode:ie},X.value=ae,(Ae=_.value)==null||Ae.closeRefundView(),w.value=(z=x.value)==null?void 0:z.data,w.value.ticketManagementOrganizationCode=ie??"",A.value=!0,i.value=!1}finally{i.value=!1}},V=()=>{$.value=!0},B=()=>{m.value=!0};return Ct(()=>$.value,v=>{u.setShowManualRefund(v)}),Sa(()=>{var v;if(p.query.ticketNo&&p.query.type){const G={title:`${a("app.agentTicketRefund.refund")}${p.query.ticketNo}`,name:`refund?type=ticketNo&sign=${p.query.ticketNo}`,content:Jt(xn)};C(G,"",{secondFactorCode:"CN",secondFactorValue:(v=p==null?void 0:p.query)==null?void 0:v.refundPnrNo}),f.replace("/v2/crs/ticketOperation")}}),{loading:i,printNo:X,printType:ce,showRefundFormDialog:A,refundOperationCondition:M,refundFormData:w,editableTabs:W,currentTab:d,refundEtNumber:h,queryType:k,queryTicketRes:o,ticketQueryConditionRef:_,showBatchRefund:T,showAuthOffice:m,showManualRefund:$,batchRefundRes:b,batchRefundRef:y,factor:j,showBopRefund:S,showRtktDialog:l,showCccfDialog:P,addTab:C,changeTab:N,removeTab:R,handleQueryTicket:oe,reQueryTicket:K,openBatchRefund:te,handleBatchRefund:D,openBopRefund:E,bopRefundSuccess:ge,openManualRefund:V,openAuthOffice:B,openRefundFormDialog:de,deliverRefundData:H,openRefundDialog:q,openRtkt:pe,openCccf:ue}},ab=nb,sb={class:"h-[27px] p-[4px] mr-2.5"},ob=["href","download"],ib=t("i",{class:"iconfont icon-download text-[16px] text-[var(--bkc-el-color-primary)]"},null,-1),lb=Le({__name:"DownloadTemplate",setup(a){const{t:i}=Ze(),u=I(js()),p=De(""),f=De(""),d=()=>{p.value=`${Qo}/SGUI-Batch-Refund-Template-CRS${u.value==="en"?"-EN":""}.xlsx`,f.value=i("app.batchRefund.batchRefundTemplate")};return(o,_)=>(s(),g("div",sb,[t("a",{href:p.value,download:f.value,class:"no-underline text-[var(--bkc-el-color-primary)]"},[ib,t("span",{class:"text-[12px]",onClick:d},n(o.$t("app.batchRefund.downloadTemplate")),1)],8,ob)]))}}),rb=()=>{const a=I(""),i=I([]),u=I([]),p=d=>new Promise(o=>{const _=new FileReader;_.readAsBinaryString(d),_.onload=h=>{var y;o((y=h==null?void 0:h.target)==null?void 0:y.result)}});return{fileName:a,resolveFile:async d=>{const o=d.raw;if(o){a.value=o.name;const _=await p(o),T=(await new Go.Workbook().xlsx.load(_)).getWorksheet(1),b=[],S=[];T==null||T.eachRow({includeEmpty:!0},function(k,$){if($===1)k.eachCell({includeEmpty:!0},function(m){S.push(m.value)});else{const m={};k.eachCell({includeEmpty:!0},function(A,l){(A.value||A.value===0)&&(m[S[l-1]]=A.value)}),Bs(m)||b.push(m)}}),u.value=S,i.value=b}},jsonData:i,fileContentTitle:u}},cb=a=>{const{t:i}=Ze(),{fileName:u,jsonData:p,resolveFile:f,fileContentTitle:d}=rb(),{postToAIGPersonalization:o}=la(da.salesTicketManagementRefundedAig),_=De(!0),h=De(!1),y=De(!1),T=De(!1),b=I("part"),S=I(),k=I({batchTkt:[{ticketNo:"",domestic:!0,voteCounterNo:"",commissionAmount:"",commissionRate:"",netRefund:"",currency:"",otherDeduction:"",failure:!1,errorCode:"",description:"",satTransactionNo:"",transactionNo:""}]}),$={ticketNo:[{required:!0,message:i("app.batchRefund.ticketNoNotEmpty"),trigger:"blur"},{pattern:Tn,message:i("app.batchRefund.formatError"),trigger:"blur"}],voteCounter:[{required:!0,message:i("app.batchRefund.printNoNotEmpty"),trigger:"blur"},{pattern:Ht,message:i("app.batchRefund.formatError"),trigger:"blur"}]},m=je(()=>k.value.batchTkt.every(D=>!D.ticketNo)),A=je(()=>k.value.batchTkt.every(D=>!D.netRefund||D.failure)),l=()=>{_.value=!0,h.value=!1,a("update:modelValue",!1)},P=async D=>{var E;k.value.batchTkt.splice(D,1),(E=S.value)==null||E.validate()},j=()=>{const D={ticketNo:"",domestic:!0,voteCounterNo:"",commissionAmount:"",commissionRate:"",netRefund:"",currency:"",otherDeduction:"",failure:!1,errorCode:"",description:"",satTransactionNo:"",transactionNo:""};k.value.batchTkt.length>0&&(D.voteCounterNo=k.value.batchTkt[0].voteCounterNo),k.value.batchTkt.push(D)},X=()=>{const D=u.value.substring(u.value.lastIndexOf("."));return[".xls",".xlsx"].includes(D)},ce=()=>{var D,E,pe,ue;return((D=d.value)==null?void 0:D.length)===3&&((E=d.value)==null?void 0:E[0])===i("app.batchRefund.ticketNo")&&((pe=d.value)==null?void 0:pe[1])===i("app.batchRefund.printNo")&&((ue=d.value)==null?void 0:ue[2])===i("app.batchRefund.ticketType")},M=(D,E)=>D[E],w=()=>p.value.map(D=>{const[E,pe,ue]=[M(D,i("app.batchRefund.ticketNo")),M(D,i("app.batchRefund.printNo")),M(D,i("app.batchRefund.ticketType"))];if(E||ue||pe||pe===0)return{ticketNo:E??"",domestic:ue?ue.trim()===i("app.batchRefund.domestic"):!0,voteCounterNo:pe||pe===0?`${pe}`:""}}),W=()=>{var E;if([_.value,h.value]=[!0,!1],(E=S.value)==null||E.clearValidate(),!X()){_.value=!1;return}if(!ce()){h.value=!0;return}let D=w();if(D.length<=0){h.value=!0;return}D=D.length<=20?D:D.slice(0,20),k.value.batchTkt=[...D]},C=()=>{y.value=!1},R=()=>k.value.batchTkt.map(D=>({ticketNo:D.ticketNo,domestic:D.domestic,printNo:D.voteCounterNo})),N=async()=>{var D;(D=S.value)==null||D.validate(async E=>{if(E){let pe;try{y.value=!0;const ue=we("091U0104");pe=(await $o({ticketList:R()},ue)).data.value,a("handleBatchRefund",pe)}catch{y.value=!1}try{if(pe!=null&&pe.refundOrder){const ue=pe==null?void 0:pe.refundOrder;await o(ue)}}catch(ue){console.error("批量退票入库异常:",ue.message)}}else{await Je.alert(i("app.batchRefund.formatErrorTips"),{icon:Ce("em",{class:"iconfont icon-close-circle-line text-[var(--bkc-color-special-red-1)]"}),customClass:"u-msg-box-icon",confirmButtonText:i("app.batchRefund.confirm"),showClose:!1,draggable:!0});return}})},K=D=>D==="all"?k.value.batchTkt.map(pe=>({ticketNo:pe.ticketNo,domestic:pe.domestic,printNo:pe.voteCounterNo})):k.value.batchTkt.filter(pe=>!pe.netRefund||pe.failure).map(pe=>({ticketNo:pe.ticketNo,domestic:pe.domestic,printNo:pe.voteCounterNo})),oe=async D=>{var E;T.value=!1,(E=S.value)==null||E.validate(async pe=>{if(!pe){await Je.alert(i("app.batchRefund.formatErrorTips"),{icon:Ce("em",{class:"iconfont icon-close-circle-line text-[var(--bkc-color-special-red-1)]"}),customClass:"u-msg-box-icon",confirmButtonText:i("app.batchRefund.confirm"),showClose:!1,draggable:!0});return}if(D&&te()){T.value=!0;return}try{y.value=!0;const ue=D?"all":b.value,ge=we("091U0101"),se=(await Ro({ticketList:K(ue)},ge)).data.value;y.value=!1,(k.value.batchTkt??[]).forEach(de=>{(se??[]).forEach(H=>{var Q,q,V,B,v;Gt(de.ticketNo)===Gt(H.ticketNo)&&(de.commissionAmount=((Q=H.refundFeeDTO)==null?void 0:Q.commissionAmount)??"",de.commissionRate=((q=H.refundFeeDTO)==null?void 0:q.commissionRate)??"",de.otherDeduction=((V=H.refundFeeDTO)==null?void 0:V.otherDeduction)??"",de.netRefund=((B=H.refundFeeDTO)==null?void 0:B.netRefund)??"",de.currency=((v=H.refundFeeDTO)==null?void 0:v.currency)??"",de.failure=H.failure,de.errorCode=H.errorCode??"",de.description=H.description??"",de.satTransactionNo=H.satTransactionNo??"",de.transactionNo=H.transactionNo??"")})})}catch{y.value=!1}})},te=()=>{const D=k.value.batchTkt.filter(E=>!E.netRefund||E.failure);return D.length!==0&&D.length!==k.value.batchTkt.length};return{scopeType:b,showQueryScope:T,disabledBatchRefund:A,batchDataRule:$,confirmBatchRefund:N,closeBatchRefund:l,deleteBatchTktInfo:P,addBatchTktInfo:j,disabledBatch:m,fileName:u,resolveFile:f,jsonData:p,importBatchTktInfo:W,isFailure:h,isFileType:_,loading:y,closeLoading:C,formRef:S,batchRefundFormData:k,queryRefundFee:oe}},ub=cb,db=t("i",{class:"iconfont icon-close"},null,-1),pb=[db],fb={class:"text-[18px] font-[700] text-[var(--bkc-color-gray-1)]"},mb={class:"flex item-center batch-tips h-[36px] p-2 mb-[10px] border border-solid border-yellow-2 rounded-[4px] bg-yellow-3 text-yellow-1"},gb=t("em",{class:"iconfont icon-warning-circle-fill mr-[8px] leading-5"},null,-1),kb={class:"text-xs leading-5"},yb={class:"flex justify-between"},vb=t("i",{class:"icon-plus-square iconfont text-[var(--bkc-el-color-primary)]"},null,-1),hb={key:0,class:"px-[5px] py-[10px] text-[var(--bkc-color-special-red-1)]"},_b={key:1,class:"px-[5px] py-[10px] text-[var(--bkc-color-special-red-1)]"},bb={class:"p-[10px] pl-[10px] border border-solid border-[var(--bkc-theme-2)] rounded-[8px] mt-[20px] mb-[10px]"},xb={class:"max-h-[450px] overflow-y-scroll batch-refund-form"},Tb={class:"flex text-[12px] mb-[5px]"},Nb={class:"w-[140px] mr-[40px] text-[var(--bkc-color-gray-1)]"},$b=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),Rb={class:"w-[60px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Cb=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),wb={class:"w-[120px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Sb=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),Pb={class:"w-[100px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Ab={class:"w-[100px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Db={class:"w-[192px] text-[var(--bkc-color-gray-1)]"},Eb={key:0,class:"w-5"},Ob={class:"w-[100px] h-8 mr-[40px] justify-start items-center inline-flex"},Fb={key:0,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Vb={key:1,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Mb={key:2,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Lb={class:"w-[100px] h-8 mr-[40px] justify-start items-center inline-flex"},jb={key:0,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Bb={key:1,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Ib={class:"w-[192px] h-8 justify-start items-center inline-flex"},Ub={key:0,class:"text-red-1 text-xs font-bold leading-tight"},Qb={class:"max-w-[400px] max-h-[200px] overflow-y-auto"},qb={key:0},zb={class:"flex align-center cursor-pointer"},Gb={class:"text-red-1 text-xs font-bold leading-tight"},Hb={key:2,class:"text-red-1 text-xs font-bold leading-tight"},Yb=["onClick"],Kb=t("em",{class:"iconfont icon-delete text-[20px] relative text-brand-2"},null,-1),Wb=[Kb],Xb={key:0,class:"w-full text-center text-[var(--bkc-el-color-primary)] cursor-pointer"},Jb={class:"crs-btn-dialog-ui"},Zb={class:"flex items-center"},ex=t("div",{class:"iconfont icon-info-circle-line text-brand-2 mr-[16px]"},null,-1),tx={class:"text-lg text-gray-1"},nx=Le({__name:"BatchRefundDialog",emits:["handleBatchRefund","update:modelValue"],setup(a,{expose:i,emit:u}){const p=u,{scopeType:f,showQueryScope:d,disabledBatchRefund:o,batchDataRule:_,confirmBatchRefund:h,closeBatchRefund:y,deleteBatchTktInfo:T,addBatchTktInfo:b,disabledBatch:S,fileName:k,isFailure:$,isFileType:m,resolveFile:A,importBatchTktInfo:l,closeLoading:P,loading:j,formRef:X,batchRefundFormData:ce,queryRefundFee:M}=ub(p);return i({closeLoading:P}),(w,W)=>{const C=vt,R=Ho,N=et,K=oa,oe=ct,te=Rn,D=nt,E=_t,pe=ut,ue=Dt,ge=Et,se=rt,de=ht;return s(),re(se,{"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,class:"batch-refund-dialog tc-input-pad-init","align-center":!0,width:"1040px",onClose:e(y)},{header:c(()=>[t("span",fb,n(w.$t("app.batchRefund.batchRefund")),1)]),footer:c(()=>[t("span",Jb,[Ue((s(),re(N,{disabled:e(S),type:"primary","data-gid":"091U0101",onClick:W[2]||(W[2]=H=>e(M)(!0))},{default:c(()=>[J(n(w.$t("app.batchRefund.checkTheRefundFee")),1)]),_:1},8,["disabled"])),[[de,e(j),void 0,{fullscreen:!0,lock:!0}]]),Ue((s(),re(N,{"data-gid":"091U0104",disabled:e(o),type:"primary",onClick:e(h)},{default:c(()=>[J(n(w.$t("app.batchRefund.batchRefundBtn")),1)]),_:1},8,["disabled","onClick"])),[[de,e(j),void 0,{fullscreen:!0,lock:!0}]]),r(N,{onClick:e(y)},{default:c(()=>[J(n(w.$t("app.batchRefund.cancel")),1)]),_:1},8,["onClick"])])]),default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:W[0]||(W[0]=(...H)=>e(y)&&e(y)(...H))},pb),t("div",mb,[gb,t("span",kb,n(w.$t("app.batchRefund.batchRefundTips")),1)]),t("div",yb,[r(R,{accept:".xlsx, .xls",action:"","show-file-list":!1,"auto-upload":!1,"on-change":e(A)},{default:c(()=>[r(C,{modelValue:e(k),"onUpdate:modelValue":W[1]||(W[1]=H=>Me(k)?k.value=H:null),placeholder:w.$t("app.batchRefund.pleaseSelectImportFile"),class:Pe(["mr-[10px]","cursor-pointer",e($)||!e(m)?"upload-box-error":""])},{suffix:c(()=>[vb]),_:1},8,["modelValue","placeholder","class"])]),_:1},8,["on-change"]),r(lb),r(N,{disabled:!e(k),type:"primary",onClick:e(l)},{default:c(()=>[J(n(w.$t("app.batchRefund.importBtn")),1)]),_:1},8,["disabled","onClick"])]),e($)?(s(),g("span",hb,n(w.$t("app.batchRefund.importFailureTips")),1)):ee("",!0),e(m)?ee("",!0):(s(),g("span",_b,n(w.$t("app.batchRefund.fileTypeTips")),1)),r(K,{"border-style":"dashed"}),t("div",bb,[t("div",xb,[t("div",Tb,[t("p",Nb,[J(n(w.$t("app.batchRefund.ticketNo")),1),$b]),t("p",Rb,[J(n(w.$t("app.refundForm.ticketType")),1),Cb]),t("p",wb,[J(n(w.$t("app.batchRefund.printNo")),1),Sb]),t("p",Pb,n(w.$t("app.batchRefund.agencyFeeRate")),1),t("p",Ab,n(w.$t("app.batchRefund.commission")),1),t("p",Db,n(w.$t("app.batchRefund.refundTotal")),1),e(ce).batchTkt.length>1?(s(),g("p",Eb)):ee("",!0)]),r(pe,{ref_key:"formRef",ref:X,model:e(ce)},{default:c(()=>[(s(!0),g(_e,null,Re(e(ce).batchTkt,(H,Q)=>(s(),g("div",{key:Q,class:"flex"},[r(oe,{class:"inline-flex mr-[40px] mb-[10px] w-[140px]",prop:"batchTkt."+Q+".ticketNo",rules:e(_).ticketNo},{default:c(()=>[r(C,{modelValue:H.ticketNo,"onUpdate:modelValue":q=>H.ticketNo=q,modelModifiers:{trim:!0},clearable:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"]),r(oe,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"batchTkt."+Q+".domestic"},{default:c(()=>[r(te,{modelValue:H.domestic,"onUpdate:modelValue":q=>H.domestic=q,"inline-prompt":"","active-text":w.$t("app.issue.dom"),"inactive-text":w.$t("app.issue.intr")},null,8,["modelValue","onUpdate:modelValue","active-text","inactive-text"])]),_:2},1032,["prop"]),r(oe,{class:"inline-flex mr-[40px] w-[120px] mb-[10px]",prop:"batchTkt."+Q+".voteCounterNo",rules:e(_).voteCounter},{default:c(()=>[(s(),re(Kt,{key:H.voteCounterNo+Q,modelValue:H.voteCounterNo,"onUpdate:modelValue":[q=>H.voteCounterNo=q,q=>{var V;return(V=e(X))==null?void 0:V.validateField(`batchTkt.${Q}.voteCounterNo`)}],modelModifiers:{trim:!0},"select-class":"w-[140px]"},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1032,["prop","rules"]),t("div",Ob,[!H.commissionAmount&&!H.commissionRate?(s(),g("div",Fb,"-")):Number(H.commissionAmount)>0||Number(H.commissionRate)===0?(s(),g("div",Vb,n(H.currency)+" "+n(Number(H.commissionAmount).toFixed(2)),1)):(s(),g("div",Mb,n(H.commissionRate)+"%",1))]),t("div",Lb,[H.otherDeduction?(s(),g("div",Bb,n(H.currency)+" "+n(Number(H.otherDeduction).toFixed(2)),1)):(s(),g("div",jb,"-"))]),t("div",Ib,[!H.netRefund&&!H.failure?(s(),g("div",Ub,"-")):H.failure?(s(),re(E,{key:1,placement:"top"},{content:c(()=>[t("div",Qb,[t("div",null,n(w.$t("app.batchRefund.errorCode"))+n(H.errorCode||"--"),1),t("div",null,n(w.$t("app.batchRefund.description"))+n(H.description||"--"),1),t("div",null,n(w.$t("app.batchRefund.transactionNo"))+n(H.transactionNo||"--"),1),H.satTransactionNo?(s(),g("div",qb,n(w.$t("app.batchRefund.satTransactionNo"))+n(H.satTransactionNo||"--"),1)):ee("",!0)])]),default:c(()=>[t("span",zb,[r(D,{class:"mr-1"},{default:c(()=>[r(e(In),{class:"text-base leading-tight text-red-1 cursor-pointer"})]),_:1}),t("span",Gb,n(w.$t("app.batchRefund.countFailed")),1)])]),_:2},1024)):(s(),g("div",Hb,n(H.currency)+" "+n(Number(H.netRefund).toFixed(2)),1))]),e(ce).batchTkt.length>1?(s(),g("div",{key:0,class:"cursor-pointer w-5 h-8 justify-start items-center gap-2.5 inline-flex",onClick:q=>e(T)(Q)},Wb,8,Yb)):ee("",!0)]))),128))]),_:1},8,["model"]),e(ce).batchTkt.length<20?(s(),g("div",Xb,[r(N,{link:"",type:"primary",onClick:e(b)},{default:c(()=>[J(n(w.$t("app.batchRefund.addTicketNo")),1)]),_:1},8,["onClick"])])):ee("",!0)])]),r(se,{modelValue:e(d),"onUpdate:modelValue":W[5]||(W[5]=H=>Me(d)?d.value=H:null),class:"scope-dialog","show-close":!1,width:"500px",top:"40vh","close-on-click-modal":!0},{footer:c(()=>[r(ge,{modelValue:e(f),"onUpdate:modelValue":W[3]||(W[3]=H=>Me(f)?f.value=H:null)},{default:c(()=>[r(ue,{label:"part",size:"large"},{default:c(()=>[J(n(w.$t("app.batchRefund.partialTicketsd")),1)]),_:1}),r(ue,{label:"all",size:"large",class:"mr-[20px]"},{default:c(()=>[J(n(w.$t("app.batchRefund.allTicket")),1)]),_:1})]),_:1},8,["modelValue"]),Ue((s(),re(N,{type:"primary",onClick:W[4]||(W[4]=H=>e(M)(!1))},{default:c(()=>[J(n(w.$t("app.batchRefund.checkTheRefundFee")),1)]),_:1})),[[de,e(j),void 0,{fullscreen:!0,lock:!0}]])]),default:c(()=>[t("div",Zb,[ex,t("div",tx,n(w.$t("app.batchRefund.queryScope")),1)])]),_:1},8,["modelValue"])]),_:1},8,["onClose"])}}});const ax=a=>{const{t:i}=Ze(),u=it({refundFormNumber:"",deviceNum:""}),p=I(),f={refundFormNumber:[{required:!0,message:i("app.agentTicketQuery.rtNumEmpty"),trigger:"change"},{pattern:Xs,message:i("app.agentTicketQuery.rtNumError"),trigger:"change"}],deviceNum:[{required:!0,message:i("app.agentTicketRefund.prntNoNotEmpty"),trigger:"change"},{pattern:Ht,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"change"}]};return{bopFromData:u,rules:f,bopFrom:p,reFundBop:async()=>{p.value&&await p.value.validate(async _=>{var h;if(_){const y=Mt.service({fullscreen:!0});try{const T=we("091U0103");((h=(await Co(u,T)).data.value)==null?void 0:h.resCode)==="SUCCESS"&&a("bopRefundSuccess")}finally{y.close()}}})},cancel:()=>{a("update:modelValue",!1)}}},sx=ax,ox=t("i",{class:"iconfont icon-close"},null,-1),ix=[ox],lx={class:"flex item-center justify-start h-[36px] px-2.5 py-2 mt-[20px] mb-[10px] bg-yellow-3 rounded border border-solid border-yellow-2 text-yellow-1"},rx=t("em",{class:"iconfont icon-warning-circle-fill leading-5"},null,-1),cx={class:"ml-[5px] text-xs leading-5"},ux={class:"mt-[10px] px-[100px]"},dx={class:"flex justify-center crs-btn-dialog-ui"},px=Le({__name:"BopRefundDialog",emits:["bopRefundSuccess","update:modelValue"],setup(a,{emit:i}){const u=i,{bopFromData:p,rules:f,bopFrom:d,reFundBop:o,cancel:_}=sx(u);return(h,y)=>{const T=vt,b=ct,S=et,k=ut,$=rt;return s(),re($,{title:h.$t("app.agentTicketQuery.bopRefund"),"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,"align-center":"true",class:"preview-dialog bop-preview-dialog tc-input-pad-init",width:"680px",onClose:e(_)},{default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:y[0]||(y[0]=(...m)=>e(_)&&e(_)(...m))},ix),t("div",null,[t("div",lx,[rx,t("span",cx,n(h.$t("app.agentTicketQuery.bopRefundTips")),1)]),t("div",ux,[r(k,{ref_key:"bopFrom",ref:d,rules:e(f),"require-asterisk-position":"right",model:e(p)},{default:c(()=>[r(b,{label:h.$t("app.agentTicketQuery.rtNum"),prop:"refundFormNumber"},{default:c(()=>[r(T,{modelValue:e(p).refundFormNumber,"onUpdate:modelValue":y[1]||(y[1]=m=>e(p).refundFormNumber=m),modelModifiers:{trim:!0},onInput:y[2]||(y[2]=m=>{var A;return e(p).refundFormNumber=((A=e(p).refundFormNumber)==null?void 0:A.toUpperCase())??""})},null,8,["modelValue"])]),_:1},8,["label"]),r(b,{label:h.$t("app.agentTicketQuery.repelTicket.ticketMachineNumber"),prop:"deviceNum"},{default:c(()=>[r(Kt,{modelValue:e(p).deviceNum,"onUpdate:modelValue":[y[3]||(y[3]=m=>e(p).deviceNum=m),y[4]||(y[4]=m=>{var A;return(A=e(d))==null?void 0:A.validateField("deviceNum")})],modelModifiers:{trim:!0},"select-class":"w-[382.54px]"},null,8,["modelValue"])]),_:1},8,["label"]),t("div",dx,[r(S,{"data-gid":"091U0103",type:"primary",onClick:y[5]||(y[5]=m=>e(o)())},{default:c(()=>[J(n(h.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),r(S,{onClick:e(_)},{default:c(()=>[J(n(h.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),_:1},8,["rules","model"])])])]),_:1},8,["title","onClose"])}}});const fx=a=>{const{t:i}=Ze(),u=Rt(),p=I(!0),f=I(),d=I(""),o=I(""),_=I(!1),h=I(),y=I(),T=I("1"),b=I(!1),S=I(!0),k=I(),$=I(!1),m=I({authLevel:"oneLevel",office:"",ticketNo:"",ticketNoEnd:""}),A=je(()=>{var se;return((se=u.state.user)==null?void 0:se.agent)??""}),l=async()=>{var se,de,H,Q,q,V,B,v,G,L;try{const ae=we("091N0206");$.value=!0;const ie={ticketNumber:m.value.ticketNo},Ne=await Ba(ie,ae);((se=Ne.data.value)==null?void 0:se.code)==="200"?(d.value=((Q=(H=(de=Ne==null?void 0:Ne.data)==null?void 0:de.value)==null?void 0:H.data.ticketDisplayAuthInfo)==null?void 0:Q.bookOffice)??"",o.value=((B=(V=(q=Ne==null?void 0:Ne.data)==null?void 0:q.value)==null?void 0:V.data.ticketDisplayAuthInfo)==null?void 0:B.ticketNumber)??"",f.value=(L=(G=(v=Ne==null?void 0:Ne.data)==null?void 0:v.value)==null?void 0:G.data.ticketDisplayAuthInfo)==null?void 0:L.authInfoList.filter(Ae=>Ae.authTo!=="")):b.value=!1}finally{$.value=!1}},P=async se=>{var de;try{$.value=!0;const H={removeOffice:se,ticketNumber:m.value.ticketNo},Q=we("091N0102");((de=(await Un(H,Q)).data.value)==null?void 0:de.code)==="200"&&(await lt({message:i("app.intlPassengerForm.del.delSuccess"),type:"success"}),await l())}finally{$.value=!1}},j=()=>{_.value=!0},X=async()=>{var se,de;try{$.value=!0;let H=!0;m.value.authLevel==="oneLevel"?H=!1:H=!0;const Q={accreditOffice:m.value.office,reAuth:H,ticketNumber:m.value.ticketNo},q=we("091N0201"),V=await Qn(Q,q);((de=(se=V==null?void 0:V.data)==null?void 0:se.value)==null?void 0:de.code)==="200"&&(await lt({message:i("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await l())}finally{$.value=!1}},ce=async se=>{se&&await se.validate(async de=>{de&&X()})},M=(se,de,H)=>{var B;const Q=(B=m.value.ticketNo)==null?void 0:B.replace("-",""),q=Q==null?void 0:Q.substring(Q.length-3),V=parseInt(m.value.ticketNoEnd,10)-parseInt(q,10);if(V<0||V>29){H(i("app.agentTicketQuery.ticketAuth.lastThreeDigitsTips"));return}H()},w={office:[{required:!0,message:i("app.agentTicketQuery.ticketAuth.officeTipOne"),trigger:"blur"},{pattern:hn,message:i("app.agentTicketQuery.ticketAuth.officeTipTwo")}],ticketNo:[{required:!0,message:i("app.agentTicketQuery.ticketAuth.ticketTipOne"),trigger:"blur"},{pattern:/(^\d{3}(-)\d{10}$)|(^\d{13}$)/,message:i("app.agentTicketQuery.ticketAuth.ticketTipTwo"),trigger:"blur"}],ticketNoEnd:[{pattern:/^[0-9]{3}$/,message:i("app.agentTicketQuery.ticketAuth.ticketNoEndTipOne")},{validator:M,trigger:"blur"}]},W=()=>{var se;b.value=!1,(se=k.value)==null||se.resetFields(),m.value.office="",m.value.authLevel="oneLevel",m.value.ticketNoEnd=""},C=async()=>{try{$.value=!0,b.value=!0,await l()}finally{$.value=!1}},R=async()=>{b.value=!1},N=()=>{const se=[],de=m.value.ticketNo.replace("-","");if(se.push(de),!m.value.ticketNoEnd)return se;const H=parseInt(de.substring(10),10),q=parseInt(m.value.ticketNoEnd,10)-H+1;for(let V=1;V<q;V++){const B=(Number(de)+V).toString();se.push(B)}return se},K=async()=>{try{const se=we("091N0204");$.value=!0;const de=[];(N()??[]).forEach(q=>{const V={accreditOffice:m.value.office,reAuth:m.value.authLevel==="twoLevel",ticketNumber:q};de.push(Qn(V,se))}),(await Promise.allSettled(de)).filter(q=>q.value.data.value.code!=="200").length<1&&(await lt({message:i("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await l())}finally{$.value=!1}},oe=async()=>{try{const se=we("091N0205");$.value=!0;const de=[];(N()??[]).forEach(q=>{const V={removeOffice:m.value.office,ticketNumber:q};de.push(Un(V,se))}),(await Promise.allSettled(de)).filter(q=>q.value.data.value.code!=="200").length<1&&lt({message:i("app.intlPassengerForm.del.delSuccess"),type:"success"})}finally{$.value=!1}},te=()=>{var se;(se=k.value)==null||se.validate(de=>{if(de)switch(T.value){case"1":K();break;case"2":oe();break;case"3":C();break}})},D=()=>{a("update:modelValue",!1)},E=()=>`TICKET_AUTH_OFFICE_${A.value}_`??"",pe=()=>{try{const se=T.value==="2"?"delete":"add";return(JSON.parse(localStorage.getItem(`${E()}${se}`)??"")||[]).map(H=>({value:H}))}catch{return[]}},ue=()=>{if(hn.test(m.value.office)){const se=T.value==="2"?"delete":"add",de=pe().map(Q=>Q.value).filter(Q=>Q!==m.value.office);de.unshift(m.value.office);const H=de.slice(0,5);localStorage.setItem(`${E()}${se}`,JSON.stringify(H))}},ge=se=>{var de;m.value.office=se.value,(de=y.value)==null||de.blur()};return Ct(()=>m.value.office,se=>{m.value.office=(se==null?void 0:se.toUpperCase())??""}),{authType:T,authForm:m,authFormRules:w,authTicketShow:b,handleConfirm:te,handleCancel:D,ticketAuthFormRef:k,authChange:W,dialogTableVisible:p,tableData:f,authDelete:P,authSubmit:ce,authTicketFormRef:h,showAdd:j,addAuthVisible:_,office:d,showAuthOffice:S,closeShowAuth:R,ticketNum:o,showLoading:$,officeHistoryRef:y,loadOfficeHistory:pe,saveOfficeHistory:ue,selectOfficeHistory:ge}},mx=fx,fa=a=>(wt("data-v-3adb8bea"),a=a(),St(),a),gx={class:"pb-[20px]"},kx=fa(()=>t("i",{class:"iconfont icon-close"},null,-1)),yx=[kx],vx={class:"w-full px-0 pt-0.5 pb-[5px] bg-gray-0 rounded-md flex-col justify-start items-center gap-2.5 inline-flex mt-[-12px] query-auth-ticket"},hx={class:"self-stretch justify-between items-center inline-flex"},_x={class:"text-gray-1 text-lg font-bold leading-normal"},bx={class:"self-stretch h-full flex-col justify-center items-center flex"},xx={class:"self-stretch h-full flex-col justify-center items-center gap-2.5 flex"},Tx={class:"h-full flex-col justify-center items-start gap-5 flex"},Nx={class:"self-stretch flex-col justify-center items-start gap-2.5 flex"},$x={class:"justify-start items-center gap-2.5 inline-flex h-[20px] w-[420px]"},Rx={class:"text-gray-2 text-xs font-normal leading-tight"},Cx={class:"text-gray-2 text-xs font-normal leading-tight"},wx={class:"text-gray-2 text-xs font-normal leading-tight"},Sx={key:0,class:"justify-start items-center gap-2.5 inline-flex h-[20px] authType"},Px={class:"text-gray-2 text-xs font-normal leading-tight"},Ax={class:"text-gray-2 text-xs font-normal leading-tight"},Dx={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Ex={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Ox={class:"justify-start items-center flex w-full"},Fx={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Vx=fa(()=>t("div",{class:"text-gray-2 text-xs font-normal leading-tight"},"-",-1)),Mx={class:"self-stretch justify-center items-center gap-2.5 inline-flex w-full mt-5 crs-btn-dialog-ui"},Lx={class:"justify-start items-start flex"},jx={key:0,class:"w-full auth-data"},Bx={class:"self-stretch h-full flex-col justify-center items-center flex w-full"},Ix={class:"flex-col justify-center items-center gap-2.5 inline-flex w-full"},Ux={class:"self-stretch justify-between items-center inline-flex"},Qx={class:"justify-start items-center gap-[68px] flex"},qx={class:"text-gray-1 text-sm font-normal leading-snug"},zx={class:"text-gray-1 text-sm font-normal leading-snug"},Gx={class:"flex-col justify-start items-start inline-flex"},Hx={class:"self-stretch p-2.5 rounded border border-brand-3 justify-start items-start inline-flex"},Yx={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Kx={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Wx={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},Xx={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},Jx={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Zx={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},eT={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},tT={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},nT={key:0},aT={key:1},sT={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},oT={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},iT={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},lT=fa(()=>t("i",{class:"cursor-pointer primary-color iconfont icon-delete text-brand-2"},null,-1)),rT={key:0,class:"self-stretch h-[82px] flex-col justify-start items-start gap-3.5 flex add-auth-tkt"},cT={class:"self-stretch h-9 px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 flex"},uT={class:"h-5 justify-start items-center inline-flex text-yellow-1"},dT={class:"text-xs font-normal leading-tight"},pT={class:"w-[632px] justify-start items-center gap-2.5 inline-flex"},fT={class:"justify-start items-center gap-2.5 flex"},mT={class:"justify-start items-center flex"},gT={class:"text-gray-2 text-xs font-normal leading-tight"},kT={class:"text-gray-2 text-xs font-normal leading-tight"},yT={class:"justify-start items-center flex crs-btn-dialog-ui"},vT=Le({__name:"TicketAuth",emits:["update:modelValue"],setup(a,{emit:i}){const u=i,{authType:p,authForm:f,authFormRules:d,handleConfirm:o,ticketAuthFormRef:_,authChange:h,authTicketShow:y,handleCancel:T,tableData:b,authDelete:S,authSubmit:k,authTicketFormRef:$,showAdd:m,addAuthVisible:A,office:l,closeShowAuth:P,ticketNum:j,showAuthOffice:X,showLoading:ce,officeHistoryRef:M,loadOfficeHistory:w,saveOfficeHistory:W,selectOfficeHistory:C}=mx(u);return(R,N)=>{const K=Dt,oe=Et,te=ct,D=Xa,E=nt,pe=_t,ue=vt,ge=et,se=Ma,de=ut,H=oa,Q=rt,q=ht;return s(),re(Q,{modelValue:e(X),"onUpdate:modelValue":N[11]||(N[11]=V=>Me(X)?X.value=V:null),"close-on-click-modal":!1,"align-center":"true",width:"680px",class:"preview-dialog","close-on-press-escape":!1,"show-close":!1,onClose:e(T)},{default:c(()=>[Ue((s(),g("div",gx,[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:N[0]||(N[0]=(...V)=>e(T)&&e(T)(...V))},yx),t("div",vx,[t("div",hx,[t("div",_x,n(R.$t("app.agentTicketQuery.ticketAuth.title")),1)]),t("div",bx,[t("div",xx,[t("div",Tx,[r(de,{ref_key:"ticketAuthFormRef",ref:_,model:e(f),rules:e(d),"require-asterisk-position":"right",class:"w-[400px] auth-form"},{default:c(()=>[t("div",Nx,[t("div",$x,[r(oe,{modelValue:e(p),"onUpdate:modelValue":N[1]||(N[1]=V=>Me(p)?p.value=V:null),class:"h-[20px]",onChange:e(h)},{default:c(()=>[r(K,{label:"1"},{default:c(()=>[t("span",Rx,n(R.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1}),r(K,{label:"2"},{default:c(()=>[t("span",Cx,n(R.$t("app.agentTicketQuery.ticketAuth.deletingEntitlement")),1)]),_:1}),r(K,{label:"3"},{default:c(()=>[t("span",wx,n(R.$t("app.agentTicketQuery.ticketAuth.viewingAuthorization")),1)]),_:1})]),_:1},8,["modelValue","onChange"])]),e(p)==="1"?(s(),g("div",Sx,[r(te,null,{default:c(()=>[r(oe,{modelValue:e(f).authLevel,"onUpdate:modelValue":N[2]||(N[2]=V=>e(f).authLevel=V)},{default:c(()=>[r(K,{label:"oneLevel",size:"large"},{default:c(()=>[t("span",Px,n(R.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),r(K,{label:"twoLevel",size:"large"},{default:c(()=>[t("span",Ax,n(R.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1})])):ee("",!0),t("div",{class:Pe(["flex-col justify-center items-start gap-2.5 inline-flex w-full",{"mt-[-14px] authType-3":e(p)==="3"}])},[t("div",Dx,[e(p)==="1"||e(p)==="2"?(s(),re(te,{key:0,class:"w-full",label:R.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:c(()=>[r(D,{ref_key:"officeHistoryRef",ref:M,modelValue:e(f).office,"onUpdate:modelValue":N[3]||(N[3]=V=>e(f).office=V),modelModifiers:{trim:!0},"fetch-suggestions":e(w),placeholder:R.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(C),onBlur:e(W)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])):ee("",!0)]),t("div",Ex,[t("div",Ox,[r(te,{label:R.$t("app.agentTicketQuery.ticketAuth.ticketNo"),class:"ticket-no w-full ticketNo-div",prop:"ticketNo"},{default:c(()=>[t("div",Fx,[e(p)!=="3"?(s(),re(pe,{key:0,content:e(p)==="1"?R.$t("app.agentTicketQuery.ticketAuth.addTips"):R.$t("app.agentTicketQuery.ticketAuth.addTipsThree"),placement:"top-start","popper-class":"ticket-auth-popper"},{default:c(()=>[r(E,{class:"tooltip-icon ml-[-4px] mr-[13px] rotate-180"},{default:c(()=>[r(e(on))]),_:1})]),_:1},8,["content"])):ee("",!0),r(ue,{modelValue:e(f).ticketNo,"onUpdate:modelValue":N[4]||(N[4]=V=>e(f).ticketNo=V),modelModifiers:{trim:!0},class:"w-full",placeholder:R.$t("app.agentTicketQuery.ticketAuth.ticketTipOne")},null,8,["modelValue","placeholder"])])]),_:1},8,["label"])]),e(p)==="1"||e(p)==="2"?(s(),g(_e,{key:0},[Vx,r(te,{label:"",class:"ticket-no-end flex-nowrap",prop:"ticketNoEnd"},{default:c(()=>[r(ue,{modelValue:e(f).ticketNoEnd,"onUpdate:modelValue":N[5]||(N[5]=V=>e(f).ticketNoEnd=V),modelModifiers:{trim:!0},placeholder:R.$t("app.agentTicketQuery.ticketAuth.ticketNoEnd"),maxlength:"3"},null,8,["modelValue","placeholder"])]),_:1})],64)):ee("",!0)])],2)]),t("div",Mx,[t("div",Lx,[e(p)==="2"?(s(),re(se,{key:0,title:R.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),teleported:!1,width:"160",icon:e(on),"icon-color":"var(--bkc-el-color-primary)",placement:"top","cancel-button-type":"button","data-gid":"091N0205",onConfirm:e(o)},{reference:c(()=>[r(ge,{type:"primary"},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})]),_:1},8,["title","icon","onConfirm"])):(s(),re(ge,{key:1,type:"primary",onClick:e(o)},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1},8,["onClick"])),r(ge,{class:Pe([e(p)==="2"?"ml-[12px]":""]),onClick:e(T)},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1},8,["class","onClick"])])])]),_:1},8,["model","rules"])])]),e(y)&&e(b)?(s(),g("div",jx,[t("div",Bx,[r(H,{"border-style":"dashed"}),t("div",Ix,[t("div",Ux,[t("div",Qx,[t("div",qx,n(R.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+"："+n(e(j)),1),t("div",zx,n(R.$t("app.agentTicketQuery.ticketAuth.ticketingOfficeNumber"))+"："+n(e(l)),1)]),t("div",Gx,[t("div",{class:"justify-center items-center gap-1 inline-flex",onClick:N[6]||(N[6]=(...V)=>e(m)&&e(m)(...V))},[r(ge,{link:"",type:"primary",size:"small"},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1})])])]),t("div",Hx,[t("div",Yx,[t("div",Kx,[t("div",Wx,n(R.$t("app.agentTicketQuery.ticketAuth.officeNum")),1)]),(s(!0),g(_e,null,Re(e(b),(V,B)=>(s(),g("div",{key:B,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",Xx,n(V.authTo),1)]))),128))]),t("div",Jx,[t("div",Zx,[t("div",eT,n(R.$t("app.agentTicketQuery.ticketAuth.authAuthority")),1)]),(s(!0),g(_e,null,Re(e(b),(V,B)=>(s(),g("div",{key:B,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",tT,[V.reAuth?(s(),g("span",nT,[r(E,null,{default:c(()=>[r(e(Xn))]),_:1})])):(s(),g("span",aT,n(R.$t("app.agentTicketQuery.ticketAuth.noAuth")),1))])]))),128))]),t("div",sT,[t("div",oT,[t("div",iT,n(R.$t("app.agentTicketQuery.ticketAuth.operation")),1)]),(s(!0),g(_e,null,Re(e(b),(V,B)=>(s(),g("div",{key:B,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-5 inline-flex"},[r(se,{title:R.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),icon:e(on),teleported:!1,width:"160","icon-color":"var(--bkc-el-color-primary)","cancel-button-type":"button",onConfirm:v=>e(S)(V.authTo)},{reference:c(()=>[lT]),_:2},1032,["title","icon","onConfirm"])]))),128))])]),e(A)?(s(),g("div",rT,[t("div",cT,[t("div",uT,[r(E,{class:"tooltip-icon mr-[8px]"},{default:c(()=>[r(e(Wn))]),_:1}),t("div",dT,n(R.$t("app.agentTicketQuery.ticketAuth.addTipsTwo")),1)])]),t("div",pT,[t("div",fT,[r(de,{ref_key:"authTicketFormRef",ref:$,model:e(f),inline:"",size:"small",rules:e(d),"require-asterisk-position":"right"},{default:c(()=>[r(te,null,{default:c(()=>[r(oe,{modelValue:e(f).authLevel,"onUpdate:modelValue":N[7]||(N[7]=V=>e(f).authLevel=V)},{default:c(()=>[t("div",mT,[r(K,{label:"oneLevel",size:"large"},{default:c(()=>[t("span",gT,n(R.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),r(K,{label:"twoLevel",size:"large"},{default:c(()=>[t("span",kT,n(R.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),r(te,{label:R.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:c(()=>[r(D,{ref_key:"officeHistoryRef",ref:M,modelValue:e(f).office,"onUpdate:modelValue":N[8]||(N[8]=V=>e(f).office=V),modelModifiers:{trim:!0},"fetch-suggestions":e(w),placeholder:R.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(C),onBlur:e(W)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t("div",yT,[e(A)?(s(),re(ge,{key:0,type:"primary","data-gid":"091N0101",onClick:N[9]||(N[9]=V=>e(k)(e($)))},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})):ee("",!0),e(A)?(s(),re(ge,{key:1,onClick:N[10]||(N[10]=V=>e(P)())},{default:c(()=>[J(n(R.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1})):ee("",!0)])])])):ee("",!0)])])])):ee("",!0)])])])),[[q,e(ce)]])]),_:1},8,["modelValue","onClose"])}}});const hT=dt(vT,[["__scopeId","data-v-3adb8bea"]]),_T={class:"flex justify-between"},bT={class:"mt-[20px] h-[476px]"},xT={key:1,class:"flex justify-center items-center flex-col pt-[156px]"},TT=["alt"],NT={class:"mt-[19px] text-lg leading-[24px] font-bold text-gray-2"},$T={class:"text-base text-gray-4"},RT=Le({__name:"RtktDialog",emits:["update:modelValue"],setup(a,{emit:i}){const u=i,{t:p}=Ze(),f=I(!1),d=I(),o=De({});let _=[];const h={ticketNo:[{required:!0,message:p("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:Tn,message:p("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}]},y=it({ticketNo:""}),T=async()=>{const m=await Da("searchLocalData"),A=[];(JSON.parse(m.localData??"")??[]).forEach(l=>{const P={code:l.airportCode,cnName:l.cityArray[1],enName:l.cityArray[0]};A.push(P)}),_=A},b=m=>((m??[]).forEach((A,l)=>{_==null||_.forEach(P=>{P.code===A.departureCity&&(m[l]=Object.assign(m[l],{departureCityName:P.cnName})),P.code===A.arrivalCity&&(m[l]=Object.assign(m[l],{arrivalCityName:P.cnName}))})}),m),S=()=>{const{passenger:m}=o.value;m&&(m.segments=b(m==null?void 0:m.segments))},k=()=>{var m;(m=d.value)==null||m.validate(async A=>{if(A)try{f.value=!0;const l=we("091U0105"),{data:P}=await wo(y.ticketNo,l);o.value=P.value??{},await S()}catch{o.value={}}finally{f.value=!1}})},$=()=>{var m;(m=d.value)==null||m.resetFields()};return mt(async()=>{await T()}),(m,A)=>{const l=vt,P=ct,j=et,X=ut,ce=rt,M=ht;return s(),re(ce,{title:"RTKT",width:"890px","close-on-click-modal":!1,"align-center":!0,class:"repel-tikect-dialog tc-input-pad-init crs-new-ui-init-cls",onClose:A[2]||(A[2]=()=>u("update:modelValue",!1))},{default:c(()=>{var w,W;return[t("div",_T,[r(X,{ref_key:"formRef",ref:d,model:y,rules:h,inline:!0,"require-asterisk-position":"right"},{default:c(()=>[r(P,{prop:"ticketNo",label:e(p)("app.agentTicketQuery.ticketAuth.ticketNo")},{default:c(()=>[r(l,{modelValue:y.ticketNo,"onUpdate:modelValue":A[0]||(A[0]=C=>y.ticketNo=C),modelModifiers:{trim:!0},clearable:!0},null,8,["modelValue"])]),_:1},8,["label"]),r(P,null,{default:c(()=>[r(j,{type:"primary","data-gid":"091U0105",onClick:k},{default:c(()=>[J(n(m.$t("app.agentTicketQuery.queryBtn")),1)]),_:1})]),_:1}),r(P,null,{default:c(()=>[r(j,{onClick:$},{default:c(()=>[J(n(m.$t("app.agentTicketQuery.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),(w=o.value)!=null&&w.ticket?(s(),re(Lo,{key:0,"rtkt-detailed-info":o.value,onClick:A[1]||(A[1]=C=>u("update:modelValue",!1))},null,8,["rtkt-detailed-info"])):ee("",!0)]),Ue((s(),g("div",bT,[(W=o.value)!=null&&W.ticket?(s(),re(jo,{key:0,"rtkt-detailed-info":o.value},null,8,["rtkt-detailed-info"])):(s(),g("div",xT,[t("img",{src:ra,alt:m.$t("app.fastQuery.netPrice.nodata")},null,8,TT),t("p",NT,n(m.$t("app.fastQuery.netPrice.noOrderInfo")),1),t("p",$T,n(m.$t("app.agentTicketQuery.plsInputTicketToSearch")),1)]))])),[[M,f.value]])]}),_:1})}}});const Pt=Pa.global.t,is=[{label:Pt("app.agentTicketRefund.pay.cash"),value:"CASH"},{label:Pt("app.agentTicketRefund.pay.tc"),value:"TC"},{label:Pt("app.agentTicketRefund.pay.check"),value:"CHECK"},{label:Pt("app.agentTicketRefund.pay.gr"),value:"GR"},{label:Pt("app.agentTicketRefund.pay.ef"),value:"EF"}];Pt("app.agentTicketRefund.passenger.adt"),Pt("app.agentTicketRefund.passenger.chd"),Pt("app.agentTicketRefund.passenger.chd"),Pt("app.agentTicketRefund.passenger.yth"),Pt("app.agentTicketRefund.passenger.inf");const CT=[{label:Pt("app.pnrManagement.paymentMethod.domestic"),value:"D"},{label:Pt("app.pnrManagement.paymentMethod.international"),value:"I"}],wT="TYN202",ST="UP6225819300614137",PT=a=>{var ke;const{t:i}=Ze(),u=Rt(),p=27,f=I("ONLY_REFUND"),d=I(""),o=I(!1),_=I(),{defaultOffice:h,office:y,defaultRoleWithPid:T,agent:b,defaultOfficeIataNum:S}=u.state.user,k=T?h:((ke=y==null?void 0:y.split(";"))==null?void 0:ke[0])??"",$=I([]),m=I([]),A={BSP:{label:i("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:i("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:i("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:i("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:i("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:i("app.agentTicketQuery.OWNTicket"),value:"ARL"}},l=it({iata:S,agent:b,office:k,volunteer:"",createUser:"",printNo:"",check:"",marketAirline:"",currency:"",name:"",psgType:"",etTag:"1",remark:"",remarkCode:"",remarkInfo:"",creditCard:"",conjunction:"",airline:"",tktType:"D",payType:"CASH",ticketNo:"",totalAmount:"",commision:"",commisionRate:"",otherDeduction:"",otherDeductionRate:"",netRefund:"0.00",totalTaxs:"",taxs:[],rate:"",receiptPrinted:"",segment:[],crsPnrNo:"",pnr:"",isCoupon:"",isDragonBoatOffice:h===wT,refundDate:ot().format("DDMMMYY/HHmm").toUpperCase(),conjunctionTicketNos:[],ticketManagementOrganizationCode:""}),P=je(()=>{const x=`${l.airline}${l.ticketNo}`;return!Is.test(x)}),j=je(()=>{var x;return(x=u.state.user)==null?void 0:x.entityType}),X=je(()=>!["CDS","GPCDS"].includes(l.ticketManagementOrganizationCode)),ce=()=>{X.value||(l.printNo="")},M=(x,O,Y)=>{var ye;const fe=Number(x.field.split(".")[1]);l.taxs[fe].name&&!O?Y(i("app.agentTicketRefund.taxAmount")):!l.taxs[fe].name&&!O&&((ye=_.value)==null||ye.clearValidate(`taxs.${fe}.name`),Y()),Y()},w=(x,O,Y)=>{var ye;const fe=Number(x.field.split(".")[1]);l.taxs[fe].value&&!O?Y(i("app.agentTicketRefund.taxes")):!l.taxs[fe].value&&!O&&((ye=_.value)==null||ye.clearValidate(`taxs.${fe}.value`),Y()),Y()},W=(x,O,Y)=>{l.payType==="TC"&&(O?!l.isDragonBoatOffice&&!Zn.test(O)?Y(i("app.agentTicketRefund.creditCardInput")):l.isDragonBoatOffice&&!ea.test(O)&&Y(i("app.agentTicketRefund.dragonBoatOfficeInput")):Y(i("app.agentTicketRefund.creditCardNotEmpty"))),Y()},C=()=>($.value=new Array(4).fill(""),l.segment.some(x=>x)?l.segment.forEach((x,O)=>{x&&!no.test(x)&&($.value[O]=i("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:4}))}):$.value[0]=i("app.pnrManagement.validate.required"),$.value.every(x=>!x)),R=je(()=>({tktType:[{required:!0,message:i("app.agentTicketRefund.ticketTypeNotEmpty"),trigger:["change","blur"]}],printNo:[{required:!0,message:i("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:Ht,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],airline:[{required:!0,message:i("app.agentTicketRefund.refundAirlineSettlementCodeNotEmpty"),trigger:"blur"},{pattern:Va,message:i("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:3}),trigger:"blur"}],ticketNo:[{required:!0,message:i("app.agentTicketRefund.refundTicketNoNotEmpty"),trigger:"blur"},{pattern:Js,message:i("app.agentTicketRefund.supportDigitsAndHorizontalBar"),trigger:"blur"}],conjunction:[{required:!0,message:i("app.agentTicketRefund.numberOfCombinedTicketsNotEmpty"),trigger:"blur"},{pattern:Zs,message:i("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:1}),trigger:"blur"}],currency:[{required:!0,message:i("app.agentTicketRefund.currencyNotEmpty"),trigger:"blur"},{pattern:eo,message:i("app.agentTicketRefund.onlySupportLetters"),trigger:"blur"}],payType:[{required:!0,message:i("app.agentTicketRefund.paymentSel"),trigger:["change","blur"]},{pattern:Jn,message:i("app.agentTicketRefund.paymentInput"),trigger:["change","blur"]}],totalAmount:[{pattern:gt,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],taxValue:[{pattern:Ea,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"},{validator:M,trigger:"blur"}],taxName:[{pattern:Oa,message:i("app.agentTicketRefund.taxes"),trigger:"blur"},{validator:w,trigger:"blur"}],commisionRate:[{pattern:gt,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],otherDeductionRate:[{pattern:gt,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],otherDeduction:[{pattern:gt,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],commision:[{pattern:gt,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],psdName:[{pattern:to,message:i("app.agentTicketRefund.psgNameError"),trigger:"blur"}],creditCard:[{validator:W,trigger:"blur"}],remarkInfo:[{pattern:_n,message:i("app.agentTicketRefund.remarkHint"),trigger:"blur"}],netRefund:[{required:!0,message:i("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:gt,message:i("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}],ticketManagementOrganizationCode:[{required:!0,message:i("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"}]})),N=()=>{const{totalAmount:x,totalTaxs:O,otherDeduction:Y,commision:fe,commisionRate:ye}=l;if(!ge())if(ye){const Se=Lt(jt(Number(x),Number(ye)),100).toString(),Fe=rn(Number(Se),2).toString();l.commision=Fe;const Be=`${At(Qe(Number(x),Number(O)),Qe(Number(Y),Number(Fe)))}`;l.netRefund=Number(Be).toFixed(2)}else{const Se=`${At(Qe(Number(x),Number(O)),Qe(Number(Y),Number(fe)))}`;l.netRefund=Number(Se).toFixed(2)}},K=()=>{if(l.taxs.length===p)return;const x=l.taxs.length+5>p?p-l.taxs.length:5,O=new Array(x).fill({name:"",value:""});l.taxs=l.taxs.concat(O).map(Y=>({...Y}))},oe=()=>{let x=new dn(0);l.taxs.forEach((O,Y)=>{var fe;(fe=_.value)==null||fe.validateField(`taxs.${Y}.value`).then(ye=>{ye&&(x=x.add(new dn(O.value?O.value:0)),l.totalTaxs=Nt(l.currency)?x.toString():Number(x).toFixed(2),Nt(l.currency)?se(""):N())})})},te=x=>x.length<10?x.concat(new Array(10-x.length).fill({name:"",value:""})).map(O=>({...O})):x,D=async()=>{var x,O;try{o.value=!0;let Y=`${l.airline}${l.ticketNo}`;const fe=Y.indexOf("-");Y=fe!==-1?Y.substring(0,fe):Y;const ye=we("091Q0203"),Se=(O=(x=await Ua({ticketNo:Y},ye))==null?void 0:x.data)==null?void 0:O.value;l.taxs=[],((Se==null?void 0:Se.rtKTTaxes)??[]).forEach(Fe=>{l.taxs.push({name:Fe.taxType,value:Fe.taxAmount})}),l.taxs=te(l.taxs),l.totalAmount=(Se==null?void 0:Se.ticketAmount)??"",l.currency=(Se==null?void 0:Se.currency)??"",oe()}finally{o.value=!1}},E=async()=>{const x=[];l.taxs.forEach((O,Y)=>{var fe,ye;x.push((fe=_.value)==null?void 0:fe.validateField(`taxs.${Y}.name`)),x.push((ye=_.value)==null?void 0:ye.validateField(`taxs.${Y}.value`))}),await Promise.all(x),l.taxs.forEach((O,Y)=>{l.taxs[Y].value&&(l.taxs[Y].value=Nt(l.currency)?l.taxs[Y].value??0:Number(l.taxs[Y].value??0).toFixed(2))}),oe()},pe=async()=>{var x;l.segment=new Array(4).fill(""),$.value=new Array(4).fill(""),await cn(),l.ticketNo&&((x=_.value)==null||x.validateField("ticketNo"))},ue=x=>x&&!gt.test(x),ge=()=>{const{totalAmount:x,otherDeductionRate:O,otherDeduction:Y,commision:fe,commisionRate:ye}=l;return ue(x??"")||ue(O??"")||ue(Y??"")||ue(fe??"")||ue(ye??"")},se=x=>{if(x==="otherDeductionRate"&&l.otherDeductionRate){const Fe=Lt(jt(Number(l.totalAmount),Number(l.otherDeductionRate)),100).toString();l.otherDeduction=rn(Number(Fe),2).toString()}const{totalAmount:O,totalTaxs:Y,otherDeduction:fe,commision:ye,commisionRate:Se}=l;if(!ge()){if(Se){const Fe=Lt(jt(Number(O),Number(Se)),100).toString(),Be=rn(Number(Fe),2).toString();l.commision=Be.endsWith(".00")?Be.slice(0,-3):Be;const Ke=`${At(Qe(Number(O),Number(Y)),Qe(Number(fe),Number(Be)))}`;l.netRefund=Number(Ke).toFixed(2)}else l.netRefund=`${At(Qe(Number(O),Number(Y)),Qe(Number(fe),Number(ye)))}`;l.netRefund.endsWith(".00")&&(l.netRefund=l.netRefund.slice(0,-3))}},de=x=>{if(!ge()){if(Nt(l.currency)){se(x);return}if(x==="otherDeductionRate"&&l.otherDeductionRate){l.otherDeductionRate=Number(l.otherDeductionRate).toFixed(2);const O=Lt(jt(Number(l.totalAmount),Number(l.otherDeductionRate)),100).toString();l.otherDeduction=Number(O).toFixed(2)}l.totalAmount&&(l.totalAmount=Number(l.totalAmount).toFixed(2)),l.commision&&(l.commision=Number(l.commision).toFixed(2)),l.commisionRate&&(l.commisionRate=Number(l.commisionRate).toFixed(2)),l.otherDeduction&&(l.otherDeduction=Number(l.otherDeduction).toFixed(2)),N()}},H=()=>{l.commisionRate||(l.commision="")},Q=()=>{_.value.clearValidate("creditCard"),l.payType.toUpperCase()==="TC"&&(l.creditCard=l.isDragonBoatOffice?ST:"")},q=x=>{x.target.value&&!is.some(O=>O.label===x.target.value)&&(l.payType=x.target.value)},V=x=>({commision:x.commision&&Number(x.commision)>0?x.commision:"0",commisionRate:x.commisionRate??"",netRefund:x.netRefund,otherDeduction:x.otherDeduction||"0",taxs:x.taxs.filter(O=>!!O.value),totalAmount:x.totalAmount||"0",totalTaxs:x.totalTaxs}),B=x=>{const O=x.segment.filter(fe=>!!fe),Y=new Array(4-(O==null?void 0:O.length)).fill("0000");return[...O,...Y]},v=x=>({airline:x.airline,crsPnrNo:"",currency:x.currency,etTag:x.etTag,marketAirline:"",name:Ft.encode(x.name.trim()),payType:x.payType.toUpperCase(),pnr:"",psgType:"",segment:[],couponNos:B(x),ticketNo:`${x.airline}-${x.ticketNo}`,tktType:x.tktType}),G=x=>({modificationType:"ONLY_REFUND",prntNo:x.printNo,ticketManagementOrganizationCode:x.ticketManagementOrganizationCode,resultpre:{amount:V(x),conjunction:x.conjunction,creditCard:x.creditCard,isCoupon:x.isCoupon,office:x.office,operator:x.createUser,remark:x.remarkInfo??"",segList:[],ticket:v(x),volunteer:"NON_VOLUNTEER_MANUAL"}}),L=()=>{a("update:modelValue",!1)},ae=x=>({ticketNo:x,ticketType:l.tktType,printerNo:l.printNo??"",refundNo:d.value??"",ticketManagementOrganizationCode:l.ticketManagementOrganizationCode??""}),ie=async()=>{const x=`${l.airline}${l.ticketNo.includes("-")?l.ticketNo.split("-")[0]:l.ticketNo}`;await a("deliverRefundData",ae(x)),await L(),Je.close()},Ne=async()=>{var x;(x=_.value)==null||x.validate(async O=>{var fe,ye,Se,Fe;const Y=C();if(!(!O||!Y)){o.value=!0;try{if(f.value==="ONLY_REFUND"){const Be=G(l),Ke=we("091Q0202"),ve=(ye=(fe=await Bo(Be,Ke))==null?void 0:fe.data)==null?void 0:ye.value,Ye=(Se=ve==null?void 0:ve.data)==null?void 0:Se.refundNumber.replace("-","");d.value=Ye??"",l.ticketManagementOrganizationCode!=="ARL"&&(d.value=Ye?Ye.substring(3):"");const le=Ce("p",{className:"flex",style:{"margin-top":"10px"}},[Ce("span",{className:"text-[14px]"},`${i("app.agentTicketRefund.refundTicketNumber")}：`),Ce("span",{className:"text-[14px] text-brand-2 cursor-pointer",onClick:ie},d.value)]),pt=(Fe=ve==null?void 0:ve.data)!=null&&Fe.refundStatus.includes("success")?i("app.agentTicketRefund.refundSuccess"):i("app.agentTicketRefund.refundSuccessButStatusFailed");ro(pt,le).then(L).catch(L)}}finally{o.value=!1}}})},Ae=()=>{var x,O,Y,fe,ye,Se,Fe,Be,Ke,ve,Ye,le;((x=j.value)!=null&&x.includes("$$$")||(O=j.value)!=null&&O.includes("BSP"))&&(m.value.push(A.BSP),m.value.push(A.GPBSP)),!((Y=j.value)!=null&&Y.includes("BSP"))&&((fe=j.value)!=null&&fe.includes("GP"))&&m.value.push(A.GPBSP),((ye=j.value)!=null&&ye.includes("$$$")||(Se=j.value)!=null&&Se.includes("BOP"))&&m.value.push(A.BOPBSP),((Fe=j.value)!=null&&Fe.includes("$$$")||(Be=j.value)!=null&&Be.includes("CDS"))&&(m.value.push(A.CDS),m.value.push(A.GPCDS)),((Ke=j.value)!=null&&Ke.includes("$$$")||(ve=j.value)!=null&&ve.includes("本票"))&&m.value.push(A.ARL),l.ticketManagementOrganizationCode=((le=(Ye=m.value)==null?void 0:Ye[0])==null?void 0:le.value)??""};return(()=>{l.taxs=te([]),l.segment=new Array(4).fill(""),$.value=new Array(4).fill(""),Ae()})(),{refundType:f,fullscreenLoading:o,refundFormData:l,FORM_RULES:R,refundFormRef:_,MAX_TAX_NUM:p,segmentErrorMessage:$,queryRTKTDisabled:P,getTaxAll:D,validSegment:C,addTax:K,checkTax:E,calcAmount:de,changePayType:Q,bindPaymentValue:q,submitRefund:Ne,closeDialog:L,changeTicketType:pe,commisionRateChange:H,ticketOrganizationList:m,isShowPrintNo:X,changeTicketManagementOrganizationCode:ce}},AT=PT,DT=t("i",{class:"iconfont icon-close"},null,-1),ET=[DT],OT={class:"ticket-refund-form"},FT={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},VT={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},MT={class:"self-stretch justify-start items-start gap-5 inline-flex"},LT={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},jT={class:"w-[90px] text-gray-3 text-xs shrink-0"},BT=t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1),IT={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},UT={key:1,class:"inline-block w-[12px]"},QT={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},qT={key:1},zT={class:"inline-block w-[84px] text-[12px] text-gray-2"},GT=t("span",null,"-",-1),HT={class:"self-stretch justify-start items-start gap-5 inline-flex"},YT={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},KT={class:"w-[90px] text-gray-3 text-xs shrink-0"},WT={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},XT={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},JT={class:"w-[84px] text-gray-3 text-xs shrink-0"},ZT={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},e0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},t0={class:"w-[84px] text-gray-3 text-xs shrink-0"},n0={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},a0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},s0={class:"w-[84px] text-gray-3 text-xs shrink-0"},o0={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},i0={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},l0={class:"self-stretch justify-start items-start gap-5 inline-flex"},r0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] conjunction-num"},c0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] relative"},u0=t("span",{class:"iconfont icon-info-circle-line absolute left-[58px]"},null,-1),d0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] conjunction-num"},p0=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),f0={class:"self-stretch justify-start items-start gap-5 inline-flex"},m0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] passenger-name"},g0={class:"grow shrink basis-0 min-h-[32px] justify-start flex mb-[10px]"},k0={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0 refund-segment"},y0={class:"self-stretch justify-start items-start gap-5 inline-flex"},v0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require amount"},h0={key:0,class:"not-required-tip"},_0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require"},b0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require refund-type"},x0=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),T0={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},N0={class:"self-stretch justify-start items-start gap-5 inline-flex"},$0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},R0={class:"w-full mb-[10px]"},C0={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},w0={class:"ml-[20px]"},S0={class:"text-gray-2 font-[700]"},P0={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},A0={class:"w-[20px] text-gray-3 text-xs shrink-0 leading-8"},D0={class:"w-[40px] mr-[6px] shrink-0"},E0={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},O0={class:"self-stretch justify-start items-start gap-5 inline-flex"},F0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},V0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},M0=t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1),L0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},j0=t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1),B0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},I0={class:"self-stretch justify-start items-start gap-5 inline-flex"},U0={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},Q0={class:"w-[90px] text-gray-3 text-xs shrink-0"},q0={class:"justify-start items-center flex text-gray-2 text-xs relative"},z0=t("span",{class:"iconfont icon-info-circle-line absolute left-[-40px]"},null,-1),G0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},H0={class:"text-gray-2 font-[700]"},Y0=t("span",null,[t("i",{class:"iconfont icon-info-circle-line text-[20px] font-normal ml-[10px] text-gray-4"})],-1),K0={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},W0=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),X0={class:"flex justify-center w-full mt-[10px] footer items-center"},J0=Le({__name:"ManualRefund",emits:["update:modelValue","deliverRefundData"],setup(a,{emit:i}){const u=i,{refundType:p,fullscreenLoading:f,refundFormRef:d,refundFormData:o,FORM_RULES:_,MAX_TAX_NUM:h,segmentErrorMessage:y,queryRTKTDisabled:T,getTaxAll:b,validSegment:S,addTax:k,checkTax:$,calcAmount:m,changePayType:A,bindPaymentValue:l,submitRefund:P,closeDialog:j,changeTicketType:X,commisionRateChange:ce,ticketOrganizationList:M,isShowPrintNo:w,changeTicketManagementOrganizationCode:W}=AT(u);return(C,R)=>{const N=Zt,K=en,oe=ct,te=nt,D=vt,E=_t,pe=Rn,ue=et,ge=ut,se=Dt,de=Et,H=rt,Q=ht;return s(),re(H,{width:"1040",title:C.$t("app.agentTicketRefund.manualRefundBtn"),"show-close":!1,"close-on-click-modal":!1,class:"crs-new-ui-init-cls ticket-manual-refund-dialog","align-center":!0,onClose:e(j)},{default:c(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:R[0]||(R[0]=(...q)=>e(j)&&e(j)(...q))},ET),Ue((s(),g("div",null,[t("div",OT,[t("div",null,[t("div",FT,n(C.$t("app.agentTicketRefund.refundInformationForm")),1)]),r(ge,{ref_key:"refundFormRef",ref:d,model:e(o),"require-asterisk-position":"right"},{default:c(()=>{var q,V,B,v,G;return[t("div",VT,[t("div",MT,[t("div",LT,[t("div",jT,n(C.$t("app.agentTicketRefund.refundTicketNumber")),1),BT]),t("div",IT,[r(oe,{label:C.$t("app.refundForm.ticketType"),prop:"tktType",rules:e(_).tktType},{default:c(()=>[r(K,{modelValue:e(o).tktType,"onUpdate:modelValue":R[1]||(R[1]=L=>e(o).tktType=L),placeholder:C.$t("app.agentTicketRefund.choose"),clearable:"",onChange:e(X)},{default:c(()=>[(s(!0),g(_e,null,Re(e(CT),L=>(s(),re(N,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])]),_:1},8,["label","rules"])]),t("div",{class:Pe(["grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] ticketManagementOrganizationCode",e(Us)()==="en"?"ticket-organization-en":""])},[r(oe,{label:C.$t("app.refundForm.ticketManagementOrganizationCode"),prop:"ticketManagementOrganizationCode",rules:e(_).ticketManagementOrganizationCode},{default:c(()=>[r(K,{modelValue:e(o).ticketManagementOrganizationCode,"onUpdate:modelValue":R[2]||(R[2]=L=>e(o).ticketManagementOrganizationCode=L),disabled:!e(o).ticketManagementOrganizationCode,placeholder:e(o).ticketManagementOrganizationCode?"":C.$t("app.agentTicketQuery.noData"),onChange:e(W)},{default:c(()=>[(s(!0),g(_e,null,Re(e(M),L=>(s(),re(N,{key:L.value,label:L.label,value:L.value},{default:c(()=>[t("span",null,[e(o).ticketManagementOrganizationCode===L.value?(s(),re(te,{key:0,size:12,class:"iconfont icon-right-line"})):(s(),g("span",UT))]),J(" "+n(L.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["label","rules"])],2),t("div",QT,[e(w)?(s(),re(oe,{key:0,label:C.$t("app.agentTicketRefund.prntNo"),prop:"printNo",rules:e(_).printNo},{default:c(()=>[r(Kt,{modelValue:e(o).printNo,"onUpdate:modelValue":[R[3]||(R[3]=L=>e(o).printNo=L),R[4]||(R[4]=L=>e(d).validateField("printNo"))],"need-distinguish":!1,"select-class":"w-[150px]"},null,8,["modelValue"])]),_:1},8,["label","rules"])):(s(),g("span",qT,[t("span",zT,n(C.$t("app.agentTicketRefund.prntNo")),1),GT]))])]),t("div",HT,[t("div",YT,[t("div",KT,n(C.$t("app.agentTicketRefund.refundAgent")),1),t("div",WT,n(((q=e(o))==null?void 0:q.agent)??"-"),1)]),t("div",XT,[t("div",JT,n(C.$t("app.agentTicketRefund.refundIataNo")),1),t("div",ZT,n(((V=e(o))==null?void 0:V.iata)??"-"),1)]),t("div",e0,[t("div",t0,n(C.$t("app.agentTicketRefund.refundOffice")),1),t("div",n0,n(((B=e(o))==null?void 0:B.office)??"-"),1)]),t("div",a0,[t("div",s0,n(C.$t("app.agentTicketRefund.refundDate")),1),t("div",o0,n(((v=e(o))==null?void 0:v.refundDate)??"-"),1)])])]),t("div",i0,[t("div",l0,[t("div",r0,[r(oe,{label:C.$t("app.agentTicketRefund.refundAirlineSettlementCode"),class:"w90",prop:"airline",rules:e(_).airline},{default:c(()=>[r(D,{modelValue:e(o).airline,"onUpdate:modelValue":R[5]||(R[5]=L=>e(o).airline=L),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",c0,[r(oe,{label:C.$t("app.agentTicketRefund.refundTicketNo"),prop:"ticketNo",rules:e(_).ticketNo},{default:c(()=>[r(D,{modelValue:e(o).ticketNo,"onUpdate:modelValue":R[6]||(R[6]=L=>e(o).ticketNo=L),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"]),r(E,{placement:"top",content:C.$t("app.agentTicketRefund.internationalTicketNoTips"),"popper-class":"w-[255px]"},{default:c(()=>[u0]),_:1},8,["content"])]),t("div",d0,[r(oe,{label:C.$t("app.agentTicketRefund.numberOfCombinedTickets"),prop:"conjunction",rules:e(_).conjunction},{default:c(()=>[r(D,{modelValue:e(o).conjunction,"onUpdate:modelValue":R[7]||(R[7]=L=>e(o).conjunction=L),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label","rules"])]),p0]),t("div",f0,[t("div",m0,[r(oe,{label:C.$t("app.agentTicketRefund.passName"),prop:"name",class:"w90",rules:e(_).psdName},{default:c(()=>[r(D,{modelValue:e(o).name,"onUpdate:modelValue":R[8]||(R[8]=L=>e(o).name=L),clearable:"",onInput:R[9]||(R[9]=L=>e(o).name=e(o).name.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",g0,[t("div",k0,n(C.$t("app.agentTicketRefund.refundSeg")),1),(s(!0),g(_e,null,Re(e(o).segment,(L,ae)=>(s(),re(oe,{key:ae,prop:"segment."+ae,error:e(y)[ae],class:"mr10"},{default:c(()=>[r(D,{modelValue:e(o).segment[ae],"onUpdate:modelValue":ie=>e(o).segment[ae]=ie,modelModifiers:{trim:!0},clearable:"",onBlur:e(S)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["prop","error"]))),128))])]),t("div",y0,[t("div",v0,[r(oe,{label:C.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",class:Pe(["w90",{"not-required-container":!e(o).totalAmount}]),rules:e(_).totalAmount},{default:c(()=>[r(D,{modelValue:e(o).totalAmount,"onUpdate:modelValue":R[10]||(R[10]=L=>e(o).totalAmount=L),modelModifiers:{trim:!0},clearable:"",onBlur:R[11]||(R[11]=L=>e(m)("totalAmount"))},null,8,["modelValue"]),e(o).totalAmount?ee("",!0):(s(),g("div",h0,n(C.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),t("div",_0,[r(oe,{label:C.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:e(_).payType},{default:c(()=>[r(K,{modelValue:e(o).payType,"onUpdate:modelValue":R[12]||(R[12]=L=>e(o).payType=L),modelModifiers:{trim:!0},filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:C.$t("app.agentTicketRefund.choose"),clearable:"",onChange:e(A),onBlur:e(l)},{default:c(()=>[(s(!0),g(_e,null,Re(e(is),(L,ae)=>(s(),re(N,{key:ae,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),t("div",b0,[r(oe,{label:C.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:e(_).currency},{default:c(()=>[r(D,{modelValue:e(o).currency,"onUpdate:modelValue":R[13]||(R[13]=L=>e(o).currency=L),modelModifiers:{trim:!0},clearable:"",onInput:R[14]||(R[14]=L=>e(o).currency=e(o).currency.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label","rules"])]),x0])]),t("div",T0,[t("div",N0,[t("div",$0,[r(oe,{label:C.$t("app.agentTicketRefund.etTag")},{default:c(()=>[r(pe,{modelValue:e(o).etTag,"onUpdate:modelValue":R[15]||(R[15]=L=>e(o).etTag=L),"inline-prompt":"","active-text":"Y","inactive-text":"N","active-value":"1","inactive-value":"0"},null,8,["modelValue"])]),_:1},8,["label"])])]),t("div",R0,[t("div",C0,[t("div",null,[t("span",null,n(C.$t("app.agentTicketRefund.refundTax")),1),t("span",w0,n(C.$t("app.fare.singleFare.totalTax")),1),t("span",S0," "+n(e(o).currency)+" "+n(e(o).totalTaxs),1)]),t("div",null,[r(ue,{link:"",type:"primary",size:"small",disabled:e(T),onClick:e(b)},{default:c(()=>[J(n(C.$t("app.agentTicketRefund.rtktTax")),1)]),_:1},8,["disabled","onClick"]),r(ue,{link:"",type:"primary",size:"small",disabled:((G=e(o).taxs)==null?void 0:G.length)===e(h),onClick:e(k)},{default:c(()=>[J(n(C.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"])])]),t("div",P0,[(s(!0),g(_e,null,Re(e(o).taxs,(L,ae)=>(s(),g("div",{key:ae,class:"grow shrink-0 basis-0 h-[32px] justify-start flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[t("div",A0,n(ae+1),1),t("div",D0,[r(oe,{prop:"taxs."+ae+".name",rules:e(_).taxName},{default:c(()=>[r(D,{modelValue:L.name,"onUpdate:modelValue":ie=>L.name=ie,modelModifiers:{trim:!0},onInput:ie=>L.name=L.name.toUpperCase(),onBlur:e($)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"])]),_:2},1032,["prop","rules"])]),r(oe,{prop:"taxs."+ae+".value",rules:e(_).taxValue},{default:c(()=>[r(D,{modelValue:L.value,"onUpdate:modelValue":ie=>L.value=ie,modelModifiers:{trim:!0},clearable:"",onBlur:e($)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["prop","rules"])]))),128))])])]),t("div",E0,[t("div",O0,[t("div",F0,[r(oe,{label:C.$t("app.agentTicketRefund.commision"),prop:"commision",class:"w90",rules:e(_).commision},{default:c(()=>[r(D,{modelValue:e(o).commision,"onUpdate:modelValue":R[16]||(R[16]=L=>e(o).commision=L),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:R[17]||(R[17]=L=>e(m)("commision"))},null,8,["modelValue"])]),_:1},8,["label","rules"])]),t("div",V0,[r(oe,{label:C.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(_).commisionRate},{default:c(()=>[r(D,{modelValue:e(o).commisionRate,"onUpdate:modelValue":R[18]||(R[18]=L=>e(o).commisionRate=L),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:R[19]||(R[19]=L=>e(m)("commisionRate")),onInput:e(ce)},null,8,["modelValue","onInput"]),M0]),_:1},8,["label","rules"])]),t("div",L0,[r(oe,{label:C.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",class:"w170",rules:e(_).otherDeductionRate},{default:c(()=>[r(D,{modelValue:e(o).otherDeductionRate,"onUpdate:modelValue":R[20]||(R[20]=L=>e(o).otherDeductionRate=L),modelModifiers:{trim:!0},placeholder:"1-100",clearable:"",onBlur:R[21]||(R[21]=L=>e(m)("otherDeductionRate"))},null,8,["modelValue"]),j0]),_:1},8,["label","rules"])]),t("div",B0,[r(oe,{label:C.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",class:"w110",rules:e(_).otherDeduction},{default:c(()=>[r(D,{modelValue:e(o).otherDeduction,"onUpdate:modelValue":R[22]||(R[22]=L=>e(o).otherDeduction=L),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",onBlur:R[23]||(R[23]=L=>e(m)("otherDeduction"))},null,8,["modelValue"])]),_:1},8,["label","rules"])])]),t("div",I0,[t("div",U0,[t("div",Q0,n(C.$t("app.agentTicketRefund.remark")),1),t("div",q0,[r(oe,{prop:"remarkInfo",rules:e(_).remarkInfo},{default:c(()=>[r(D,{modelValue:e(o).remarkInfo,"onUpdate:modelValue":R[24]||(R[24]=L=>e(o).remarkInfo=L),clearable:"",placeholder:C.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:R[25]||(R[25]=L=>e(o).remarkInfo=e(o).remarkInfo.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["rules"]),r(E,{placement:"top",content:C.$t("app.agentTicketRefund.remarkTips")},{default:c(()=>[z0]),_:1},8,["content"])])]),t("div",G0,[r(oe,{label:C.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund"},{default:c(()=>[t("span",H0,n(e(o).currency)+" "+n(e(o).netRefund),1),r(E,{placement:"top",effect:"dark"},{content:c(()=>[J(n(C.$t("app.agentTicketRefund.netRefundTip")),1)]),default:c(()=>[Y0]),_:1})]),_:1},8,["label"])]),t("div",K0,[r(oe,{label:C.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:e(_).creditCard},{default:c(()=>[r(D,{modelValue:e(o).creditCard,"onUpdate:modelValue":R[26]||(R[26]=L=>e(o).creditCard=L),modelModifiers:{trim:!0},clearable:"",onInput:R[27]||(R[27]=L=>e(o).creditCard=e(o).creditCard.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label","rules"])]),W0])])]}),_:1},8,["model"])]),t("div",X0,[r(de,{modelValue:e(p),"onUpdate:modelValue":R[28]||(R[28]=q=>Me(p)?p.value=q:null),class:"mr-[10px]"},{default:c(()=>[r(se,{disabled:"",label:"ONLY_REFUND"},{default:c(()=>[J(n(C.$t("app.agentTicketRefund.onlyRt")),1)]),_:1})]),_:1},8,["modelValue"]),r(ue,{type:"primary",onClick:e(P)},{default:c(()=>[J(n(C.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["onClick"])])])),[[Q,e(f)]])]),_:1},8,["title","onClose"])}}});const Z0=async(a,i)=>{const u=a.cloneNode(!0),p=document.createElement("div");p.style.cssText=`
    position: fixed;
    top: 0;
    left: 0;
    width: ${a.offsetWidth}px;
    height: ${a.offsetHeight+100}px;
    overflow: visible;
    z-index: -9999;
  `,p.appendChild(u),document.body.appendChild(p);try{const f=await Qs(u,{useCORS:!0,scale:3,windowWidth:u.scrollWidth,windowHeight:u.scrollHeight,backgroundColor:"#FFFFFF",allowTaint:!0,logging:!0}),d=new co("p","pt",[1500,1500]);d.addImage(f,"PNG",0,0,1500,1500),d.save(i)}finally{document.body.removeChild(p)}},eN=a=>{const{t:i}=Ze(),u=I(),p=it({printNo:"",airlineSettlementCode:"",startTicketNo:"",endTicketNo:""}),f=I(),d=I(!1),o=I(!1),_=I({}),h={printNo:[{required:!0,message:i("app.cccf.required"),trigger:"blur"},{pattern:Ht,message:i("app.cccf.printNoTip"),trigger:"blur"}],airlineSettlementCode:[{required:!0,message:i("app.cccf.required"),trigger:"blur"},{pattern:Va,message:i("app.cccf.airlineSettlementCodeTip"),trigger:"blur"}],startTicketNo:[{required:!0,message:i("app.cccf.required"),trigger:"blur"},{pattern:ao,message:i("app.cccf.startTicketNoTip"),trigger:"blur"}],endTicketNo:[{pattern:so,message:i("app.cccf.endTicketNoTip"),trigger:"blur"}]},y=m=>({beginNumber:m.startTicketNo,endNumber:m.endTicketNo,airlineNumber:m.airlineSettlementCode,deviceNumber:m.printNo});return{cccfFromRef:u,cccfFromData:p,rules:h,creditCardReceiptData:_,printRef:f,printStyleControl:d,downloadPdfStyleControl:o,handleQuery:async()=>{var m;(m=u==null?void 0:u.value)==null||m.validate(async A=>{var P;if(!A)return;const l=Mt.service({fullscreen:!0});try{const j=we("091U0106"),X=await So(y(p),j);_.value=((P=X==null?void 0:X.data)==null?void 0:P.value)??{}}finally{l.close()}})},handleReset:()=>{p.printNo="",p.airlineSettlementCode="",p.startTicketNo="",p.endTicketNo=""},handlePrint:async()=>{d.value=!0,await cn(),await Uo(f.value,{paging:!0,style:`<style>
        .print-refund-form-panel {
          width: 1050px
        }
      </style>`}),d.value=!1},handlePdf:async()=>{o.value=!0,await cn(),await Z0(f.value,`${p.airlineSettlementCode}-${p.startTicketNo}-${p.endTicketNo}`),o.value=!1},cancel:()=>{a("update:modelValue",!1)}}},tN=eN,nN=t("i",{class:"iconfont icon-close"},null,-1),aN=[nN],sN={class:"border-separate border-spacing-0 border-gray-2 w-full"},oN={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},iN=t("span",{class:"font-bold"},"PASSENGER NAME : ",-1),lN=t("td",{class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},[t("span",null,"-")],-1),rN={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},cN=t("span",{class:"font-bold"},"COUNTRY OF SELL : ",-1),uN={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},dN=t("span",{class:"font-bold"},"ORIGIN/DESTINATION : ",-1),pN={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},fN=t("span",{class:"font-bold"},"BOOKING : ",-1),mN=t("span",null,"-",-1),gN={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},kN=t("span",null,"TIME DATE AND PLACE OF ISSUE ",-1),yN={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},vN=t("span",{class:"font-bold"},"TICKET # : ",-1),hN={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},_N={class:"flex"},bN={class:"w-[80px]"},xN={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},TN=t("span",{class:"font-bold"},"IATA # : ",-1),NN={class:"w-1/2 p-0 border-t border-l border-r border-gray-2"},$N={class:"flex h-full"},RN={class:"w-1/2 border-r border-gray-2 p-1.5"},CN={class:"w-1/2 p-1.5"},wN=t("span",{class:"font-bold"},"AGENT : ",-1),SN={class:"h-full"},PN={class:"w-full h-full p-0 border border-gray-2",colspan:"2"},AN={class:"flex h-full"},DN={class:"w-[26%] h-full p-1.5 border-r border-gray-2 flex flex-col"},EN=t("div",{class:"font-bold"},"ITINERARY",-1),ON={key:0,class:"flex"},FN=t("div",{class:"w-[50px]"},"FORM",-1),VN={class:"flex"},MN=t("div",{class:"w-[50px]"},"TO",-1),LN={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},jN=t("div",{class:"font-bold"},"CARRIER",-1),BN={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},IN=t("div",{class:"font-bold"},"FLIGHT",-1),UN=t("div",null,"VOID",-1),QN={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},qN=t("div",{class:"font-bold"},"CLASS",-1),zN={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},GN=t("div",{class:"font-bold"},"DATE",-1),HN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},YN=t("div",{class:"font-bold"},"STATUS",-1),KN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},WN=t("div",{class:"font-bold"},"FARE",-1),XN=t("div",null,"-",-1),JN=[XN],ZN={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},e$=t("div",{class:"font-bold"},"BASIS/TKT",-1),t$={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},n$=t("div",{class:"font-bold"},"DESIGNATOR",-1),a$=t("div",null,"-",-1),s$=[a$],o$={class:"w-1/5 p-1.5 flex flex-col"},i$=t("div",{class:"font-bold"},"STOPOVER",-1),l$={class:"border-separate border-spacing-0 border-gray-2 w-full mt-2.5"},r$={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},c$=t("div",{class:"font-bold"},"CARD HOLDER",-1),u$={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},d$=t("div",{class:"font-bold"},"CREDIT CARD CODE",-1),p$=t("td",{class:"w-1/3 border-t border-l border-r border-gray-2 p-1.5"},[t("div",{class:"font-bold"},"BANK SEQUENCE"),t("div",null,"-")],-1),f$={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},m$=t("div",{class:"font-bold"},"EXPIRE DATE",-1),g$={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},k$=t("div",{class:"font-bold"},"APPROVAL CODE",-1),y$={class:"w-1/3 border-t border-l border-r border-gray-2 p-1.5"},v$=t("div",{class:"font-bold"},"AMOUNT",-1),h$=t("tr",null,[t("td",{class:"w-full border border-gray-2 p-1.5",colspan:"3"},[t("div",{class:"font-bold"},"CARDHOLDER SIGNATURE : ")])],-1),_$={key:1,class:"flex justify-center flex-col items-center min-h-[400px]"},b$=["alt"],x$={class:"mt-[20px] text-lg leading-[24px] font-bold text-gray-2"},T$={class:"text-base text-gray-4"},N$={key:2,class:"flex justify-center crs-btn-dialog-ui"},$$=Le({__name:"CreditCardReceiptPrintDialog",emits:["update:modelValue"],setup(a,{emit:i}){const u=i,{cccfFromRef:p,cccfFromData:f,rules:d,creditCardReceiptData:o,printRef:_,printStyleControl:h,downloadPdfStyleControl:y,handleQuery:T,handleReset:b,handlePrint:S,handlePdf:k,cancel:$}=tN(u);return(m,A)=>{const l=ct,P=vt,j=et,X=ut,ce=rt;return s(),re(ce,{title:`${m.$t("app.cccf.creditCardReceiptPrint")}CCCF`,"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,"align-center":"true",class:"cccf-dialog crs-new-ui-init-cls",width:"1040px",onClose:e($)},{default:c(()=>{var M,w,W,C,R,N,K,oe;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:A[0]||(A[0]=(...te)=>e($)&&e($)(...te))},aN),t("div",null,[t("div",null,[r(X,{ref_key:"cccfFromRef",ref:p,rules:e(d),model:e(f),"require-asterisk-position":"right",class:"flex cccf-from"},{default:c(()=>[r(l,{label:m.$t("app.cccf.printNo"),prop:"printNo",class:"mr-2.5"},{default:c(()=>[r(Kt,{modelValue:e(f).printNo,"onUpdate:modelValue":[A[1]||(A[1]=te=>e(f).printNo=te),A[2]||(A[2]=te=>{var D;return(D=e(p))==null?void 0:D.validateField("printNo")})],modelModifiers:{trim:!0},"select-class":"w-[100px]"},null,8,["modelValue"])]),_:1},8,["label"]),r(l,{label:m.$t("app.cccf.airlineSettlementCode"),prop:"airlineSettlementCode",class:"mr-2.5"},{default:c(()=>[r(P,{modelValue:e(f).airlineSettlementCode,"onUpdate:modelValue":A[3]||(A[3]=te=>e(f).airlineSettlementCode=te),modelModifiers:{trim:!0},class:"airline-settlement-code"},null,8,["modelValue"])]),_:1},8,["label"]),r(l,{label:m.$t("app.cccf.startTicketNo"),prop:"startTicketNo",class:"mr-2.5"},{default:c(()=>[r(P,{modelValue:e(f).startTicketNo,"onUpdate:modelValue":A[4]||(A[4]=te=>e(f).startTicketNo=te),modelModifiers:{trim:!0},class:"start-ticket-no"},null,8,["modelValue"])]),_:1},8,["label"]),r(l,{label:m.$t("app.cccf.endTicketNo"),prop:"endTicketNo",class:"mr-2.5"},{default:c(()=>[r(P,{modelValue:e(f).endTicketNo,"onUpdate:modelValue":A[5]||(A[5]=te=>e(f).endTicketNo=te),modelModifiers:{trim:!0},class:"end-ticket-no"},null,8,["modelValue"])]),_:1},8,["label"]),r(j,{type:"primary","data-gid":"091U0106",onClick:A[6]||(A[6]=te=>e(T)())},{default:c(()=>[J(n(m.$t("app.cccf.query")),1)]),_:1}),r(j,{onClick:A[7]||(A[7]=te=>e(b)())},{default:c(()=>[J(n(m.$t("app.cccf.reset")),1)]),_:1})]),_:1},8,["rules","model"])]),(M=e(o))!=null&&M.pnr?(s(),g("div",{key:0,ref_key:"printRef",ref:_,class:Pe(["text-gray-2 min-h-[400px] w-[1008px]",e(y)?"p-[20px]":""])},[t("table",sN,[t("tbody",null,[t("tr",null,[t("td",oN,[iN,t("span",null,n(e(o).fullName||"-"),1)]),lN]),t("tr",null,[t("td",rN,[cN,t("span",null,n(e(o).countryCode),1)]),t("td",uN,[dN,t("span",null,n(e(o).orgArrivalCityCode),1)])]),t("tr",null,[t("td",pN,[fN,t("span",null,n(e(o).pnr||"-"),1),mN]),t("td",gN,[kN,t("span",null,n(e(o).ticketIssueTime||"-"),1)])]),t("tr",null,[t("td",yN,[vN,t("span",null,n(e(o).bspCurrencyCode||"-"),1)]),t("td",hN,[t("div",_N,[t("div",bN,n(e(o).ticketIssueDate||"-"),1),J(n(e(o).locationSubTypeCode||"-"),1)])])]),t("tr",null,[t("td",xN,[TN,t("span",null,n(e(o).agentIataNumber||"-"),1)]),t("td",NN,[t("div",$N,[t("div",RN,[t("span",null,n(e(o).issueOfficeId||"-"),1),J(" DEV-"),t("span",null,n(e(o).deviceNumber||"-"),1)]),t("div",CN,[wN,t("span",null,n(e(o).issueAgentId||"-"),1)])])])]),t("tr",SN,[t("td",PN,[t("div",AN,[t("div",DN,[EN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[D===0?(s(),g("div",ON,[FN,J(n(te.originCityNumericCode||"-"),1)])):ee("",!0),t("div",VN,[MN,J(n(te.destinationCityCode||"-"),1)])]))),128))]),t("div",LN,[jN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.ocAirlineCode||"-"),1)]))),128))]),t("div",BN,[IN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.ocFlightNumber||"-"),1)]))),128)),UN]),t("div",QN,[qN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.ocClassId||"-"),1)]))),128))]),t("div",zN,[GN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.departureDate||"-"),1)]))),128))]),t("div",HN,[YN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.reservationStatusCode||"-"),1)]))),128))]),t("div",KN,[WN,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},JN))),128))]),t("div",ZN,[e$,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.seatValueLevel||"-"),1)]))),128))]),t("div",t$,[n$,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},s$))),128))]),t("div",o$,[i$,(s(!0),g(_e,null,Re(e(o).itineraryInfoList,(te,D)=>(s(),g("div",{key:D},[t("div",null,n(te.stopoverCode||"-"),1)]))),128))])])])])])]),t("table",l$,[t("tbody",null,[t("tr",null,[t("td",r$,[c$,t("div",null,n(((w=e(o).paymentInfo)==null?void 0:w.creditCardNumber)||"-"),1)]),t("td",u$,[d$,t("div",null,n(((W=e(o).paymentInfo)==null?void 0:W.operatingCompany)||"-"),1)]),p$]),t("tr",null,[t("td",f$,[m$,t("div",null,n(((C=e(o).paymentInfo)==null?void 0:C.endDate)||"-"),1)]),t("td",g$,[k$,t("div",null,n(((R=e(o).paymentInfo)==null?void 0:R.checkCode)||"-"),1)]),t("td",y$,[v$,t("div",null,[J(n(((N=e(o).paymentInfo)==null?void 0:N.paymentAmount)||"-")+" ",1),t("span",null,n(((K=e(o).paymentInfo)==null?void 0:K.currencyCode)||"-"),1)])])]),h$])]),t("div",{class:Pe(["mt-2.5",e(h)?"":"text-xs"])},"CARDHOLDER ACKNOWLEDGES RECEIPT OF GOODS AND/OR SERVICE IN THE AMOUNT OF THE TOTAL SHOWN HEREON AND AGRESS TO PERFORM THE OBLIGATIONS SET FORTH IN THE CARDHOLDER' S AGREEMENT WITH THE ISSUER.",2)],2)):(s(),g("div",_$,[t("img",{src:ra,alt:m.$t("app.fastQuery.skQuerys.nodata")},null,8,b$),t("div",x$,n(m.$t("app.commonProblems.noQueryData")),1),t("div",T$,n(m.$t("app.commonProblems.enterCriteriaQuery")),1)])),(oe=e(o))!=null&&oe.pnr?(s(),g("div",N$,[r(j,{type:"primary",onClick:A[8]||(A[8]=te=>e(k)())},{default:c(()=>[J(n(m.$t("app.cccf.download")),1)]),_:1}),r(j,{type:"primary",onClick:A[9]||(A[9]=te=>e(S)())},{default:c(()=>[J(n(m.$t("app.cccf.print")),1)]),_:1}),r(j,{onClick:e($)},{default:c(()=>[J(n(m.$t("app.cccf.close")),1)]),_:1},8,["onClick"])])):ee("",!0)])]}),_:1},8,["title","onClose"])}}});const R$={class:"ticket-operation-container crs-new-ui-init-cls"},C$={class:"bg-gray-0 rounded-lg shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},w$={class:"bg-gray-8 h-[50px] flex"},S$=["onClick"],CR=Le({__name:"TicketOperationContainer",setup(a){const{loading:i,printNo:u,printType:p,showRefundFormDialog:f,refundOperationCondition:d,refundFormData:o,currentTab:_,editableTabs:h,refundEtNumber:y,queryTicketRes:T,ticketQueryConditionRef:b,showBatchRefund:S,showManualRefund:k,showAuthOffice:$,queryType:m,batchRefundRes:A,batchRefundRef:l,factor:P,showBopRefund:j,showRtktDialog:X,showCccfDialog:ce,addTab:M,changeTab:w,removeTab:W,handleQueryTicket:C,reQueryTicket:R,openBatchRefund:N,handleBatchRefund:K,openBopRefund:oe,bopRefundSuccess:te,openManualRefund:D,openAuthOffice:E,deliverRefundData:pe,openRefundDialog:ue,openRtkt:ge,openCccf:se}=ab();return(de,H)=>{var V,B;const Q=nt,q=ht;return Ue((s(),g("div",null,[t("div",R$,[t("div",C$,[t("div",w$,[(s(!0),g(_e,null,Re(e(h),(v,G)=>{var L,ae;return s(),g("div",{key:v.name,class:Pe([G===0?"p-[14px]":"p-[10px] pr-[0px]",e(_)===(((ae=(L=e(h))==null?void 0:L[0])==null?void 0:ae.name)??"ticketQuery")&&G===0?"bg-gray-0":"","h-full cursor-pointer text-[14px]"]),onClick:ie=>e(w)(G)},[t("span",{class:Pe([G===0?"":"rounded-[4px] px-[10px] h-[32px] border-[1px] border-solid",G!==0?e(_)===v.name?"border-brand-2 bg-brand-4":"border-gray-6 bg-gray-0":"","flex items-center text-gray-2"])},[t("span",{class:Pe([e(_)===v.name?"text-brand-2 font-[700]":""])},n(v.title),3),G!==0?(s(),re(Q,{key:0,class:"ml-[6px]",onClick:jn(ie=>e(W)(G,v.name),["stop"])},{default:c(()=>[r(e(qs))]),_:2},1032,["onClick"])):ee("",!0)],2)],10,S$)}),128))]),Ue(t("div",null,[r(Ai,{ref_key:"ticketQueryConditionRef",ref:b,onHandleQueryTicket:e(C),onAddNewTab:e(M),onOpenAuthOffice:e(E),onOpenBatchRefund:e(N),onOpenBopRefund:e(oe),onOpenRefundDialog:e(ue),onOpenManualRefund:e(D),onOpenRtkt:e(ge),onOpenCccf:e(se)},null,8,["onHandleQueryTicket","onAddNewTab","onOpenAuthOffice","onOpenBatchRefund","onOpenBopRefund","onOpenRefundDialog","onOpenManualRefund","onOpenRtkt","onOpenCccf"])],512),[[kn,e(_)===(((B=(V=e(h))==null?void 0:V[0])==null?void 0:B.name)??"ticketQuery")]])]),(s(!0),g(_e,null,Re(e(h),v=>Ue((s(),g("div",{key:v.name},[(s(),re(zs(v.content),{key:v.name,"query-ticket-res":e(T),"tkt-no":e(y),"batch-refund-res":e(A),"query-type":e(m),factor:e(P),onAddNewTab:e(M),onRemoveTab:e(W),onReQueryTicket:e(R)},null,40,["query-ticket-res","tkt-no","batch-refund-res","query-type","factor","onAddNewTab","onRemoveTab","onReQueryTicket"]))])),[[kn,e(_)===v.name]])),128))]),e(j)?(s(),re(px,{key:0,modelValue:e(j),"onUpdate:modelValue":H[0]||(H[0]=v=>Me(j)?j.value=v:null),onBopRefundSuccess:e(te)},null,8,["modelValue","onBopRefundSuccess"])):ee("",!0),e(S)?(s(),re(nx,{key:1,ref_key:"batchRefundRef",ref:l,modelValue:e(S),"onUpdate:modelValue":H[1]||(H[1]=v=>Me(S)?S.value=v:null),onHandleBatchRefund:e(K)},null,8,["modelValue","onHandleBatchRefund"])):ee("",!0),e($)?(s(),re(hT,{key:2,modelValue:e($),"onUpdate:modelValue":H[2]||(H[2]=v=>Me($)?$.value=v:null)},null,8,["modelValue"])):ee("",!0),e(k)?(s(),re(J0,{key:3,modelValue:e(k),"onUpdate:modelValue":H[3]||(H[3]=v=>Me(k)?k.value=v:null),onDeliverRefundData:e(pe)},null,8,["modelValue","onDeliverRefundData"])):ee("",!0),e(f)?(s(),re(aa,{key:4,modelValue:e(f),"onUpdate:modelValue":H[4]||(H[4]=v=>Me(f)?f.value=v:null),"printer-no":e(u),"printer-type":e(p),"is-supplement-refund":!1,"refund-operation-condition":e(d),"refund-ticket-data":e(o),onReQueryTicket:e(R)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data","onReQueryTicket"])):ee("",!0),e(X)?(s(),re(RT,{key:5,modelValue:e(X),"onUpdate:modelValue":H[5]||(H[5]=v=>Me(X)?X.value=v:null)},null,8,["modelValue"])):ee("",!0),e(ce)?(s(),re($$,{key:6,modelValue:e(ce),"onUpdate:modelValue":H[6]||(H[6]=v=>Me(ce)?ce.value=v:null)},null,8,["modelValue"])):ee("",!0)])),[[q,e(i),void 0,{fullscreen:!0,lock:!0}]])}}});export{CR as default};
