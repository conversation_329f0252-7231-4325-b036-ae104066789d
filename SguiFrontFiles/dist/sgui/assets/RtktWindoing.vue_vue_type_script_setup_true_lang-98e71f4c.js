import{ao as d,ap as n,q as Ft,w as Pt,x as g,B as y,P as t,Q as r,ai as Ct,aj as St,ak as Et,bq as Bt,am as Ot,an as Mt}from"./index-9381ab2b.js";import{_ as Qt}from"./_plugin-vue_export-helper-c27b6911.js";import{u as Wt}from"./TicketOriginalPopover.vue_vue_type_script_setup_true_lang-bce4521f.js";const os=(s,o)=>d(`${n}/apiRefundTicket/findRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),is=(s,o)=>d(`${n}/apiRefundTicket/findRefundFee`,{headers:{gid:o}},{originalValue:!0,ignoreError:!0}).post(s).json(),ds=(s,o)=>d(`${n}/apiRefundTicket/autoRefund`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),ns=(s,o)=>d(`${n}/apiRefundTicket/manualRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),ps=(s,o)=>d(`${n}/apiRefundTicket/manualRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),ls=(s,o)=>d(`${n}/apiRefundTicket/batchManualRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),xs=(s,o)=>d(`${n}/pnrManager/deletePnr`,{headers:{gid:o}}).post({pnrNo:s}).json(),cs=(s,o)=>d(`${n}/apiRefundTicket/previewRefundTicket`,{headers:{gid:o}}).post(s).json(),gs=(s,o)=>d(`${n}/crs/involuntary/queryPnrMessage`,{headers:{gid:o}},{ignoreError:!0}).post(s).json(),ys=(s,o)=>d(`${n}/pnrManager/deletePnrAndDeleteInfantInfo`,{headers:{gid:o}}).post(s).json(),bs=(s,o)=>d(`${n}/apiRefundTicket/queryRtktDetail`,{headers:{gid:o}},{ignoreError:!0}).post(s).json(),fs=(s,o)=>d(`${n}/apiRefundTicket/batchFindRefundFee`,{headers:{gid:o}}).post(s).json(),us=(s,o)=>d(`${n}/apiRefundTicket/checkPassengerInPnr`,{headers:{gid:o}}).post(s).json(),f=s=>(Ot("data-v-ba04ec3d"),s=s(),Mt(),s),Lt={class:"w-full h-full overflow-auto"},Kt={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},zt={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Ut={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Gt={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ht={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Jt={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Xt={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Yt={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Zt={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},te={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ee={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},re=f(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"OFFICE：",-1)),se={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ae={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},oe={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ie={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},de={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},ne={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},pe={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},le={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},xe={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ce={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ge={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},ye={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},be={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},fe=Bt('<div class="flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2" data-v-ba04ec3d><div class="text-gray-2 text-[12px] font-bold leading-[20px]" data-v-ba04ec3d></div><div class="text-gray-2 text-[12px] font-normal leading-[20px]" data-v-ba04ec3d></div></div><div class="flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2" data-v-ba04ec3d><div class="text-gray-2 text-[12px] font-bold leading-[20px]" data-v-ba04ec3d></div><div class="text-gray-2 text-[12px] font-normal leading-[20px]" data-v-ba04ec3d></div></div>',2),ue={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},_e={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ke={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},he={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},ve={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},me={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Re={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},De={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ie={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},$e={class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},je={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},qe={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Te={class:"grid grid-cols-[28%_22%_28%_22%] auto-rows-[minmax(32px,auto)]"},Ae={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},we={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ne={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ce={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Fe={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ve={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Pe={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Se={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ee={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Be={class:"flex justify-start items-center flex-wrap row-span-2 bg-gray-0 border border-b-0 border-gray-2"},Oe={class:"flex justify-start items-center h-[32px] px-[6px] py-[4px] w-full"},Me={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Qe={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},We={class:"flex justify-start items-center h-[32px] px-[6px] py-[4px]"},Le={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ke={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ze={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Ue={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ge={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},He={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Je={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Xe={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ye={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Ze={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},tr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},er={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},rr={class:"col-span-1 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},sr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ar={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},or={class:"col-span-3 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},ir={class:"text-gray-2 text-[12px] font-bold leading-[20px] break-keep"},dr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},nr={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},pr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},lr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},xr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},cr={class:"col-span-3 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},gr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},yr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},br={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},fr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ur={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},_r={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},kr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},hr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},vr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},mr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Rr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Dr={class:"flex justify-start items-center p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2"},Ir={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},$r={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},jr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},qr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Tr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ar={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},wr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Nr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Cr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0 border-b-0"},Fr={class:"text-gray-2 text-[12px] font-bold leading-[20px] whitespace-pre-wrap"},Vr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Pr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Sr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Er={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Br={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0"},Or=f(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"FP：",-1)),Mr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Qr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0"},Wr=f(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"TC：",-1)),Lr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Kr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2"},zr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ur={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Gr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-t-0 border-gray-2"},Hr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Jr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Xr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-t-0 border-gray-2"},Yr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Zr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ts=Ft({__name:"RTKTTable",props:{rtktDetailedInfo:{}},setup(s){const o=s,l=Pt(()=>{var x,c;const e=((c=(x=o.rtktDetailedInfo)==null?void 0:x.price)==null?void 0:c.taxes)||[];if(e.length){const p=e.filter(i=>i.newOldRefundTax==="R");return p.length?p.map(i=>`${i.taxCode}:${i.currencyCode} ${i.taxAmount}`).join(" / "):"-"}return"-"});return(e,x)=>{var c,p,i,b,u,_,k,h,v,m,R,D,I,$,j,q,T,A,w,N,C,F,V,P,S,E,B,O,M,Q,W,L,K,z,U,G,H,J,X,Y,Z,tt,et,rt,st,at,ot,it,dt,nt,pt,lt,xt,ct,gt,yt,bt,ft,ut,_t,kt,ht,vt,mt,Rt,Dt,It,$t,jt,qt,Tt,At,wt,Nt;return g(),y("div",Lt,[t("div",Kt,[t("div",zt,[t("div",Ut,r(e.$t("app.queryRtkt.issueAirline")),1),t("div",Gt,r(((p=(c=e.rtktDetailedInfo)==null?void 0:c.ticket)==null?void 0:p.issueAirline)||"-"),1)]),t("div",Ht,[t("div",Jt,r(e.$t("app.queryRtkt.issueMethod")),1),t("div",Xt,r(((b=(i=e.rtktDetailedInfo)==null?void 0:i.ticket)==null?void 0:b.issueType)||"-"),1)]),t("div",Yt,[t("div",Zt,r(e.$t("app.queryRtkt.asa")),1),t("div",te,r(((_=(u=e.rtktDetailedInfo)==null?void 0:u.ticket)==null?void 0:_.iataCode)||"-"),1)]),t("div",ee,[re,t("div",se,r(((h=(k=e.rtktDetailedInfo)==null?void 0:k.ticket)==null?void 0:h.office)||"-"),1)]),t("div",ae,[t("div",oe,r(e.$t("app.queryRtkt.code")),1),t("div",ie,r(((m=(v=e.rtktDetailedInfo)==null?void 0:v.ticket)==null?void 0:m.code)||"-"),1)]),t("div",de,[t("div",ne,r(e.$t("app.queryRtkt.ticketState")),1),t("div",pe,r((D=(R=e.rtktDetailedInfo)==null?void 0:R.ticket)!=null&&D.ticketState?e.$t(`app.queryRtkt.${($=(I=e.rtktDetailedInfo)==null?void 0:I.ticket)==null?void 0:$.ticketState}`):"-"),1)]),t("div",le,[t("div",xe,r(e.$t("app.queryRtkt.account")),1),t("div",ce,r(((q=(j=e.rtktDetailedInfo)==null?void 0:j.ticket)==null?void 0:q.accountNumber)||"-"),1)]),t("div",ge,[t("div",ye,r(e.$t("app.queryRtkt.issueDate")),1),t("div",be,r(((A=(T=e.rtktDetailedInfo)==null?void 0:T.ticket)==null?void 0:A.issueDate)||"-"),1)]),fe,t("div",ue,[t("div",_e,r(e.$t("app.queryRtkt.ticketNumber")),1),t("div",ke,r(((N=(w=e.rtktDetailedInfo)==null?void 0:w.ticket)==null?void 0:N.ticketNumber)||"-"),1)]),t("div",he,[t("div",ve,r(e.$t("app.queryRtkt.deviceNumber")),1),t("div",me,r((F=(C=e.rtktDetailedInfo)==null?void 0:C.ticket)!=null&&F.printNumber?`DEV-${(P=(V=e.rtktDetailedInfo)==null?void 0:V.ticket)==null?void 0:P.printNumber}`:"-"),1)]),t("div",Re,[t("div",De,r(e.$t("app.queryRtkt.refundDeviceNumber")),1),t("div",Ie,r((E=(S=e.rtktDetailedInfo)==null?void 0:S.ticket)!=null&&E.refundPrintNumber?`DEV-${(O=(B=e.rtktDetailedInfo)==null?void 0:B.ticket)==null?void 0:O.refundPrintNumber}`:"-"),1)]),t("div",$e,[t("div",je,r(e.$t("app.queryRtkt.eiItem")),1),t("div",qe,r(((Q=(M=e.rtktDetailedInfo)==null?void 0:M.ticket)==null?void 0:Q.ei)||"-"),1)])]),t("div",Te,[(g(!0),y(Ct,null,St((L=(W=e.rtktDetailedInfo)==null?void 0:W.passenger)==null?void 0:L.segments,(a,Vt)=>(g(),y(Ct,{key:Vt},[t("div",Ae,[t("div",we,r(e.$t("app.queryRtkt.originAndDestination")),1),t("div",Ne,r(a.departureCityName)+"("+r(a.departureCity)+")-"+r(a.arrivalCityName)+"("+r(a.arrivalCity)+") ",1)]),t("div",Ce,[t("div",Fe,r(e.$t("app.queryRtkt.flightNumber")),1),t("div",Ve,r(a.flightNo?a.flightNo:"-"),1)]),t("div",Pe,[t("div",Se,r(e.$t("app.queryRtkt.departureDate")),1),t("div",Ee,r(a.departureDateTime?a.departureDateTime:"-"),1)]),t("div",Be,[t("div",Oe,[t("div",Me,r(e.$t("app.queryRtkt.effectiveDate")),1),t("div",Qe,r(a.notValidBefore?a.notValidBefore:"-"),1)]),t("div",We,[t("div",Le,r(e.$t("app.queryRtkt.expiryDate")),1),t("div",Ke,r(a.notValidAfter?a.notValidAfter:"-"),1)])]),t("div",ze,[t("div",Ue,r(e.$t("app.queryRtkt.cabin")),1),t("div",Ge,r(a!=null&&a.cabin?a==null?void 0:a.cabin:"-"),1)]),t("div",He,[t("div",Je,r(e.$t("app.queryRtkt.fareBasis")),1),t("div",Xe,r(a!=null&&a.fareBasis?a==null?void 0:a.fareBasis:"-"),1)]),t("div",Ye,[t("div",Ze,r(e.$t("app.queryRtkt.baggage")),1),t("div",tr,r(a!=null&&a.baggage?a==null?void 0:a.baggage:"-"),1)])],64))),128))]),t("div",er,[t("div",rr,[t("div",sr,r(e.$t("app.queryRtkt.fareFlag")),1),t("div",ar,r((z=(K=e.rtktDetailedInfo)==null?void 0:K.price)!=null&&z.autoFareType?e.$t("app.queryRtkt.auto"):e.$t("app.queryRtkt.manual")),1)]),t("div",or,[t("div",ir,r(e.$t("app.queryRtkt.fcItem")),1),t("div",dr,r(((G=(U=e.rtktDetailedInfo)==null?void 0:U.price)==null?void 0:G.fc)||"-"),1)])]),t("div",nr,[t("div",pr,[t("div",lr,r(e.$t("app.queryRtkt.totalTax")),1),t("div",xr,r(((J=(H=e.rtktDetailedInfo)==null?void 0:H.price)==null?void 0:J.taxAmount)||"-"),1)]),t("div",cr,[t("div",gr,r(e.$t("app.queryRtkt.taxDetails")),1),t("div",yr,r(((Y=(X=e.rtktDetailedInfo)==null?void 0:X.price)==null?void 0:Y.taxDetail)||"-"),1)]),t("div",br,[t("div",fr,r(e.$t("app.queryRtkt.ticketAmountFR")),1),t("div",ur,r(((tt=(Z=e.rtktDetailedInfo)==null?void 0:Z.price)==null?void 0:tt.ticketAmountFOrR)||"-"),1)]),t("div",_r,[t("div",kr,r(e.$t("app.queryRtkt.ticketAmountE")),1),t("div",hr,r(((rt=(et=e.rtktDetailedInfo)==null?void 0:et.price)==null?void 0:rt.ticketAmount)||"-"),1)]),t("div",vr,[t("div",mr,r(e.$t("app.queryRtkt.commissionFare")),1),t("div",Rr,r(((at=(st=e.rtktDetailedInfo)==null?void 0:st.ticket)==null?void 0:at.internationalIndicator)==="D"?(it=(ot=e.rtktDetailedInfo)==null?void 0:ot.price)==null?void 0:it.commissionFare:"-"),1)]),t("div",Dr,[t("div",Ir,r(e.$t("app.queryRtkt.commissionRate")),1),t("div",$r,r(((nt=(dt=e.rtktDetailedInfo)==null?void 0:dt.ticket)==null?void 0:nt.internationalIndicator)==="I"?(lt=(pt=e.rtktDetailedInfo)==null?void 0:pt.price)==null?void 0:lt.commissionRate:"-"),1)]),t("div",jr,[t("div",qr,r(e.$t("app.queryRtkt.scny"))+"：",1),t("div",Tr,r(((ct=(xt=e.rtktDetailedInfo)==null?void 0:xt.price)==null?void 0:ct.scny)||"-"),1)]),t("div",Ar,[t("div",wr,r(e.$t("app.queryRtkt.fareAmount")),1),t("div",Nr,r(((yt=(gt=e.rtktDetailedInfo)==null?void 0:gt.price)==null?void 0:yt.fareAmount)||"-"),1)]),t("div",Cr,[t("div",Fr,[Et(r(e.$t("app.queryRtkt.issueAirlineCode"))+" ",1),t("span",Vr,r(((ft=(bt=e.rtktDetailedInfo)==null?void 0:bt.ticket)==null?void 0:ft.issueAirlineCode)||"-"),1)])]),t("div",Pr,[t("div",Sr,r(e.$t("app.queryRtkt.gpSign")),1),t("div",Er,r(((_t=(ut=e.rtktDetailedInfo)==null?void 0:ut.ticket)==null?void 0:_t.ticketManagementOrganizationCode)??"-")+" "+r(((ht=(kt=e.rtktDetailedInfo)==null?void 0:kt.ticket)==null?void 0:ht.ticketType)!=="NONE"?"-":"")+" "+r((((mt=(vt=e.rtktDetailedInfo)==null?void 0:vt.ticket)==null?void 0:mt.ticketType)==="NONE"?"":(Dt=(Rt=e.rtktDetailedInfo)==null?void 0:Rt.ticket)==null?void 0:Dt.ticketType)??""),1)]),t("div",Br,[Or,t("div",Mr,r((($t=(It=e.rtktDetailedInfo)==null?void 0:It.ticket)==null?void 0:$t.fp)||"-"),1)]),t("div",Qr,[Wr,t("div",Lr,r(((qt=(jt=e.rtktDetailedInfo)==null?void 0:jt.price)==null?void 0:qt.tc)||"-"),1)]),t("div",Kr,[t("div",zr,r(e.$t("app.queryRtkt.creditCard")),1),t("div",Ur,r(((At=(Tt=e.rtktDetailedInfo)==null?void 0:Tt.price)==null?void 0:At.creditCardDetail)||"-"),1)]),t("div",Gr,[t("div",Hr,r(e.$t("app.queryRtkt.originalTicket")),1),t("div",Jr,r(((Nt=(wt=e.rtktDetailedInfo)==null?void 0:wt.ticket)==null?void 0:Nt.originalTicket)||"-"),1)]),t("div",Xr,[t("div",Yr,r(e.$t("app.queryRtkt.detailsOfTaxRefund")),1),t("div",Zr,r(l.value),1)])])])}}});const _s=Qt(ts,[["__scopeId","data-v-ba04ec3d"]]),es=t("i",{class:"iconfont icon-windowed mr-[2px]"},null,-1),ks=Ft({__name:"RtktWindoing",props:{rtktDetailedInfo:{}},emits:["openRtktDetailWindow"],setup(s,{emit:o}){const l=s,e=o,x=Wt(),c=async()=>{e("openRtktDetailWindow");const p=l.rtktDetailedInfo.ticket.ticketNumber.indexOf(l.rtktDetailedInfo.ticket.issueAirlineCode);let i="";p===0&&(i=l.rtktDetailedInfo.ticket.ticketNumber.substring(p+l.rtktDetailedInfo.ticket.issueAirlineCode.length)),i=`${l.rtktDetailedInfo.ticket.issueAirlineCode}-${i}`,await x.delRtktDetailInfoWindowsList(i),x.setRtktDetailInfoWindowsList({...l.rtktDetailedInfo,id:i}),x.closeFastQuery()};return(p,i)=>(g(),y("div",{class:"open-detail-dialog flex items-center text-brand-2 text-[12px] cursor-pointer",onClick:i[0]||(i[0]=b=>c())},[es,t("div",null,r(p.$t("app.fastQuery.windowing")),1)]))}});export{_s as R,ks as _,gs as a,fs as b,ls as c,us as d,ds as e,cs as f,is as g,ps as h,ns as m,os as o,ys as p,bs as q,xs as x};
