package com.swcares.core.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface SwaggerGroup {
    GroupType value();

    @Getter
    @AllArgsConstructor
    enum GroupType {
        /**
         * 全部接口
         */
        DEFAULT("default", "全部接口"),
        /**
         * 业务接口
         */
        BUSINESS("business", "业务接口");
        private final String type;
        private final String display;
    }
}
