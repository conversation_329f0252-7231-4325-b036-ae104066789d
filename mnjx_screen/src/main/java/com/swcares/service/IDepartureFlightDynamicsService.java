package com.swcares.service;

import com.swcares.obj.vo.DepartureFlightDynamicsReqVo;
import com.swcares.obj.vo.DepartureFlightVo;
import com.swcares.obj.page.ScreenPage;


/**
 * <AUTHOR>
 */
public interface IDepartureFlightDynamicsService {

    /**
     * retrieveDepartureFlightDynamics
     * @param departureFlightDynamicsReqVo  departureFlightDynamicsReqVo
     * @return retrieveDepartureFlightDynamics
     */
    ScreenPage<DepartureFlightVo> retrieveDepartureFlightDynamics(DepartureFlightDynamicsReqVo departureFlightDynamicsReqVo);
}
