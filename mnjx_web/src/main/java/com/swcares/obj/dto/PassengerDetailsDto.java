package com.swcares.obj.dto;

import com.swcares.obj.type.EnumCertificateType;
import com.swcares.obj.type.EnumPatType;
import com.swcares.obj.type.EnumPassengerType;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PassengerDetailsDto {
    /**
     * 旅客类型
     */
    private EnumPassengerType passengerType;
    /**
     * 全名（中文）
     */
    private String fullName;
    /**
     * 证件类型(中文)
     */
    private EnumCertificateType certificateType;
    /**
     * 证件号码
     */
    private String certificateNo;
    /**
     * 是否无陪
     */
    private boolean isUm;
    /**
     * 是否VIP
     */
    private boolean isVip;
    /**
     * 军警残
     */
    private EnumPatType officerType;
    /**
     * 特殊服务项
     */
    private String ssrInfo;
}
