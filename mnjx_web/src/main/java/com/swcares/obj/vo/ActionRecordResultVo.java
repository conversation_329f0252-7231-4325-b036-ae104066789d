package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * [操作记录查询返回对象]
 *
 * <AUTHOR>
 */
@Data
@Api(tags = "操作记录查询列表")
public class ActionRecordResultVo {

    @ApiModelProperty(value = "office号")
    private String officeNo;

    @ApiModelProperty(value = "OFFICE号所属机构")
    private String cname;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "计数")
    private Integer countNo;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "创建时间")
    private String minTime;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "创建时间")
    private String maxTime;
}
