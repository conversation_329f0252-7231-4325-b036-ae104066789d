package com.swcares.obj.vo;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 【查询返回的结果对象】
 *
 * <AUTHOR> by yaodan
 * 2021/8/23-11:22
 */
@Data
@Api(tags = "工作号查询返回的结果对象")
public class JobNoVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "工作号")
    private String jobNo;

    @ApiModelProperty(value = "OFFICE号")
    private String officeNo;

    @ApiModelProperty(value = "状态 0 启用 1 停用")
    private String jobNoStatus;

    @ApiModelProperty(value = "级别")
    private String levelCode;

    @ApiModelProperty(value = "姓名")
    private String accountName;

    @ApiModelProperty(value = "角色")
    private String accountRole;

    @ApiModelProperty(value = "机构名称")
    private String organizationName;

    @ApiModelProperty(value = "账号开始日期")
    private String startDate;

    @ApiModelProperty(value = "账号结束日期")
    private String endDate;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "账号与工作号关系ID")
    private String accountJobNoId;

    @ApiModelProperty(value = "账号数据ID，这个是账号表的主键")
    private String accountId;

    @ApiModelProperty(value = "工作号ID")
    private String jobNoId;
}
