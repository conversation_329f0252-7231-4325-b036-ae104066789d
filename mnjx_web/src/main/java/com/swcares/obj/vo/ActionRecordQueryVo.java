package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * [操作记录查询对象]
 *
 * <AUTHOR>
 */
@Data
@Api(tags = "操作记录查询列表")
public class ActionRecordQueryVo {

    @ApiModelProperty(value = "office号")
    private String officeNo;

    @ApiModelProperty(value = "OFFICE号所属机构ID，根据OFFICE_TYPE分别关联到agent表，airline表，airport表")
    private String orgId;

    @ApiModelProperty(value = "OFFICE号所属机构名称")
    private String cname;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "创建时间")
    private String minTime;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @ApiModelProperty(value = "创建时间")
    private String maxTime;
}
