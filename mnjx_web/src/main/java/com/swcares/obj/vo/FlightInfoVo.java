package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: FltInfoVo
 * @Description: 航班综合数据展示
 * @Author: admin
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("航班综合数据展示")
public class FlightInfoVo implements Serializable {
    @ApiModelProperty(value = "航班号")
    private String fltNo;
    @ApiModelProperty(value = "舱位")
    private String cabinInfo;
    @ApiModelProperty(value = "航班日期")
    private String fltDate;
    @ApiModelProperty(value = "城市对")
    private String orgDst;
    @ApiModelProperty(value = "起飞时间")
    private String planOffDate;
    @ApiModelProperty(value = "计划时间")
    private String planOnDate;
    @ApiModelProperty(value = "飞机号")
    private String planeNo;
    @ApiModelProperty(value = "经停数")
    private String stopPoint;
    @ApiModelProperty(value = "餐食",example = "M")
    private String mealCode;
    @ApiModelProperty(value = "控制级别",example = "DS#")
    private String protocalLevel;
    @ApiModelProperty(value = "存在舱位",example = "1")
    private String isExistsCabin;
    private String planFltId;
    @ApiModelProperty(value = "允许ASR标识")
    private String allowAsr;
    @ApiModelProperty(value = "计划航节ID")
    private String planSectionId;
    @ApiModelProperty(value = "共享航班重量")
    private String shareWithFlt;
    @ApiModelProperty(value = "共享航班")
    private String shareFltNo;
    @ApiModelProperty(value = "跨天标识",example = "+1")
    private String nextDay;


}
