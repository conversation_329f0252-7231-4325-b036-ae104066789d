package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxCrew;
import com.swcares.service.ICrewService;
import com.swcares.core.util.ObjectUtils;
import com.swcares.obj.vo.CrewCreateVo;
import com.swcares.obj.vo.CrewQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@Api(tags = "机组人员管理")
@RestController
@RequestMapping("/crew")
public class CrewController {

    @Resource
    private ICrewService iCrewService;

    @ApiOperation("新增机组人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "crewCreateVo",
                    value = "机队管理对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = CrewCreateVo.class
            )
    })
    @PostMapping("/create")
    public String create(@RequestBody @Validated CrewCreateVo crewCreateVo) throws UnifiedResultException, InstantiationException, IllegalAccessException {
        MnjxCrew mnjxCrew = ObjectUtils.vo2Entity(crewCreateVo, MnjxCrew.class);
        boolean isOk = iCrewService.create(mnjxCrew);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("分页查询部门信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    required = true,
                    name = "current",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    required = true,
                    name = "limit",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "查询对象",
                    name = "countryQueryVo",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = CrewQueryVo.class
            )
    })
    @PostMapping("/retrievePageByCond/{current}/{limit}")
    @PageCheck
    public IPage<MnjxCrew> retrievePageByCond(
            @PathVariable long current,
            @PathVariable long limit,
            @RequestBody CrewQueryVo crewQueryVo) {
        return iCrewService.retrievePageByCond(new Page<>(current, limit), crewQueryVo);
    }

    @ApiOperation("根据id获取机组人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "机组人员ID",
                    required = true,
                    name = "id",
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    @PostMapping("/retrieveById/{id}")
    public MnjxCrew retrieveById(@PathVariable String id) {
        return iCrewService.retrieveById(id);
    }

    @ApiOperation("根据乘务组编号获取机组人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "乘务组编号",
                    required = true,
                    name = "crewNo",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class
            )
    })
    @PostMapping("/retrieveByCrewNo/{crewNo}")
    public MnjxCrew retrieveByCrewNo(@PathVariable("crewNo") String crewNo) {
        return iCrewService.retrieveByCrewNo(crewNo);
    }

    @ApiOperation("修改机组人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxCrew",
                    value = "机队对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = MnjxCrew.class
            )
    })
    @PutMapping("/updateById")
    public String updateById(@RequestBody @Validated MnjxCrew mnjxCrew) throws UnifiedResultException {
        return iCrewService.updateById(mnjxCrew);
    }

    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "机组人员ID列表",
                    required = true,
                    name = "ids",
                    allowMultiple = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = List.class
            )
    })
    @DeleteMapping("/deleteIds")
    public String deleteIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean isOk = iCrewService.deleteIds(ids);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}
