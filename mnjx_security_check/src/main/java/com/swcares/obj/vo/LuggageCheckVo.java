package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class LuggageCheckVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private String unpackId;

    @ApiModelProperty("行李id")
    private String luggageId;

    @ApiModelProperty("行李编号")
    private String luggageNo;

    @ApiModelProperty("旅客姓名")
    private String nmName;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("航段号")
    private String pnrSegNo;

    @ApiModelProperty("行李航段")
    private String bagSegNo;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态")
    private String sellCabin;
}
