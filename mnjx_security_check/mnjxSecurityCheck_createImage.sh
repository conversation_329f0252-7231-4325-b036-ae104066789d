#!/bin/bash
chmod 744 ./entrypoint.sh
echo "删除mnjx_security_check镜像"
docker rmi -f $(docker images "*/*/mnjx_security_check" -aq)
echo "使用本地环境打包镜像"
# 使用系统时间作为版本号
currentDateTime=$(date "+%Y%m%d%H%M%S")
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/sts/mnjx_security_check:latest .
docker build -f ./Dockerfile -t harbor.kaiya.com:30443/sts/mnjx_security_check:"${currentDateTime}" .
echo "登录harbor服务器"
docker login --username=admin --password=kaiya@harbor harbor.kaiya.com:30443
echo "推送镜像到远程的harbor"
docker push harbor.kaiya.com:30443/sts/mnjx_security_check:latest
docker push harbor.kaiya.com:30443/sts/mnjx_security_check:"${currentDateTime}"
