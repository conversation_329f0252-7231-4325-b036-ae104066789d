<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.PoMapper">
    <select id="retrievePlanSection" resultType="com.swcares.entity.MnjxPlanFlight">
        select
            mpf.*
        from
            mnjx_flight mf
        left join mnjx_tcard mt on
            mf.flight_id = mt.flight_id
        left join mnjx_plan_flight mpf on
            mt.tcard_id = mpf.tcard_id
        where
            1 = 1
            and mf.flight_no = #{flightNo}
            and mpf.flight_date = #{flightDate}
    </select>
</mapper>