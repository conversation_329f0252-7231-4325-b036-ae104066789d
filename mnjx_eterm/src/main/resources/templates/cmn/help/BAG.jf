HELP INFORMATION FOR BAG: TRANSACTION. APPLICATION GRP IS CKI
THIS FUNCTION IS ALLOWED UNDER THE FOLLOWING USER GROUPS
THE SECURITY GROUP IS : 98
REPRESENTING THE FOLLOWING USER GROUPS: 1-9-88,91
DESCRIPTION: THE TRANSACTION CAN ISSUE BAGGAGE TAGS FOR ONE OR SEVERAL
SURNAMES TO THE REQUIRED OR DEFAULT (FINAL) DESTINATION IN THE
PASSENGER RECORD. THE TAGS ISSUED ARE FOR BAGGAGE (BAG) OR CARGO KENNELS
(AVIH) ITEMS. REI<PERSON><PERSON> BAGGAGE TAGS PREVIOUSLY ISSUED BY CKI.
FORMAT:
   BAG:ENT-NBR,BTAG-DEST    PRODUCE BAGGAGE TAGS.
   BAG:ENT-NBR, R           REISSUE BAGGAGE TAGS.
EXAMPLE:
   BAG:1,<PERSON><PERSON><PERSON><PERSON>        PRODUCE BAGGAGE TAGS AND ITS DESTINATION IS JFX
   BAG:2,R            REISSUE BAGGAGE TAGS OF SECOND ENTRY PASSENGER