package com.swcares.eterm.crs.handler;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.TrfdParamDto;
import com.swcares.eterm.crs.obj.dto.TrfdZdto;
import com.swcares.eterm.crs.service.ITrfdService;

import javax.annotation.Resource;

/**
 * TRFD指令格式验证
 * eg:TRFD AM/1/D
 * TRFD A/1D
 * TRFD M/1/D/退票单号
 * TRFD TM/1/1/票号
 * TRFD H/1/D/退票单号
 * TRFD TH/1/D/票号
 * <p>
 * 全自动退票 指令格式
 * TRFD:Z/13位票号/打票机号  eg:TRFD:Z/7811000000014/1
 *
 * <AUTHOR>
 */
@OperateType(action = "TRFD",template = "/crs/trfd.jf")
public class TrfdHandler implements Handler {

    @Resource
    private ITrfdService iTrfdService;


    /**
     * TRFD自动退票模板
     */
    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        TrfdParamDto trfdParamDto = iTrfdService.parseTrfd(cmd);
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        TrfdZdto trfdZdto = iTrfdService.handle(memoryData,trfdParamDto);
        unifiedResult.setResults(ListUtils.toList(trfdParamDto,trfdZdto).toArray());
        return unifiedResult;
    }
}
