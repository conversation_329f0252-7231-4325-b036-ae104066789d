package com.swcares.eterm.crs.obj.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * TOL 模板返回参数
 *
 * <AUTHOR> by yaodan
 */
@Data
public class TicketLimitsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 起始票号
     */
    private String startTicket;

    /**
     * 结束票号
     */
    private String endTicket;

    /**
     * 总票张数
     */
    private BigInteger totalNumber;

    /**
     * OFFICE ID
     */
    private String officeId;

    /**
     * 发放单位OFFICE号
     */
    private String officeNo;

    /**
     * 票证类型一 I:国际票 D:国内票
     */
    private String ticketTypeOne;

    /**
     * 票证类型二 DC:普通票证 RF:退票 MC:出票方式
     */
    private String ticketTypeTwo;

    /**
     * 票证发放状态票证发放状态 UU：还未发放。 IU：已发放。 UO：使用完。
     */
    private String ticketReleaseStatus;

    /**
     * 发放日期时间(即数据入库时间)
     */
    private Date releaseDate;

    private String useBeginDate;

    /**
     * IATA
     */
    private String agentIata;

    /**
     * PID
     */
    private String siPid;

    /**
     * SI ID
     */
    private String siId;
}
