package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.PrDto;
import com.swcares.eterm.dcs.cki.obj.dto.PrResultDto;
import com.swcares.eterm.dcs.cki.service.IPrService;

import javax.annotation.Resource;
import java.util.List;

/***
 * PR指令的解析，处理
 *
 * <AUTHOR>
 *
 */
@OperateType(action = "PR", shorthand = true, template = "/dcs/cki/pr.jf", fullScreen = true)
public class PrHandler implements Handler {

    @Resource
    private IPrService iPrService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        PrResultDto prResultDto = iPrService.handle(memoryData, cmd);
        List<PrDto> prs = prResultDto.getPrDtos();

        for (PrDto prDto : prs) {
            iPrService.restoreInit(prDto.getPnrNmId(), prDto.getSeat(), prDto.getFlightNo(), prDto.getFlightDate());
        }

        iPrService.putPrCache(memoryData, prResultDto);
        iPrService.savePrForPrint(prs, "PR");

        PrDto prDto = prs.get(0);
        if (prResultDto.isMultiple()) {
            return iPrService.buildResultList(prs, prDto.getFlightNo(), prDto.getFlightDate(), prDto.getOrg(), prResultDto.getParam(), prResultDto.getCabin());
        } else {
            return iPrService.buildResult(prDto, prResultDto.getParam(), prResultDto.getCabin());
        }
    }
}