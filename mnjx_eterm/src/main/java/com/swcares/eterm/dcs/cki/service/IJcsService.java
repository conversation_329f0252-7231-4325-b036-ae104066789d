package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.JcrDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;


/**
 * JCS指令的处理类
 *
 * <AUTHOR>
 */

public interface IJcsService {
    /***
     * 参数解析
     * @param cmd
     * @return
     * @throws UnifiedResultException
     */
    JcrDto parseCmd(String cmd) throws UnifiedResultException;

    /***
     * 业务处理
     * @param jcrDto
     * @return
     * @throws UnifiedResultException
     */
    PaResultDto handle(JcrDto jcrDto) throws UnifiedResultException;
}
