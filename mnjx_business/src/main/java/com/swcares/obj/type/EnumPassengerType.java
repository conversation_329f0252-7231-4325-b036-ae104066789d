package com.swcares.obj.type;

import com.swcares.core.util.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumPassengerType {
    /**
     * 成人
     */
    ADULT("成人", Constant.PASSENGER_TYPE_ADULT),
    /**
     * 儿童
     */
    CHILD("儿童", Constant.PASSENGER_TYPE_CHILD),
    /**
     * 婴儿
     */
    INFANT("婴儿", Constant.PASSENGER_TYPE_INFANT);

    @Getter
    private final String describe;
    @Getter
    private final String code;

    /**
     * 通过中文获取具体的枚举类型
     *
     * @param describe 中文描述
     * @return 具体的枚举类型
     */
    public static EnumPassengerType ofDescribe(String describe) {
        return Arrays.stream(EnumPassengerType.values())
                .filter(passengerTypeEnum -> passengerTypeEnum.getDescribe().equalsIgnoreCase(describe))
                .findAny()
                .orElse(ADULT);
    }

    public static EnumPassengerType ofCode(String code) {
        return Arrays.stream(EnumPassengerType.values())
                .filter(passengerTypeEnum -> passengerTypeEnum.getCode().equalsIgnoreCase(code))
                .findAny()
                .orElse(ADULT);
    }
}
