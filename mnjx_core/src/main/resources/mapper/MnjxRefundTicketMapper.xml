<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxRefundTicketMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxRefundTicket">
        <id column="refund_id" property="refundId"/>
        <result column="refund_no" property="refundNo"/>
        <result column="ticket_no" property="ticketNo"/>
        <result column="collection" property="collection"/>
        <result column="cn_price" property="cnPrice"/>
        <result column="yq_price" property="yqPrice"/>
        <result column="comm" property="comm"/>
        <result column="net_refund" property="netRefund"/>
        <result column="refund_date" property="refundDate"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        refund_id, refund_no,ticket_no,collection, cn_price, yq_price, comm, net_refund, refund_date
    </sql>

</mapper>
