<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrSaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrSa">
        <id column="pnrSaId" property="pnrSaId" />
        <result column="pnr_id" property="pnrId" />
        <result column="org" property="org" />
        <result column="dst" property="dst" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnrSaId, pnr_id, org, dst, input_value
    </sql>

</mapper>
