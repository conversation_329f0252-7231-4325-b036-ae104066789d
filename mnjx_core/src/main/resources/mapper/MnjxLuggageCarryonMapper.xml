<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxLuggageCarryonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxLuggageCarryon">
        <result column="luggage_carryon_id" property="luggageCarryonId"/>
        <result column="pnr_nm_id" property="pnrNmId"/>
        <result column="check_result" property="checkResult"/>
        <result column="remark" property="remark"/>
        <result column="check_time" property="checkTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        luggage_carryon_id, pnr_nm_id, check_result, remark, check_time
    </sql>

</mapper>
