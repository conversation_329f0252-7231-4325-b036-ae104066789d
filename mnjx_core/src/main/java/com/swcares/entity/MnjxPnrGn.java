package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_gn")
public class MnjxPnrGn extends Model<MnjxPnrGn> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_gn_id", type = IdType.ASSIGN_ID)
    private String pnrGnId;

    @ApiModelProperty(value = "PNR ID")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "团队人数")
    @TableField("group_number")
    private Integer groupNumber;

    @ApiModelProperty(value = "已输入姓名的旅客数")
    @TableField("name_number")
    private Integer nameNumber;

    @ApiModelProperty(value = "团队名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.pnrGnId;
    }

}
