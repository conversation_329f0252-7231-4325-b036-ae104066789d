package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_job_execute")
@ApiModel(value="MnjxJobExecute对象", description="")
public class MnjxJobExecute extends Model<MnjxJobExecute> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "execute_id", type = IdType.ASSIGN_ID)
    private String executeId;

    @TableField("job_name")
    private String jobName;

    @TableField("execute_date")
    private String executeDate;

    @Override
    protected Serializable pkVal() {
        return this.executeId;
    }

}
