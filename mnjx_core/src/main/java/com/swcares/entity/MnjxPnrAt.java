package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_at")
public class MnjxPnrAt extends Model<MnjxPnrAt> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_at_id", type = IdType.ASSIGN_ID)
    private String pnrAtId;

    @ApiModelProperty(value = "PNR编号")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "封口编号：001 002 003...")
    @TableField("at_no")
    private String atNo;

    @ApiModelProperty(value = "封口操作者SI工作号ID")
    @TableField("at_si_id")
    private String atSiId;

    @ApiModelProperty(value = "封口时间")
    @TableField("at_date_time")
    private Date atDateTime;

    @ApiModelProperty(value = "封口类型：空，I，IK，前三种为代理人封口，CA为航司封口")
    @TableField("at_type")
    private String atType;

    @ApiModelProperty(value = "本字段专用于航司封口，表示本条记录为XXX编号的代理人封口交互的应答")
    @TableField("ex_at_no")
    private String exAtNo;

    @Override
    protected Serializable pkVal() {
        return this.pnrAtId;
    }

}
