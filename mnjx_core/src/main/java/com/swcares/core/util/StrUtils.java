package com.swcares.core.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Map;

/**
 * <AUTHOR> by yaodan
 * @date 2021/7/9-14:15
 */
public class StrUtils extends StrUtil {
    /**
     * 字符常量：换行符 {@code \r\r}
     */
    public static final String CR2 = "\r\r";
    /**
     * 字符常量：空格符 {@code '  '} 多个空格符
     */
    public static final String SPACE_2 = "  ";

    /**
     * 字符常量：双斜杠 {@code '//'}
     */
    public static final String SLASH_2 = "//";

    /**
     * 字符减号（连接号）
     */
    public static final char C_DASHED = '-';
    /**
     * 数字常量
     */
    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;
    public static final Integer THREE = 3;
    public static final Integer FIFTEEN = 15;
    public static final Integer EIGHTEEN = 18;
    /**
     * 性别标识符 f 女 m 男
     */
    public static final String MALE = "M";
    public static final String FEMALE = "F";
    /**
     * 加号 十字号
     */
    public static final String CROSS = "+";
    /**
     * 分号
     */
    public static final String SEMICOLON = ";";
    public static final String NUMBER_SIGN = "#";


    /**
     * 处理两个字符串类型的 数字相加减成除
     *
     * @param str1 str1
     * @param str2 str2
     * @param type type
     * @return 数字相加减成除
     */
    @Deprecated
    public static String handlerMethmetic4String(String str1, String str2, int type) {
        DecimalFormat df = new DecimalFormat();
        BigDecimal bd1 = new BigDecimal(str1);
        BigDecimal bd2 = new BigDecimal(str2);
        BigDecimal res = null;
        switch (type) {
            //加
            case 1:
                res = NumberUtils.add(bd1, bd2);
                break;
            //减
            case 2:
                res = NumberUtils.sub(bd1, bd2);
                break;
            //乘
            case 3:
                res = NumberUtils.mul(bd1, bd2);
                break;
            //除
            case 4:
                res = NumberUtils.div(bd1, bd2);
                break;
            default:
                break;
        }
        df.applyPattern("0.00");
        return df.format(res);
    }

    /**
     * Title：containChar <br>
     * Description：统计con字符在字符串str中的出现次数 <br>
     *
     * @param str 源字符串
     * @param con 统计字符
     * @return <br>
     */
    public static int containChar(String str, String con) {
        str = str + StrUtil.SPACE;
        return str.split(con).length - 1;
    }

    /**
     * 将浮点数字格式化为字符串
     *
     * @param f       f
     * @param pattern pattern
     * @return 将浮点数字格式化为字符串
     */
    public static String formatNumber(double f, String pattern) {
        java.text.DecimalFormat num = new DecimalFormat(pattern);
        return num.format(f);
    }

    /**
     * Title：vauleIsNullyTo <br>
     * description： map中null value转为"" <br>
     *
     * @param map <br>
     *            author：zhaokan <br>
     *            date：2021/07/29 <br>
     */
    public static void valueIsNullyTo(Map<String, Object> map) {
        for (String key : map.keySet()) {
            Object value = map.get(key);
            if (ObjectUtil.isEmpty(value)) {
                map.put(key, StrUtils.EMPTY);
            }
        }
    }

    public static String objToStr(Object object) {
        return ObjectUtil.isEmpty(object) ? StrUtils.EMPTY : object.toString().trim();
    }

    /**
     * Description:字符串不为空返回true<br>
     *
     * @param str str
     * @return 字符串不为空返回true
     */
    public static boolean strNotEmpty(String str) {
        String reg = "\\s+";
        return str != null && !"".equals(str) && !str.matches(reg);
    }


    /**
     * Description:字符串为空返回true<br>
     *
     * @param str str
     * @return 字符串为空返回true
     */
    public static boolean strIsEmpty(String str) {
        return str == null || "".equals(str);
    }

    /**
     * Title: wrap
     * Description: 返回换行符，提供给enjoy模板主动换行使用
     *
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/5/7 16:14
     */
    public static String wrap() {
        return CRLF;
    }

    /**
     * tsl模板在使用的换行符
     *
     * @return
     */
    public static String lfap() {
        return LF;
    }

    /**
     * Title: fillIndex
     * Description: 序号填充空格，提供给enjoy模板序号显示格式使用
     *
     * @param index
     * @param len
     * @param isPre
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/5/16 17:26
     */
    public static String fillIndex(Integer index, Integer len, Boolean isPre) {
        return StrUtil.fill(String.valueOf(index), ' ', len, isPre);
    }

    /**
     * Title: fillString
     * Description: 字符串填充空格，提供给enjoy模板字符串显示格式使用
     *
     * @param string
     * @param len
     * @param isPre
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/9/08 17:26
     */
    public static String fillString(String string, Integer len, Boolean isPre) {
        String value = String.valueOf(string);
        if (value.matches("[\\u4e00-\\u9fa5]+")) {
            len = len - value.length();
        }
        return StrUtil.fill(value, ' ', len, isPre);
    }

    /**
     * Title: maxLength
     * Description: 限制字符串长度，如果超过指定长度，截取指定长度并在尾部加空格填充，提供给enjoy模板字符串显示格式使用
     *
     * @param string
     * @param maxLength 截取长度
     * @param len       填充后的长度
     * @return
     * <AUTHOR>
     * @date 2022/9/09 11:00
     */
    public static String maxLength(String string, Integer maxLength, Integer len) {
        if (isEmpty(string)) {
            string = " ";
        }
        if (string.length() > maxLength) {
            string = sub(string, 0, maxLength);
        }
        return StrUtil.fill(string, ' ', len, false);
    }

    /**
     * Title: containsAll
     * Description: 判断字符串是否包含后面所输入的条件字符串
     * 例如：输入WRI，条件是W I，返回true
     *
     * @param sourceStr
     * @param testStr
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/8/4 10:06
     */
    public static boolean containsAll(String sourceStr, String... testStr) {
        if (sourceStr.length() < testStr.length) {
            return false;
        }
        int matchCount = 0;
        for (String s : testStr) {
            if (sourceStr.contains(s)) {
                matchCount++;
            }
        }
        return matchCount == testStr.length;
    }

    /**
     * 根据条件判定是否需要添加前缀字符串
     *
     * @param condition 条件 true就添加，false就不添加，原样返回
     * @param content   要被操作的字符串
     * @param prefix    需要添加的前缀
     * @return 操作后的结果字符串
     */
    public static String addPrefixByCond(boolean condition, String content, String prefix) {
        return condition ? StrUtil.format("{}{}", prefix, content) : content;
    }

    /**
     * 在指定的位置插入字符串
     *
     * @param cmd   原始字符串
     * @param str   要插入的字符串
     * @param index 插入的位置
     * @return 改变后的字符串
     */
    public static String insertStrToLocation(String cmd, String str, int index) {
        String header = StrUtil.subPre(cmd, index);
        String tail = StrUtil.subSuf(cmd, index);
        return StrUtil.format("{}{}{}", header, str, tail);
    }

    /**
     * Title: subString
     * Description: 供模板使用截取字符串
     *
     * @param source
     * @param start
     * @param end
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/11/18 17:21
     */
    public static String subString(String source, Integer start, Integer end) {
        return source.substring(start, end);
    }
}
