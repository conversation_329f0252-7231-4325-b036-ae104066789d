package com.swcares.service.impl.new_beiyang;

import com.alibaba.fastjson.JSON;
import com.swcares.service.ILuggageService;
import com.swcares.service.impl.BasePrintServiceImpl;
import com.swcares.vo.LuggageVo;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(prefix = "printer", name = "category", havingValue = "BTP6800X")
public class NewBeiyangLuggageServiceImpl extends BasePrintServiceImpl<LuggageVo> implements ILuggageService {

    @Override
    protected String generatePrintData(LuggageVo vo) {
        return JSON.toJSONString(vo);
    }
}
